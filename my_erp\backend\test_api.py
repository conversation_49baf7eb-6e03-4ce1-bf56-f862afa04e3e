#!/usr/bin/env python3
"""
Test API endpoints to debug the frontend issues
"""
import requests
import json

def test_auth_and_companies():
    """Test authentication and companies endpoint"""
    
    # Test login
    print("=== Testing Login ===")
    login_response = requests.post('http://localhost:8000/api/auth/login/', json={
        'email': '<EMAIL>',
        'password': 'admin123'
    })
    
    print(f"Login Status: {login_response.status_code}")
    print(f"Login Response: {login_response.text}")
    
    if login_response.status_code == 200:
        login_data = login_response.json()
        print(f"Login Data Keys: {list(login_data.keys())}")
        
        # Get token (check different possible keys)
        token = None
        for key in ['access', 'access_token', 'token']:
            if key in login_data:
                token = login_data[key]
                print(f"Found token with key '{key}': {token[:20]}...")
                break
        
        if not token:
            print("No token found in response!")
            return
        
        # Test companies endpoint
        print("\n=== Testing Companies Endpoint ===")
        headers = {'Authorization': f'Token {token}'}
        companies_response = requests.get('http://localhost:8000/api/setup/companies/', headers=headers)
        
        print(f"Companies Status: {companies_response.status_code}")
        print(f"Companies Response: {companies_response.text}")
        
        if companies_response.status_code == 200:
            companies_data = companies_response.json()
            print(f"Companies Data Type: {type(companies_data)}")
            if isinstance(companies_data, dict):
                print(f"Companies Data Keys: {list(companies_data.keys())}")
            elif isinstance(companies_data, list):
                print(f"Companies Count: {len(companies_data)}")
                if companies_data:
                    print(f"First Company Keys: {list(companies_data[0].keys())}")
    
    # Also test without authentication to see the error
    print("\n=== Testing Without Auth ===")
    no_auth_response = requests.get('http://localhost:8000/api/setup/companies/')
    print(f"No Auth Status: {no_auth_response.status_code}")
    print(f"No Auth Response: {no_auth_response.text}")

if __name__ == "__main__":
    test_auth_and_companies()
