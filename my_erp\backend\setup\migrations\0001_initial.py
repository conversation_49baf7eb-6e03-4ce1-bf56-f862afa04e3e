# Generated by Django 4.2.21 on 2025-07-15 22:45

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(max_length=3, unique=True)),
                ('phone_code', models.CharField(blank=True, max_length=10)),
                ('currency_code', models.CharField(blank=True, max_length=3)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Countries',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=3, unique=True)),
                ('symbol', models.CharField(max_length=10)),
                ('decimal_places', models.IntegerField(default=2)),
                ('rounding', models.DecimalField(decimal_places=6, default=0.01, max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Currencies',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SetupTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('business_type', models.CharField(max_length=50)),
                ('template_data', models.TextField(default='{}')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='SystemConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True)),
                ('value', models.TextField()),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Timezone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('offset', models.CharField(max_length=10)),
                ('description', models.CharField(blank=True, max_length=200)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['offset', 'name'],
            },
        ),
        migrations.CreateModel(
            name='CompanySetup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('legal_name', models.CharField(blank=True, max_length=200)),
                ('email', models.EmailField(max_length=254, validators=[django.core.validators.EmailValidator()])),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('website', models.URLField(blank=True)),
                ('street', models.TextField(blank=True)),
                ('street2', models.CharField(blank=True, max_length=200)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('state', models.CharField(blank=True, max_length=100)),
                ('zip', models.CharField(blank=True, max_length=20)),
                ('language', models.CharField(default='en_US', max_length=10)),
                ('date_format', models.CharField(default='DD/MM/YYYY', max_length=20)),
                ('time_format', models.CharField(default='24', max_length=10)),
                ('decimal_precision', models.IntegerField(default=2)),
                ('thousands_separator', models.CharField(default=',', max_length=1)),
                ('decimal_separator', models.CharField(default='.', max_length=1)),
                ('fiscal_year_start', models.DateField(blank=True, null=True)),
                ('fiscal_year_end', models.DateField(blank=True, null=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company_logos/')),
                ('favicon', models.ImageField(blank=True, null=True, upload_to='company_favicons/')),
                ('tax_calculation_rounding_method', models.CharField(choices=[('round_per_line', 'Round per Line'), ('round_globally', 'Round Globally')], default='round_per_line', max_length=20)),
                ('setup_completed', models.BooleanField(default=False)),
                ('setup_progress', models.IntegerField(default=0)),
                ('setup_step', models.CharField(default='company', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('country', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='setup.country')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='setup.currency')),
                ('timezone', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='setup.timezone')),
            ],
            options={
                'verbose_name': 'Company Setup',
                'verbose_name_plural': 'Company Setups',
            },
        ),
        migrations.CreateModel(
            name='TaxConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('tax_type', models.CharField(choices=[('sales', 'Sales Tax'), ('purchase', 'Purchase Tax'), ('both', 'Both')], default='both', max_length=20)),
                ('is_default', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tax_configurations', to='setup.companysetup')),
            ],
            options={
                'unique_together': {('company', 'name')},
            },
        ),
        migrations.CreateModel(
            name='SetupWizardStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('step_name', models.CharField(max_length=50)),
                ('step_order', models.IntegerField()),
                ('is_completed', models.BooleanField(default=False)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('data', models.TextField(blank=True, default='{}')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wizard_steps', to='setup.companysetup')),
            ],
            options={
                'ordering': ['step_order'],
                'unique_together': {('company', 'step_name')},
            },
        ),
        migrations.CreateModel(
            name='PaymentTerm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('days', models.IntegerField(default=0)),
                ('description', models.TextField(blank=True)),
                ('is_default', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_terms', to='setup.companysetup')),
            ],
            options={
                'ordering': ['days'],
                'unique_together': {('company', 'name')},
            },
        ),
        migrations.CreateModel(
            name='BankAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bank_name', models.CharField(max_length=200)),
                ('account_number', models.CharField(max_length=50)),
                ('account_holder_name', models.CharField(max_length=200)),
                ('iban', models.CharField(blank=True, max_length=34)),
                ('swift_code', models.CharField(blank=True, max_length=11)),
                ('branch_name', models.CharField(blank=True, max_length=200)),
                ('branch_code', models.CharField(blank=True, max_length=20)),
                ('is_default', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bank_accounts', to='setup.companysetup')),
            ],
            options={
                'unique_together': {('company', 'account_number')},
            },
        ),
    ]
