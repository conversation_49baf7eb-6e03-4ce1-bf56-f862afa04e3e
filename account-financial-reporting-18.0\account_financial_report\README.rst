.. image:: https://odoo-community.org/readme-banner-image
   :target: https://odoo-community.org/get-involved?utm_source=readme
   :alt: Odoo Community Association

=========================
Account Financial Reports
=========================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:68ab5b6b999486dfe55e3f469815af509179227120d691c89d5e67b636d1ba89
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/license-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Faccount--financial--reporting-lightgray.png?logo=github
    :target: https://github.com/OCA/account-financial-reporting/tree/18.0/account_financial_report
    :alt: OCA/account-financial-reporting
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/account-financial-reporting-18-0/account-financial-reporting-18-0-account_financial_report
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/account-financial-reporting&target_branch=18.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module adds a set of financial reports. They are accessible under
Invoicing / Reporting / OCA accounting reports.

- General ledger
- Trial Balance
- Open Items
- Aged Partner Balance
- VAT Report
- Journal Ledger

Currently General ledger, Trial Balance and Open Items are fully
compatible with a foreign currency set up in account in order to display
balances. Moreover, any foreign currency used in account move lines is
properly shown.

In case that in an account has not been configured a second currency
foreign currency balances are not available.

Invoicing / Settings / Invoicing / OCA Aged Report Configuration you
will be able to set dynamic intervals that will appear on the Aged
Partner Balance. For further information, check CONFIGURE.rst

**Table of contents**

.. contents::
   :local:

Configuration
=============

To configure dynamic intervals for Aged Partner Balance you need to:

Go on 'Settings' -> 'Invoicing' -> 'OCA Aged Report Configuration'.

Click on option 'Configurations' and create new record.

Create new interval. The name established on line will be the column to
display in Aged Partner Balance. Inferior limit established on line is
the interval

Example of configuration inferior limit:

-> 15 -> 30 -> 60

It means the first interval is from 0 to 15, the second from 16 to 30,
and the third is 61+.

Go on 'Invoicing' -> 'Reporting' -> 'OCA accounting reports' -> 'Aged
Partner Balance'

When wizard is open, you need to select your interval configuration and
print report.

If you want to get default interval configuration any time you wish to
print Aged Partner Report, you can set default interval configuration
per company in:

'Settings' -> 'Invoicing' -> 'OCA Aged Report Configuration'.

Known issues / Roadmap
======================

- 'VAT Report' is valid only for cases where it's met that for each Tax
  defined: all the "Account tags" of all the 'Repartition for Invoices'
  or 'Repartition for Credit Notes' are different.
- It would be nice to have in reports a column indicating the state of
  the entries when the option "All Entries" is selected in "Target
  Moves" field in a wizard

Changelog
=========

11.0.2.5.0 (2019-04-26)
-----------------------

- In the Trial Balance you have an option to hide parent hierarchy
  levels

11.0.2.4.1 (2019-01-08)
-----------------------

- Handle better multicompany behaviour
- Improve how title appears in the reports
- Improve performance in General Ledger

11.0.2.3.1 (2018-11-29)
-----------------------

- In the Trial Balance you can apply a filter by hierarchy levels
- In the General Ledger you can apply a filter by Analytic Tag
- In the Journal Ledger the field 'Journal' is now optional

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/account-financial-reporting/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/account-financial-reporting/issues/new?body=module:%20account_financial_report%0Aversion:%2018.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Camptocamp
* initOS GmbH
* redCOR AG
* ForgeFlow

Contributors
------------

- Jordi Ballester <<EMAIL>>
- Yannick Vaucher <<EMAIL>>
- Simone Orsi <<EMAIL>>
- Leonardo Pistone <<EMAIL>>
- Damien Crier <<EMAIL>>
- Andrea Stirpe <<EMAIL>>
- Thomas Rehn <<EMAIL>>
- Andrea Gallina <<EMAIL>>
- Robert Rottermann <<EMAIL>>
- Ciro Urselli <<EMAIL>>
- Francesco Apruzzese <<EMAIL>>
- Lorenzo Battistini <https://github.com/eLBati>
- Julien Coux <<EMAIL>>
- Akim Juillerat <<EMAIL>>
- Alexis de Lattre <<EMAIL>>
- Mihai Fekete <<EMAIL>>
- Miquel Ra??ch <<EMAIL>>
- Joan Sisquella <<EMAIL>>
- `Tecnativa <https://www.tecnativa.com>`__:

  - Pedro M. Baeza
  - Sergio Teruel
  - Ernesto Tejeda
  - João Marques
  - Alexandre D. D??az
  - V??ctor Mart??nez
  - Carolina Fernandez

- `Sygel <https://www.sygel.es>`__:

  - Harald Panten
  - Valentin Vinagre

- Lois Rilo <<EMAIL>>
- Saran Lim. <<EMAIL>>
- Omar Casti??eira <<EMAIL>>
- Chau Le <<EMAIL>>

Much of the work in this module was done at a sprint in Sorrento, Italy
in April 2016.

Other credits
-------------

The migration of this module from 17.0 to 18.0 was financially supported
by Camptocamp.

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/account-financial-reporting <https://github.com/OCA/account-financial-reporting/tree/18.0/account_financial_report>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
