# Generated by Django 4.2.21 on 2025-07-15 19:37

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0003_delete_ircron'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProjectProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('description', models.TextField(blank=True)),
                ('date_start', models.DateField(blank=True, help_text='Start Date', null=True)),
                ('date', models.DateField(blank=True, help_text='Expiration Date', null=True)),
                ('privacy_visibility', models.CharField(choices=[('followers', 'Invited internal users'), ('employees', 'All internal users'), ('portal', 'Invited portal users and all internal users')], default='employees', max_length=16)),
                ('allow_subtasks', models.BooleanField(default=True, help_text='Sub-tasks')),
                ('allow_recurring_tasks', models.BooleanField(default=False, help_text='Recurring Tasks')),
                ('allow_timesheets', models.BooleanField(default=True, help_text='Timesheets')),
                ('timesheet_encode_uom_id', models.IntegerField(blank=True, help_text='Timesheet UoM', null=True)),
                ('rating_active', models.BooleanField(default=False, help_text='Customer Ratings')),
                ('rating_status', models.CharField(choices=[('stage', 'Rating when changing stage'), ('periodic', 'Periodic Rating')], default='stage', max_length=16)),
                ('rating_status_period', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('bimonthly', 'Twice a Month'), ('monthly', 'Once a Month'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly')], default='monthly', max_length=16)),
                ('task_count', models.IntegerField(default=0, help_text='Task Count')),
                ('task_ids_count', models.IntegerField(default=0)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('analytic_account_id', models.ForeignKey(blank=True, help_text='Analytic Account', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountanalyticaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('favorite_user_ids', models.ManyToManyField(blank=True, help_text='Favorite Users', related_name='favorite_projects', to=settings.AUTH_USER_MODEL)),
                ('partner_id', models.ForeignKey(blank=True, help_text='Customer', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('user_id', models.ForeignKey(blank=True, help_text='Project Manager', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'project_project',
            },
        ),
        migrations.CreateModel(
            name='ProjectUpdate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Title', max_length=128)),
                ('status', models.CharField(choices=[('on_track', 'On Track'), ('at_risk', 'At Risk'), ('off_track', 'Off Track'), ('on_hold', 'On Hold')], default='on_track', max_length=16)),
                ('description', models.TextField(help_text='Description')),
                ('progress', models.DecimalField(decimal_places=2, default=0, help_text='Progress (%)', max_digits=5)),
                ('date', models.DateField(default=datetime.date.today)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('project_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='update_ids', to='project.projectproject')),
                ('user_id', models.ForeignKey(help_text='Author', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'project_update',
                'ordering': ['-date', '-create_date'],
            },
        ),
        migrations.CreateModel(
            name='ProjectTaskType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('description', models.TextField(blank=True)),
                ('sequence', models.IntegerField(default=1)),
                ('fold', models.BooleanField(default=False, help_text='Folded in Kanban')),
                ('project_ids', models.ManyToManyField(blank=True, help_text='Projects', to='project.projectproject')),
            ],
            options={
                'db_table': 'project_task_type',
                'ordering': ['sequence', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ProjectTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('priority', models.CharField(choices=[('0', 'Normal'), ('1', 'High')], default='0', max_length=1)),
                ('kanban_state', models.CharField(choices=[('normal', 'In Progress'), ('done', 'Ready'), ('blocked', 'Blocked')], default='normal', max_length=16)),
                ('description', models.TextField(blank=True)),
                ('date_assign', models.DateTimeField(blank=True, help_text='Assigning Date', null=True)),
                ('date_deadline', models.DateField(blank=True, help_text='Deadline', null=True)),
                ('date_last_stage_update', models.DateTimeField(auto_now=True, help_text='Last Stage Update')),
                ('subtask_count', models.IntegerField(default=0)),
                ('planned_hours', models.DecimalField(decimal_places=2, default=0, help_text='Initially Planned Hours', max_digits=8)),
                ('effective_hours', models.DecimalField(decimal_places=2, default=0, help_text='Hours Spent', max_digits=8)),
                ('remaining_hours', models.DecimalField(decimal_places=2, default=0, help_text='Remaining Hours', max_digits=8)),
                ('progress', models.DecimalField(decimal_places=2, default=0, help_text='Progress (%)', max_digits=5)),
                ('overtime', models.DecimalField(decimal_places=2, default=0, help_text='Overtime', max_digits=8)),
                ('tag_ids', models.CharField(blank=True, help_text='Tags', max_length=256)),
                ('email_from', models.EmailField(blank=True, help_text='Email', max_length=254)),
                ('email_cc', models.TextField(blank=True, help_text='Watchers Emails')),
                ('recurring_task', models.BooleanField(default=False, help_text='Recurring Task')),
                ('recurring_count', models.IntegerField(default=0, help_text='Recurrence Count')),
                ('rating_last_value', models.DecimalField(decimal_places=2, default=0, help_text='Last Rating', max_digits=3)),
                ('personal_stage_type_ids', models.CharField(blank=True, help_text='Personal Stage', max_length=256)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('depend_on_ids', models.ManyToManyField(blank=True, help_text='Blocked By', related_name='dependent_ids', to='project.projecttask')),
                ('parent_id', models.ForeignKey(blank=True, help_text='Parent Task', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_ids', to='project.projecttask')),
                ('partner_id', models.ForeignKey(blank=True, help_text='Customer', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('project_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='task_ids', to='project.projectproject')),
                ('stage_id', models.ForeignKey(blank=True, help_text='Stage', null=True, on_delete=django.db.models.deletion.SET_NULL, to='project.projecttasktype')),
                ('timesheet_ids', models.ManyToManyField(blank=True, help_text='Timesheets', to='accounting.accountanalyticline')),
                ('user_ids', models.ManyToManyField(blank=True, help_text='Assignees', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'project_task',
                'ordering': ['sequence', 'id'],
            },
        ),
        migrations.CreateModel(
            name='ProjectMilestone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('deadline', models.DateField(help_text='Deadline')),
                ('is_reached', models.BooleanField(default=False, help_text='Is Reached')),
                ('reached_date', models.DateField(blank=True, help_text='Reached Date', null=True)),
                ('description', models.TextField(blank=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('project_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='milestone_ids', to='project.projectproject')),
                ('task_ids', models.ManyToManyField(blank=True, help_text='Tasks', to='project.projecttask')),
            ],
            options={
                'db_table': 'project_milestone',
                'ordering': ['deadline', 'name'],
            },
        ),
    ]
