import React, { useState, useEffect } from 'react';
import { Table, Button, Typography, Tag, Space } from 'antd';
import { PlusOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Bills = () => {
  const [bills, setBills] = useState([]);
  const [loading, setLoading] = useState(false);

  // Mock data
  const mockBills = [
    {
      id: 1,
      name: 'BILL/2024/001',
      partner_name: 'Office Supplies Co.',
      invoice_date: '2024-01-12',
      invoice_date_due: '2024-02-11',
      amount_total: 15000,
      amount_residual: 15000,
      payment_state: 'not_paid',
      state: 'posted',
    },
    {
      id: 2,
      name: 'BILL/2024/002',
      partner_name: 'Tech Solutions Ltd.',
      invoice_date: '2024-01-08',
      invoice_date_due: '2024-02-07',
      amount_total: 32000,
      amount_residual: 0,
      payment_state: 'paid',
      state: 'posted',
    },
  ];

  const columns = [
    {
      title: 'Bill #',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Vendor',
      dataIndex: 'partner_name',
      key: 'partner_name',
    },
    {
      title: 'Bill Date',
      dataIndex: 'invoice_date',
      key: 'invoice_date',
    },
    {
      title: 'Due Date',
      dataIndex: 'invoice_date_due',
      key: 'invoice_date_due',
    },
    {
      title: 'Total Amount',
      dataIndex: 'amount_total',
      key: 'amount_total',
      render: (amount) => `PKR ${amount.toLocaleString()}`,
      align: 'right',
    },
    {
      title: 'Amount Due',
      dataIndex: 'amount_residual',
      key: 'amount_residual',
      render: (amount) => (
        <span className={amount > 0 ? 'balance-negative' : 'balance-positive'}>
          PKR {amount.toLocaleString()}
        </span>
      ),
      align: 'right',
    },
    {
      title: 'Payment Status',
      dataIndex: 'payment_state',
      key: 'payment_state',
      render: (status) => {
        const colors = {
          not_paid: 'red',
          partial: 'orange',
          paid: 'green',
          in_payment: 'blue',
        };
        const labels = {
          not_paid: 'Not Paid',
          partial: 'Partial',
          paid: 'Paid',
          in_payment: 'In Payment',
        };
        return <Tag color={colors[status]}>{labels[status]}</Tag>;
      },
    },
    {
      title: 'Status',
      dataIndex: 'state',
      key: 'state',
      render: (state) => (
        <Tag color={state === 'posted' ? 'green' : 'orange'}>
          {state === 'posted' ? 'Posted' : 'Draft'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <Space>
          <Button type="text" icon={<EyeOutlined />} size="small" />
          <Button type="text" icon={<EditOutlined />} size="small" />
        </Space>
      ),
    },
  ];

  useEffect(() => {
    setBills(mockBills);
  }, []);

  return (
    <div>
      <div className="page-header">
        <Title level={2}>Vendor Bills</Title>
        <p>Manage vendor bills and expenses</p>
      </div>

      <div className="action-buttons">
        <Button type="primary" icon={<PlusOutlined />}>
          Create Bill
        </Button>
      </div>

      <div className="table-container">
        <Table
          columns={columns}
          dataSource={bills}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} bills`,
          }}
        />
      </div>
    </div>
  );
};

export default Bills;
