/**
 * Income Tax Form Component - Create/Edit Income Tax Form
 */
import React, { useEffect } from 'react';
import { Form, Row, Col } from 'antd';
import FormWrapper from '../../../components/shared/forms/FormWrapper';
import {
  TextField,
  NumberField,
  SelectField,
  DateField,
  SwitchField
} from '../../../components/shared/forms/FormField';

const IncomeTaxForm = ({ 
  initialValues, 
  onSubmit, 
  onCancel, 
  loading = false 
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, form]);

  const handleSubmit = (values) => {
    onSubmit(values);
  };

  // Calculation method options
  const calculationMethodOptions = [
    { value: 'flat', label: 'Flat Rate - Single rate applied to entire income' },
    { value: 'progressive', label: 'Progressive - Rate increases with income brackets' },
    { value: 'slab', label: 'Slab System - Different rates for different income slabs' },
  ];



  return (
    <FormWrapper
      form={form}
      onFinish={handleSubmit}
      layout="vertical"
      initialValues={{
        calculation_method: 'flat',
        min_income: 0,
        standard_deduction: 0,
        personal_exemption: 0,
        advance_tax_required: false,
        quarterly_filing: false,
        is_default: false,
        is_active: true,
        ...initialValues
      }}
    >
      {/* Basic Information */}
      <Row gutter={16}>
        <Col span={12}>
          <TextField
            name="name"
            label="Tax Name"
            required
            placeholder="e.g., Corporate Income Tax"
            help="Descriptive name for this income tax configuration"
          />
        </Col>
        <Col span={12}>
          <NumberField
            name="rate"
            label="Tax Rate (%)"
            required
            min={0}
            max={100}
            precision={2}
            placeholder="e.g., 25.00"
            help="Tax rate as a percentage"
          />
        </Col>
      </Row>

      {/* Tax Year Period */}
      <Row gutter={16}>
        <Col span={12}>
          <DateField
            name="tax_year_start"
            label="Tax Year Start"
            required
            placeholder="Select tax year start date"
            help="Beginning of the tax year"
          />
        </Col>
        <Col span={12}>
          <DateField
            name="tax_year_end"
            label="Tax Year End"
            required
            placeholder="Select tax year end date"
            help="End of the tax year"
          />
        </Col>
      </Row>

      {/* Calculation Method */}
      <Row gutter={16}>
        <Col span={24}>
          <SelectField
            name="calculation_method"
            label="Calculation Method"
            required
            options={calculationMethodOptions}
            placeholder="Select calculation method"
            help="How the tax is calculated based on income"
          />
        </Col>
      </Row>

      {/* Income Brackets */}
      <Row gutter={16}>
        <Col span={12}>
          <NumberField
            name="min_income"
            label="Minimum Income"
            required
            min={0}
            precision={2}
            placeholder="0.00"
            help="Minimum income for this tax bracket"
            addonBefore="$"
          />
        </Col>
        <Col span={12}>
          <NumberField
            name="max_income"
            label="Maximum Income"
            min={0}
            precision={2}
            placeholder="Leave empty for unlimited"
            help="Maximum income for this bracket (empty = unlimited)"
            addonBefore="$"
          />
        </Col>
      </Row>

      {/* Deductions and Exemptions */}
      <Row gutter={16}>
        <Col span={12}>
          <NumberField
            name="standard_deduction"
            label="Standard Deduction"
            min={0}
            precision={2}
            placeholder="0.00"
            help="Standard deduction amount"
            addonBefore="$"
          />
        </Col>
        <Col span={12}>
          <NumberField
            name="personal_exemption"
            label="Personal Exemption"
            min={0}
            precision={2}
            placeholder="0.00"
            help="Personal exemption amount"
            addonBefore="$"
          />
        </Col>
      </Row>

      {/* Filing Requirements */}
      <Row gutter={16}>
        <Col span={8}>
          <SwitchField
            name="advance_tax_required"
            label="Advance Tax Required"
            checkedChildren="Required"
            unCheckedChildren="Not Required"
            help="Whether advance tax payments are required"
          />
        </Col>
        <Col span={8}>
          <SwitchField
            name="quarterly_filing"
            label="Quarterly Filing"
            checkedChildren="Quarterly"
            unCheckedChildren="Annual"
            help="Whether quarterly filing is required"
          />
        </Col>
        <Col span={8}>
          <DateField
            name="annual_filing_deadline"
            label="Annual Filing Deadline"
            placeholder="Select deadline"
            help="Annual tax filing deadline"
          />
        </Col>
      </Row>

      {/* Status Settings */}
      <Row gutter={16}>
        <Col span={12}>
          <SwitchField
            name="is_default"
            label="Default Configuration"
            checkedChildren="Default"
            unCheckedChildren="Not Default"
            help="Set as default income tax configuration"
          />
        </Col>
        <Col span={12}>
          <SwitchField
            name="is_active"
            label="Active"
            checkedChildren="Active"
            unCheckedChildren="Inactive"
            defaultChecked={true}
            help="Whether this configuration is active"
          />
        </Col>
      </Row>
    </FormWrapper>
  );
};

export default IncomeTaxForm;
