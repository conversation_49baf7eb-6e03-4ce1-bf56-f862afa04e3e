/**
 * Withholding Tax Form Component - Create/Edit Withholding Tax (TDS) Form
 */
import React, { useEffect } from 'react';
import { Form, Row, Col } from 'antd';
import FormWrapper from '../../../components/shared/forms/FormWrapper';
import {
  TextField,
  NumberField,
  SelectField,
  DateField,
  SwitchField
} from '../../../components/shared/forms/FormField';

const IncomeTaxForm = ({ 
  initialValues, 
  onSubmit, 
  onCancel, 
  loading = false 
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, form]);

  const handleSubmit = (values) => {
    onSubmit(values);
  };

  // Tax type options
  const taxTypeOptions = [
    { value: 'professional_services', label: 'Professional Services' },
    { value: 'contractor_payments', label: 'Contractor Payments' },
    { value: 'rent_payments', label: 'Rent Payments' },
    { value: 'commission_payments', label: 'Commission Payments' },
    { value: 'interest_payments', label: 'Interest Payments' },
    { value: 'salary_payments', label: 'Salary Payments' },
    { value: 'dividend_payments', label: 'Dividend Payments' },
    { value: 'royalty_payments', label: 'Royalty Payments' },
    { value: 'other', label: 'Other' },
  ];

  // Applicable to options
  const applicableToOptions = [
    { value: 'vendors', label: 'Vendors/Suppliers' },
    { value: 'employees', label: 'Employees' },
    { value: 'customers', label: 'Customers' },
    { value: 'all', label: 'All Parties' },
  ];

  // Calculation base options
  const calculationBaseOptions = [
    { value: 'gross_amount', label: 'Gross Amount' },
    { value: 'net_amount', label: 'Net Amount' },
    { value: 'taxable_amount', label: 'Taxable Amount' },
  ];



  return (
    <FormWrapper
      form={form}
      onFinish={handleSubmit}
      layout="vertical"
      initialValues={{
        tax_type: 'professional_services',
        applicable_to: 'vendors',
        calculation_base: 'gross_amount',
        threshold_amount: 0,
        exemption_limit: 0,
        requires_certificate: false,
        quarterly_return_required: true,
        challan_required: true,
        payment_due_date: 7,
        return_due_date: 31,
        is_default: false,
        is_active: true,
        ...initialValues
      }}
    >
      {/* Basic Information */}
      <Row gutter={16}>
        <Col span={8}>
          <TextField
            name="name"
            label="Tax Name"
            required
            placeholder="e.g., Professional Services TDS"
            help="Descriptive name for this withholding tax"
          />
        </Col>
        <Col span={8}>
          <TextField
            name="code"
            label="Tax Code"
            required
            placeholder="e.g., TDS194J"
            help="Tax code or section reference"
          />
        </Col>
        <Col span={8}>
          <NumberField
            name="rate"
            label="Tax Rate (%)"
            required
            min={0}
            max={100}
            precision={2}
            placeholder="e.g., 10.00"
            help="Withholding tax rate as a percentage"
          />
        </Col>
      </Row>

      {/* Tax Type and Applicability */}
      <Row gutter={16}>
        <Col span={12}>
          <SelectField
            name="tax_type"
            label="Tax Type"
            required
            options={taxTypeOptions}
            placeholder="Select tax type"
            help="Type of payment this withholding tax applies to"
          />
        </Col>
        <Col span={12}>
          <SelectField
            name="applicable_to"
            label="Applicable To"
            required
            options={applicableToOptions}
            placeholder="Select applicable parties"
            help="Who this withholding tax applies to"
          />
        </Col>
      </Row>

      {/* Thresholds and Calculation */}
      <Row gutter={16}>
        <Col span={8}>
          <NumberField
            name="threshold_amount"
            label="Threshold Amount"
            required
            min={0}
            precision={2}
            placeholder="0.00"
            help="Minimum amount above which tax should be deducted"
            addonBefore="$"
          />
        </Col>
        <Col span={8}>
          <NumberField
            name="exemption_limit"
            label="Exemption Limit"
            min={0}
            precision={2}
            placeholder="0.00"
            help="Annual exemption limit for this tax type"
            addonBefore="$"
          />
        </Col>
        <Col span={8}>
          <SelectField
            name="calculation_base"
            label="Calculation Base"
            required
            options={calculationBaseOptions}
            placeholder="Select calculation base"
            help="What amount to calculate tax on"
          />
        </Col>
      </Row>

      {/* Certificate and Compliance */}
      <Row gutter={16}>
        <Col span={8}>
          <SwitchField
            name="requires_certificate"
            label="Requires Certificate"
            checkedChildren="Required"
            unCheckedChildren="Not Required"
            help="Whether TDS certificate is required"
          />
        </Col>
        <Col span={8}>
          <TextField
            name="certificate_series"
            label="Certificate Series"
            placeholder="e.g., A, B, C"
            help="TDS certificate series"
          />
        </Col>
        <Col span={8}>
          <TextField
            name="withholding_account"
            label="Withholding Account"
            placeholder="Account code"
            help="Account code for withholding tax liability"
          />
        </Col>
      </Row>

      {/* Filing and Payment Requirements */}
      <Row gutter={16}>
        <Col span={6}>
          <SwitchField
            name="quarterly_return_required"
            label="Quarterly Return"
            checkedChildren="Required"
            unCheckedChildren="Not Required"
            help="Whether quarterly TDS return filing is required"
          />
        </Col>
        <Col span={6}>
          <SwitchField
            name="challan_required"
            label="Challan Required"
            checkedChildren="Required"
            unCheckedChildren="Not Required"
            help="Whether tax payment through challan is required"
          />
        </Col>
        <Col span={6}>
          <NumberField
            name="payment_due_date"
            label="Payment Due (Days)"
            min={1}
            max={31}
            placeholder="7"
            help="Payment due date (days from month end)"
          />
        </Col>
        <Col span={6}>
          <NumberField
            name="return_due_date"
            label="Return Due (Days)"
            min={1}
            max={90}
            placeholder="31"
            help="Return filing due date (days from quarter end)"
          />
        </Col>
      </Row>

      {/* Effective Period */}
      <Row gutter={16}>
        <Col span={12}>
          <DateField
            name="effective_from"
            label="Effective From"
            required
            placeholder="Select effective from date"
            help="Date from which this configuration is effective"
          />
        </Col>
        <Col span={12}>
          <DateField
            name="effective_to"
            label="Effective To"
            placeholder="Select effective to date (optional)"
            help="Date until which this configuration is effective (leave empty for ongoing)"
          />
        </Col>
      </Row>

      {/* Status Settings */}
      <Row gutter={16}>
        <Col span={12}>
          <SwitchField
            name="is_default"
            label="Default Configuration"
            checkedChildren="Default"
            unCheckedChildren="Not Default"
            help="Set as default income tax configuration"
          />
        </Col>
        <Col span={12}>
          <SwitchField
            name="is_active"
            label="Active"
            checkedChildren="Active"
            unCheckedChildren="Inactive"
            defaultChecked={true}
            help="Whether this configuration is active"
          />
        </Col>
      </Row>
    </FormWrapper>
  );
};

export default IncomeTaxForm;
