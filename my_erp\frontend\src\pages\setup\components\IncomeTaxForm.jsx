/**
 * Income Tax Form Component - Create/Edit Income Tax Form
 */
import React, { useEffect } from 'react';
import { Form, Row, Col } from 'antd';
import FormWrapper from '../../../components/shared/forms/FormWrapper';
import { 
  TextField, 
  TextAreaField, 
  SelectField, 
  SwitchField,
  PercentageField,
  CurrencyField
} from '../../../components/shared/forms/FormField';

const IncomeTaxForm = ({ 
  initialValues, 
  onSubmit, 
  onCancel, 
  loading = false 
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    } else {
      // Set default values for new tax
      form.setFieldsValue({
        active: true,
        tax_type: 'federal'
      });
    }
  }, [initialValues, form]);

  const handleSubmit = (values) => {
    onSubmit(values);
  };

  // Tax type options
  const taxTypeOptions = [
    { value: 'federal', label: 'Federal Tax' },
    { value: 'state', label: 'State Tax' },
    { value: 'local', label: 'Local Tax' },
    { value: 'social_security', label: 'Social Security' },
    { value: 'medicare', label: 'Medicare' },
    { value: 'unemployment', label: 'Unemployment' },
    { value: 'disability', label: 'Disability' },
    { value: 'other', label: 'Other' },
  ];

  // Filing status options
  const filingStatusOptions = [
    { value: 'single', label: 'Single' },
    { value: 'married_jointly', label: 'Married Filing Jointly' },
    { value: 'married_separately', label: 'Married Filing Separately' },
    { value: 'head_of_household', label: 'Head of Household' },
    { value: 'qualifying_widow', label: 'Qualifying Widow(er)' },
  ];

  // Calculation method options
  const calculationMethodOptions = [
    { value: 'percentage', label: 'Percentage of Income' },
    { value: 'bracket', label: 'Tax Bracket' },
    { value: 'flat_rate', label: 'Flat Rate' },
    { value: 'progressive', label: 'Progressive Rate' },
  ];

  return (
    <FormWrapper
      title={null}
      form={form}
      onFinish={handleSubmit}
      onCancel={onCancel}
      loading={loading}
      submitText={initialValues ? 'Update Income Tax' : 'Create Income Tax'}
      showReset={true}
    >
      {/* Basic Information */}
      <Row gutter={16}>
        <Col span={12}>
          <TextField
            name="name"
            label="Tax Name"
            required
            placeholder="Enter tax name (e.g., Federal Income Tax)"
          />
        </Col>
        <Col span={12}>
          <TextField
            name="code"
            label="Tax Code"
            required
            placeholder="Enter tax code (e.g., FIT)"
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <SelectField
            name="tax_type"
            label="Tax Type"
            required
            options={taxTypeOptions}
            placeholder="Select tax type"
          />
        </Col>
        <Col span={12}>
          <SelectField
            name="filing_status"
            label="Filing Status"
            options={filingStatusOptions}
            placeholder="Select filing status (optional)"
          />
        </Col>
      </Row>

      {/* Tax Rate Configuration */}
      <Row gutter={16}>
        <Col span={12}>
          <PercentageField
            name="rate"
            label="Tax Rate"
            required
            placeholder="Enter tax rate"
            min={0}
            max={100}
          />
        </Col>
        <Col span={12}>
          <SelectField
            name="calculation_method"
            label="Calculation Method"
            options={calculationMethodOptions}
            placeholder="Select calculation method"
          />
        </Col>
      </Row>

      {/* Income Brackets */}
      <Row gutter={16}>
        <Col span={12}>
          <CurrencyField
            name="min_income"
            label="Minimum Income"
            placeholder="Enter minimum income for this tax"
            min={0}
          />
        </Col>
        <Col span={12}>
          <CurrencyField
            name="max_income"
            label="Maximum Income"
            placeholder="Enter maximum income (leave empty for no limit)"
            min={0}
          />
        </Col>
      </Row>

      {/* Deductions and Exemptions */}
      <Row gutter={16}>
        <Col span={12}>
          <CurrencyField
            name="standard_deduction"
            label="Standard Deduction"
            placeholder="Enter standard deduction amount"
            min={0}
          />
        </Col>
        <Col span={12}>
          <CurrencyField
            name="personal_exemption"
            label="Personal Exemption"
            placeholder="Enter personal exemption amount"
            min={0}
          />
        </Col>
      </Row>

      {/* Additional Settings */}
      <Row gutter={16}>
        <Col span={12}>
          <TextField
            name="tax_year"
            label="Tax Year"
            placeholder="Enter tax year (e.g., 2024)"
          />
        </Col>
        <Col span={12}>
          <TextField
            name="jurisdiction"
            label="Jurisdiction"
            placeholder="Enter jurisdiction (e.g., California, New York)"
          />
        </Col>
      </Row>

      {/* Status and Notes */}
      <Row gutter={16}>
        <Col span={12}>
          <div style={{ paddingTop: '30px' }}>
            <SwitchField
              name="active"
              label="Active"
              checkedChildren="Active"
              unCheckedChildren="Inactive"
            />
          </div>
        </Col>
        <Col span={12}>
          <div style={{ paddingTop: '30px' }}>
            <SwitchField
              name="mandatory"
              label="Mandatory"
              checkedChildren="Required"
              unCheckedChildren="Optional"
            />
          </div>
        </Col>
      </Row>

      {/* Description */}
      <Row gutter={16}>
        <Col span={24}>
          <TextAreaField
            name="description"
            label="Description"
            placeholder="Enter tax description and notes"
            rows={3}
          />
        </Col>
      </Row>
    </FormWrapper>
  );
};

export default IncomeTaxForm;
