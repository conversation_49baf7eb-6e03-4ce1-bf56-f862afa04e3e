"""
Inventory Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from .models import (
    StockLocation, StockWarehouse, StockPickingType, StockPicking,
    StockMove, StockQuant, StockInventory, StockInventoryLine
)


@admin.register(StockLocation)
class StockLocationAdmin(admin.ModelAdmin):
    list_display = ['name', 'complete_name', 'usage', 'active', 'warehouse_id']
    list_filter = ['usage', 'active', 'company_id', 'warehouse_id']
    search_fields = ['name', 'complete_name', 'barcode']
    ordering = ['complete_name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'complete_name', 'active', 'usage')
        }),
        ('Hierarchy', {
            'fields': ('location_id', 'warehouse_id')
        }),
        ('Settings', {
            'fields': ('barcode', 'removal_strategy_id', 'scrap_location', 'return_location')
        }),
        ('Accounting', {
            'fields': ('valuation_in_account_id', 'valuation_out_account_id'),
            'classes': ('collapse',)
        }),
    )


@admin.register(StockWarehouse)
class StockWarehouseAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'partner_id', 'active', 'delivery_steps', 'reception_steps']
    list_filter = ['active', 'delivery_steps', 'reception_steps', 'company_id']
    search_fields = ['name', 'code']
    ordering = ['sequence', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'active', 'partner_id', 'sequence')
        }),
        ('Main Locations', {
            'fields': ('view_location_id', 'lot_stock_id', 'wh_input_stock_loc_id',
                      'wh_qc_stock_loc_id', 'wh_output_stock_loc_id', 'wh_pack_stock_loc_id')
        }),
        ('Operations', {
            'fields': ('delivery_steps', 'reception_steps')
        }),
        ('Resupply', {
            'fields': ('resupply_wh_ids',),
            'classes': ('collapse',)
        }),
    )

    filter_horizontal = ['resupply_wh_ids']


@admin.register(StockPickingType)
class StockPickingTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'warehouse_id', 'sequence', 'active']
    list_filter = ['code', 'active', 'warehouse_id', 'company_id']
    search_fields = ['name', 'sequence_code']
    ordering = ['sequence', 'name']


class StockMoveInline(admin.TabularInline):
    model = StockMove
    extra = 0
    fields = ['product_id', 'product_uom_qty', 'quantity_done', 'location_id', 'location_dest_id', 'state']
    readonly_fields = ['state']


@admin.register(StockPicking)
class StockPickingAdmin(admin.ModelAdmin):
    list_display = ['name', 'origin', 'partner_id', 'picking_type_id', 'scheduled_date', 'state', 'priority']
    list_filter = ['state', 'priority', 'picking_type_id', 'company_id']
    search_fields = ['name', 'origin', 'partner_id__name']
    inlines = [StockMoveInline]
    ordering = ['-scheduled_date', '-name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'origin', 'picking_type_id', 'state', 'priority')
        }),
        ('Partner & Locations', {
            'fields': ('partner_id', 'location_id', 'location_dest_id')
        }),
        ('Dates', {
            'fields': ('date', 'scheduled_date', 'date_done')
        }),
        ('References', {
            'fields': ('sale_id', 'purchase_id', 'backorder_id', 'user_id'),
            'classes': ('collapse',)
        }),
        ('Notes', {
            'fields': ('note',),
            'classes': ('collapse',)
        }),
    )

    actions = ['action_confirm_pickings', 'action_assign_pickings', 'action_validate_pickings']

    def action_confirm_pickings(self, request, queryset):
        confirmed = 0
        for picking in queryset:
            if picking.action_confirm():
                confirmed += 1
        self.message_user(request, f'{confirmed} pickings confirmed successfully.')
    action_confirm_pickings.short_description = "Confirm selected pickings"

    def action_assign_pickings(self, request, queryset):
        assigned = 0
        for picking in queryset:
            picking.action_assign()
            if picking.state == 'assigned':
                assigned += 1
        self.message_user(request, f'{assigned} pickings assigned successfully.')
    action_assign_pickings.short_description = "Check availability for selected pickings"

    def action_validate_pickings(self, request, queryset):
        validated = 0
        for picking in queryset:
            if picking.button_validate():
                validated += 1
        self.message_user(request, f'{validated} pickings validated successfully.')
    action_validate_pickings.short_description = "Validate selected pickings"


@admin.register(StockMove)
class StockMoveAdmin(admin.ModelAdmin):
    list_display = ['name', 'product_id', 'product_uom_qty', 'quantity_done', 'location_id', 'location_dest_id', 'state', 'date_expected']
    list_filter = ['state', 'procure_method', 'picking_type_id', 'company_id']
    search_fields = ['name', 'product_id__product_tmpl_id__name', 'origin']
    ordering = ['-date_expected', '-name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'product_id', 'state', 'priority')
        }),
        ('Quantities', {
            'fields': ('product_uom_qty', 'reserved_availability', 'availability', 'quantity_done')
        }),
        ('Locations', {
            'fields': ('location_id', 'location_dest_id')
        }),
        ('Dates', {
            'fields': ('date', 'date_expected', 'date_deadline')
        }),
        ('References', {
            'fields': ('picking_id', 'origin', 'partner_id', 'sale_line_id', 'purchase_line_id'),
            'classes': ('collapse',)
        }),
        ('Advanced', {
            'fields': ('procure_method', 'group_id', 'rule_id', 'scrapped'),
            'classes': ('collapse',)
        }),
    )

    actions = ['action_confirm_moves', 'action_assign_moves', 'action_done_moves']

    def action_confirm_moves(self, request, queryset):
        confirmed = 0
        for move in queryset:
            if move.action_confirm():
                confirmed += 1
        self.message_user(request, f'{confirmed} moves confirmed successfully.')
    action_confirm_moves.short_description = "Confirm selected moves"

    def action_assign_moves(self, request, queryset):
        assigned = 0
        for move in queryset:
            if move.check_availability():
                assigned += 1
        self.message_user(request, f'{assigned} moves assigned successfully.')
    action_assign_moves.short_description = "Check availability for selected moves"

    def action_done_moves(self, request, queryset):
        done = 0
        for move in queryset:
            if move.action_done():
                done += 1
        self.message_user(request, f'{done} moves processed successfully.')
    action_done_moves.short_description = "Process selected moves"


@admin.register(StockQuant)
class StockQuantAdmin(admin.ModelAdmin):
    list_display = ['product_id', 'location_id', 'quantity', 'reserved_quantity', 'available_quantity_display', 'owner_id']
    list_filter = ['location_id', 'company_id']
    search_fields = ['product_id__product_tmpl_id__name', 'location_id__name']
    ordering = ['product_id__product_tmpl_id__name', 'location_id__name']

    def available_quantity_display(self, obj):
        available = obj.available_quantity
        color = 'green' if available > 0 else 'red' if available < 0 else 'orange'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.2f}</span>',
            color, available
        )
    available_quantity_display.short_description = 'Available'


class StockInventoryLineInline(admin.TabularInline):
    model = StockInventoryLine
    extra = 0
    fields = ['product_id', 'location_id', 'theoretical_qty', 'product_qty', 'difference_qty']
    readonly_fields = ['difference_qty']


@admin.register(StockInventory)
class StockInventoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'date', 'location_id', 'filter', 'state']
    list_filter = ['state', 'filter', 'company_id']
    search_fields = ['name']
    inlines = [StockInventoryLineInline]
    ordering = ['-date', '-name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'date', 'state')
        }),
        ('Filters', {
            'fields': ('filter', 'location_id', 'product_id', 'lot_id', 'partner_id', 'package_id')
        }),
        ('Accounting', {
            'fields': ('accounting_date',),
            'classes': ('collapse',)
        }),
    )

    actions = ['action_start_inventories', 'action_validate_inventories']

    def action_start_inventories(self, request, queryset):
        started = 0
        for inventory in queryset:
            if inventory.action_start():
                started += 1
        self.message_user(request, f'{started} inventories started successfully.')
    action_start_inventories.short_description = "Start selected inventories"

    def action_validate_inventories(self, request, queryset):
        validated = 0
        for inventory in queryset:
            if inventory.action_validate():
                validated += 1
        self.message_user(request, f'{validated} inventories validated successfully.')
    action_validate_inventories.short_description = "Validate selected inventories"


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - Inventory Module"
