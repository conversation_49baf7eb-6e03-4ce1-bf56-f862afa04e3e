/* Company Setup Styles - Professional ERP Setup Interface */

.company-setup-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.setup-header {
  margin-bottom: 24px;
}

.setup-header h2 {
  color: #262626;
  font-weight: 600;
  margin-bottom: 8px;
}

.setup-progress-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.setup-progress-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.setup-progress-text {
  font-weight: 500;
  color: #262626;
}

.setup-steps-container {
  margin-bottom: 32px;
}

.setup-steps-container .ant-steps {
  margin-bottom: 0;
}

.setup-steps-container .ant-steps-item-title {
  font-weight: 500;
  color: #262626;
}

.setup-steps-container .ant-steps-item-description {
  color: #8c8c8c;
  font-size: 12px;
}

.setup-form-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.setup-form-content {
  padding: 24px;
}

.setup-form-section {
  margin-bottom: 32px;
}

.setup-form-section:last-child {
  margin-bottom: 0;
}

.setup-section-title {
  color: #262626;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.setup-form-row {
  margin-bottom: 16px;
}

.setup-form-row:last-child {
  margin-bottom: 0;
}

.setup-form-item {
  margin-bottom: 16px;
}

.setup-form-item .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.setup-form-item .ant-input,
.setup-form-item .ant-select-selector,
.setup-form-item .ant-input-number {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.setup-form-item .ant-input:focus,
.setup-form-item .ant-select-focused .ant-select-selector,
.setup-form-item .ant-input-number:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.setup-form-item .ant-input:hover,
.setup-form-item .ant-select:hover .ant-select-selector,
.setup-form-item .ant-input-number:hover {
  border-color: #40a9ff;
}

.logo-upload-container {
  text-align: center;
  padding: 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.logo-upload-container:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.logo-upload-container .anticon {
  font-size: 24px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.logo-upload-text {
  color: #8c8c8c;
  font-size: 14px;
}

.logo-preview {
  max-width: 120px;
  max-height: 120px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.setup-navigation {
  padding: 16px 24px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 8px 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.setup-navigation-left {
  display: flex;
  gap: 12px;
}

.setup-navigation-right {
  display: flex;
  gap: 12px;
}

.setup-navigation .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  height: 36px;
  padding: 0 16px;
}

.setup-navigation .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.setup-navigation .ant-btn-primary:hover {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

.setup-review-section {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.setup-review-title {
  color: #262626;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setup-review-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setup-review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.setup-review-label {
  font-weight: 500;
  color: #595959;
  min-width: 120px;
}

.setup-review-value {
  color: #262626;
  text-align: right;
  flex: 1;
}

.setup-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: #fff;
  border-radius: 8px;
}

.setup-loading .ant-spin {
  margin-bottom: 16px;
}

.setup-loading-text {
  color: #8c8c8c;
  font-size: 14px;
}

.setup-alert {
  margin-bottom: 24px;
  border-radius: 8px;
}

.setup-divider {
  margin: 24px 0;
  border-color: #f0f0f0;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .company-setup-container {
    padding: 16px;
  }
  
  .setup-form-content {
    padding: 16px;
  }
  
  .setup-navigation {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
  }
  
  .setup-navigation-left,
  .setup-navigation-right {
    width: 100%;
    justify-content: center;
  }
  
  .setup-review-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .setup-review-label {
    min-width: auto;
  }
  
  .setup-review-value {
    text-align: left;
  }
}

@media (max-width: 480px) {
  .setup-steps-container .ant-steps {
    flex-direction: column;
  }
  
  .setup-steps-container .ant-steps-item {
    padding-bottom: 16px;
  }
  
  .setup-form-row .ant-col {
    margin-bottom: 16px;
  }
}

/* Animation Styles */
.setup-form-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.setup-form-section {
  animation: slideIn 0.4s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Focus and Hover Effects */
.setup-form-item .ant-input:focus,
.setup-form-item .ant-select-focused .ant-select-selector {
  transform: translateY(-1px);
}

.setup-navigation .ant-btn {
  transition: all 0.3s ease;
}

.setup-navigation .ant-btn:hover {
  transform: translateY(-1px);
}

/* Progress Bar Styling */
.setup-progress-content .ant-progress-line {
  flex: 1;
}

.setup-progress-content .ant-progress-text {
  font-weight: 600;
  color: #1890ff;
}

/* Step Icons */
.setup-steps-container .ant-steps-item-icon {
  border-color: #d9d9d9;
  background: #fff;
}

.setup-steps-container .ant-steps-item-process .ant-steps-item-icon {
  border-color: #1890ff;
  background: #1890ff;
}

.setup-steps-container .ant-steps-item-finish .ant-steps-item-icon {
  border-color: #52c41a;
  background: #52c41a;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .company-setup-container {
    background: #141414;
  }
  
  .setup-form-container {
    background: #1f1f1f;
    border-color: #303030;
  }
  
  .setup-header h2,
  .setup-section-title,
  .setup-review-title {
    color: #fff;
  }
  
  .setup-form-item .ant-form-item-label > label {
    color: #fff;
  }
  
  .setup-review-section {
    background: #262626;
  }
}
