# Generated by Django 4.2.21 on 2025-07-15 17:47

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text='Account Name', max_length=128)),
                ('code', models.CharField(db_index=True, help_text='Account Code', max_length=64)),
                ('account_type', models.CharField(choices=[('asset_receivable', 'Receivable'), ('asset_cash', 'Bank and Cash'), ('asset_current', 'Current Assets'), ('asset_non_current', 'Non-current Assets'), ('asset_prepayments', 'Prepayments'), ('asset_fixed', 'Fixed Assets'), ('liability_payable', 'Payable'), ('liability_credit_card', 'Credit Card'), ('liability_current', 'Current Liabilities'), ('liability_non_current', 'Non-current Liabilities'), ('equity', 'Equity'), ('equity_unaffected', 'Current Year Earnings'), ('income', 'Income'), ('income_other', 'Other Income'), ('expense', 'Expenses'), ('expense_depreciation', 'Depreciation'), ('expense_direct_cost', 'Cost of Revenue'), ('off_balance', 'Off-Balance Sheet')], db_index=True, max_length=64)),
                ('reconcile', models.BooleanField(default=False, help_text='Allow reconciliation of journal items')),
                ('deprecated', models.BooleanField(default=False, help_text='Deprecated accounts are hidden by default')),
                ('currency_id', models.CharField(blank=True, help_text='Forces all moves for this account to have this currency', max_length=3)),
                ('note', models.TextField(blank=True, help_text='Internal notes')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'account_account',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='AccountAnalyticAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('code', models.CharField(blank=True, max_length=32)),
                ('active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'account_analytic_account',
            },
        ),
        migrations.CreateModel(
            name='AccountAsset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('code', models.CharField(blank=True, max_length=32)),
                ('value', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('value_residual', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('method_number', models.IntegerField(default=5)),
                ('method_period', models.IntegerField(default=1)),
                ('method_end', models.DateField(blank=True, null=True)),
                ('purchase_date', models.DateField(blank=True, null=True)),
                ('purchase_value', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('open', 'Running'), ('close', 'Close')], default='draft', max_length=8)),
                ('active', models.BooleanField(default=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'account_asset_asset',
            },
        ),
        migrations.CreateModel(
            name='AccountBankStatement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('reference', models.CharField(blank=True, max_length=32)),
                ('date', models.DateField(db_index=True)),
                ('date_done', models.DateTimeField(blank=True, null=True)),
                ('balance_start', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('balance_end_real', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('balance_end', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('state', models.CharField(choices=[('open', 'New'), ('posted', 'Validated')], default='open', max_length=8)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'account_bank_statement',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='AccountBudgetPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('code', models.CharField(blank=True, max_length=64)),
                ('account_ids', models.ManyToManyField(related_name='budget_posts', to='accounting.accountaccount')),
            ],
            options={
                'db_table': 'account_budget_post',
            },
        ),
        migrations.CreateModel(
            name='AccountFiscalPosition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('auto_apply', models.BooleanField(default=False)),
                ('vat_required', models.BooleanField(default=False)),
                ('note', models.TextField(blank=True)),
                ('sequence', models.IntegerField(default=10)),
            ],
            options={
                'db_table': 'account_fiscal_position',
            },
        ),
        migrations.CreateModel(
            name='AccountFullReconcile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
            ],
            options={
                'db_table': 'account_full_reconcile',
            },
        ),
        migrations.CreateModel(
            name='AccountJournal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('code', models.CharField(max_length=5)),
                ('type', models.CharField(choices=[('sale', 'Sales'), ('purchase', 'Purchase'), ('cash', 'Cash'), ('bank', 'Bank'), ('general', 'Miscellaneous')], max_length=8)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10, help_text='Used to order journals in the dashboard')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'account_journal',
                'ordering': ['sequence', 'type', 'code'],
            },
        ),
        migrations.CreateModel(
            name='AccountMove',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='/', max_length=64)),
                ('ref', models.CharField(blank=True, max_length=64)),
                ('date', models.DateField(db_index=True)),
                ('move_type', models.CharField(choices=[('entry', 'Journal Entry'), ('out_invoice', 'Customer Invoice'), ('out_refund', 'Customer Credit Note'), ('in_invoice', 'Vendor Bill'), ('in_refund', 'Vendor Credit Note'), ('out_receipt', 'Sales Receipt'), ('in_receipt', 'Purchase Receipt')], db_index=True, default='entry', max_length=16)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancel', 'Cancelled')], db_index=True, default='draft', max_length=8)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('invoice_date', models.DateField(blank=True, db_index=True, null=True)),
                ('invoice_date_due', models.DateField(blank=True, db_index=True, null=True)),
                ('amount_untaxed', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_residual', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('payment_state', models.CharField(choices=[('not_paid', 'Not Paid'), ('in_payment', 'In Payment'), ('paid', 'Paid'), ('partial', 'Partially Paid'), ('reversed', 'Reversed'), ('invoicing_legacy', 'Invoicing App Legacy')], db_index=True, default='not_paid', max_length=16)),
                ('payment_reference', models.CharField(blank=True, max_length=64)),
                ('narration', models.TextField(blank=True)),
                ('sequence_number', models.IntegerField(default=0)),
                ('sequence_prefix', models.CharField(blank=True, max_length=64)),
                ('auto_post', models.BooleanField(default=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'account_move',
                'ordering': ['-date', '-name', '-invoice_date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='AccountMoveLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Label', max_length=64)),
                ('debit', models.DecimalField(db_index=True, decimal_places=2, default=0, max_digits=16)),
                ('credit', models.DecimalField(db_index=True, decimal_places=2, default=0, max_digits=16)),
                ('balance', models.DecimalField(db_index=True, decimal_places=2, default=0, max_digits=16)),
                ('currency_id', models.CharField(blank=True, max_length=3)),
                ('amount_currency', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('quantity', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('discount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('tax_base_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('reconciled', models.BooleanField(db_index=True, default=False)),
                ('matching_number', models.CharField(blank=True, db_index=True, max_length=32)),
                ('date', models.DateField(db_index=True)),
                ('date_maturity', models.DateField(blank=True, db_index=True, null=True)),
                ('ref', models.CharField(blank=True, max_length=64)),
                ('sequence', models.IntegerField(default=10)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='move_line_ids', to='accounting.accountaccount')),
            ],
            options={
                'db_table': 'account_move_line',
            },
        ),
        migrations.CreateModel(
            name='AccountPaymentTerm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('note', models.TextField(blank=True)),
                ('sequence', models.IntegerField(default=10)),
            ],
            options={
                'db_table': 'account_payment_term',
            },
        ),
        migrations.CreateModel(
            name='AccountTax',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('type_tax_use', models.CharField(choices=[('sale', 'Sales'), ('purchase', 'Purchase'), ('none', 'None')], max_length=8)),
                ('amount_type', models.CharField(choices=[('group', 'Group of Taxes'), ('fixed', 'Fixed'), ('percent', 'Percentage of Price'), ('division', 'Percentage of Price Tax Included')], default='percent', max_length=8)),
                ('amount', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=1)),
                ('description', models.CharField(blank=True, max_length=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'account_tax',
            },
        ),
        migrations.CreateModel(
            name='CrossoveredBudget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('code', models.CharField(blank=True, max_length=16)),
                ('date_from', models.DateField()),
                ('date_to', models.DateField()),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('cancel', 'Cancelled'), ('confirm', 'Confirmed'), ('validate', 'Validated'), ('done', 'Done')], default='draft', max_length=8)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'crossovered_budget',
            },
        ),
        migrations.CreateModel(
            name='MailMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(blank=True, max_length=256)),
                ('date', models.DateTimeField(db_index=True, default=datetime.datetime.now)),
                ('body', models.TextField(blank=True)),
                ('attachment_ids', models.CharField(blank=True, max_length=256)),
                ('message_type', models.CharField(choices=[('email', 'Email'), ('comment', 'Comment'), ('notification', 'System notification'), ('user_notification', 'User notification')], default='email', max_length=32)),
                ('email_from', models.CharField(blank=True, max_length=128)),
                ('reply_to', models.CharField(blank=True, max_length=128)),
                ('model', models.CharField(blank=True, db_index=True, max_length=128)),
                ('res_id', models.IntegerField(blank=True, db_index=True, null=True)),
                ('record_name', models.CharField(blank=True, max_length=128)),
            ],
            options={
                'db_table': 'mail_message',
            },
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('complete_name', models.CharField(blank=True, max_length=256)),
                ('sequence', models.IntegerField(default=10)),
                ('child_id', models.ManyToManyField(blank=True, related_name='parent_categories', to='accounting.productcategory')),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_categories', to='accounting.productcategory')),
                ('property_account_expense_categ_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expense_categories', to='accounting.accountaccount')),
                ('property_account_income_categ_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='income_categories', to='accounting.accountaccount')),
                ('property_stock_account_input_categ_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_input_categories', to='accounting.accountaccount')),
                ('property_stock_account_output_categ_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_output_categories', to='accounting.accountaccount')),
                ('property_stock_valuation_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_valuation_categories', to='accounting.accountaccount')),
            ],
            options={
                'verbose_name_plural': 'Product Categories',
                'db_table': 'product_category',
            },
        ),
        migrations.CreateModel(
            name='ProductProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('default_code', models.CharField(blank=True, help_text='Internal Reference', max_length=64)),
                ('barcode', models.CharField(blank=True, max_length=64, unique=True)),
                ('active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'product_product',
            },
        ),
        migrations.CreateModel(
            name='ResCompany',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('legal_name', models.CharField(blank=True, max_length=200)),
                ('street', models.CharField(blank=True, max_length=128)),
                ('street2', models.CharField(blank=True, max_length=128)),
                ('city', models.CharField(blank=True, max_length=128)),
                ('state_id', models.CharField(blank=True, max_length=128)),
                ('zip', models.CharField(blank=True, max_length=24)),
                ('country_id', models.CharField(default='PK', max_length=2)),
                ('phone', models.CharField(blank=True, max_length=32)),
                ('email', models.CharField(blank=True, max_length=240)),
                ('website', models.CharField(blank=True, max_length=64)),
                ('vat', models.CharField(blank=True, help_text='Tax ID', max_length=32)),
                ('company_registry', models.CharField(blank=True, max_length=64)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('fiscalyear_last_day', models.IntegerField(default=31)),
                ('fiscalyear_last_month', models.IntegerField(default=12)),
                ('period_lock_date', models.DateField(blank=True, null=True)),
                ('fiscalyear_lock_date', models.DateField(blank=True, null=True)),
                ('chart_template_id', models.CharField(blank=True, max_length=64)),
                ('bank_account_code_prefix', models.CharField(default='1014', max_length=32)),
                ('cash_account_code_prefix', models.CharField(default='1011', max_length=32)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Companies',
                'db_table': 'res_company',
            },
        ),
        migrations.CreateModel(
            name='ResCountry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('code', models.CharField(max_length=2, unique=True)),
                ('address_format', models.TextField(blank=True)),
                ('currency_id', models.CharField(blank=True, max_length=3)),
                ('phone_code', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'Countries',
                'db_table': 'res_country',
            },
        ),
        migrations.CreateModel(
            name='ResCurrency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=3, unique=True)),
                ('symbol', models.CharField(max_length=4)),
                ('rate', models.DecimalField(decimal_places=6, default=1.0, max_digits=12)),
                ('rounding', models.DecimalField(decimal_places=6, default=0.01, max_digits=12)),
                ('decimal_places', models.IntegerField(default=2)),
                ('active', models.BooleanField(default=True)),
                ('position', models.CharField(choices=[('after', 'After Amount'), ('before', 'Before Amount')], default='after', max_length=8)),
            ],
            options={
                'verbose_name_plural': 'Currencies',
                'db_table': 'res_currency',
            },
        ),
        migrations.CreateModel(
            name='ResPartner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=128)),
                ('display_name', models.CharField(blank=True, max_length=128)),
                ('ref', models.CharField(blank=True, help_text='Internal Reference', max_length=64)),
                ('is_company', models.BooleanField(default=False)),
                ('street', models.CharField(blank=True, max_length=128)),
                ('street2', models.CharField(blank=True, max_length=128)),
                ('city', models.CharField(blank=True, max_length=128)),
                ('state_id', models.CharField(blank=True, max_length=128)),
                ('zip', models.CharField(blank=True, max_length=24)),
                ('country_id', models.CharField(default='PK', max_length=2)),
                ('phone', models.CharField(blank=True, max_length=32)),
                ('mobile', models.CharField(blank=True, max_length=32)),
                ('email', models.CharField(blank=True, max_length=240)),
                ('website', models.CharField(blank=True, max_length=64)),
                ('function', models.CharField(blank=True, help_text='Job Position', max_length=128)),
                ('title', models.CharField(blank=True, max_length=16)),
                ('customer_rank', models.IntegerField(default=0, help_text='Customer ranking for prioritization')),
                ('supplier_rank', models.IntegerField(default=0, help_text='Vendor ranking for prioritization')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('vat', models.CharField(blank=True, help_text='Tax ID', max_length=32)),
                ('active', models.BooleanField(default=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.respartner')),
            ],
            options={
                'db_table': 'res_partner',
            },
        ),
        migrations.CreateModel(
            name='StockLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('complete_name', models.CharField(blank=True, max_length=256)),
                ('active', models.BooleanField(default=True)),
                ('usage', models.CharField(choices=[('supplier', 'Vendor Location'), ('view', 'View'), ('internal', 'Internal Location'), ('customer', 'Customer Location'), ('inventory', 'Inventory Loss'), ('procurement', 'Procurement'), ('production', 'Production'), ('transit', 'Transit Location')], default='internal', max_length=12)),
                ('child_ids', models.ManyToManyField(blank=True, related_name='parent_locations', to='accounting.stocklocation')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('location_id', models.ForeignKey(blank=True, help_text='Parent Location', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_locations', to='accounting.stocklocation')),
                ('valuation_in_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_valuation_in_locations', to='accounting.accountaccount')),
                ('valuation_out_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_valuation_out_locations', to='accounting.accountaccount')),
            ],
            options={
                'db_table': 'stock_location',
            },
        ),
        migrations.CreateModel(
            name='StockWarehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('code', models.CharField(max_length=5)),
                ('sequence', models.IntegerField(default=10)),
                ('active', models.BooleanField(default=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('lot_stock_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_lot_stock', to='accounting.stocklocation')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('view_location_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_view', to='accounting.stocklocation')),
                ('wh_input_stock_loc_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_input', to='accounting.stocklocation')),
                ('wh_output_stock_loc_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_output', to='accounting.stocklocation')),
                ('wh_pack_stock_loc_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_pack', to='accounting.stocklocation')),
                ('wh_qc_stock_loc_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_qc', to='accounting.stocklocation')),
            ],
            options={
                'db_table': 'stock_warehouse',
            },
        ),
        migrations.CreateModel(
            name='StockMove',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=256)),
                ('sequence', models.IntegerField(default=10)),
                ('priority', models.CharField(default='1', max_length=1)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('date_expected', models.DateTimeField(default=datetime.datetime.now)),
                ('date_deadline', models.DateTimeField(blank=True, null=True)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1, max_digits=16)),
                ('product_uom_qty', models.DecimalField(decimal_places=4, default=1, max_digits=16)),
                ('product_uom', models.IntegerField(default=1)),
                ('state', models.CharField(choices=[('draft', 'New'), ('cancel', 'Cancelled'), ('waiting', 'Waiting Another Move'), ('confirmed', 'Waiting Availability'), ('partially_available', 'Partially Available'), ('assigned', 'Available'), ('done', 'Done')], default='draft', max_length=20)),
                ('origin', models.CharField(blank=True, max_length=64)),
                ('procure_method', models.CharField(choices=[('make_to_stock', 'Take From Stock'), ('make_to_order', 'Create Procurement')], default='make_to_stock', max_length=16)),
                ('sale_line_id', models.IntegerField(blank=True, help_text='Sales Order Line reference', null=True)),
                ('purchase_line_id', models.IntegerField(blank=True, help_text='Purchase Order Line reference', null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_move_ids', models.ManyToManyField(blank=True, help_text='Generated accounting entries', to='accounting.accountmove')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('location_dest_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_move_location_dest_id', to='accounting.stocklocation')),
                ('location_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_move_location_id', to='accounting.stocklocation')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('product_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
            ],
            options={
                'db_table': 'stock_move',
            },
        ),
        migrations.CreateModel(
            name='ResCountryState',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('code', models.CharField(max_length=3)),
                ('country_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescountry')),
            ],
            options={
                'db_table': 'res_country_state',
            },
        ),
        migrations.CreateModel(
            name='ProductTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('sequence', models.IntegerField(default=1)),
                ('description', models.TextField(blank=True)),
                ('description_purchase', models.TextField(blank=True)),
                ('description_sale', models.TextField(blank=True)),
                ('type', models.CharField(choices=[('consu', 'Consumable'), ('service', 'Service'), ('product', 'Storable Product')], default='consu', max_length=8)),
                ('list_price', models.DecimalField(decimal_places=2, default=0, help_text='Sale Price', max_digits=16)),
                ('standard_price', models.DecimalField(decimal_places=2, default=0, help_text='Cost Price', max_digits=16)),
                ('sale_ok', models.BooleanField(default=True)),
                ('purchase_ok', models.BooleanField(default=True)),
                ('tracking', models.CharField(choices=[('none', 'No Tracking'), ('lot', 'By Lots'), ('serial', 'By Unique Serial Number')], default='none', max_length=8)),
                ('active', models.BooleanField(default=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('categ_id', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='accounting.productcategory')),
                ('company_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('property_account_expense_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expense_products', to='accounting.accountaccount')),
                ('property_account_income_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='income_products', to='accounting.accountaccount')),
                ('supplier_taxes_id', models.ManyToManyField(blank=True, help_text='Vendor Taxes', related_name='supplier_products', to='accounting.accounttax')),
                ('taxes_id', models.ManyToManyField(blank=True, help_text='Customer Taxes', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'product_template',
            },
        ),
        migrations.AddField(
            model_name='productproduct',
            name='product_tmpl_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_variant_ids', to='accounting.producttemplate'),
        ),
        migrations.CreateModel(
            name='MailTrackingValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field', models.CharField(max_length=64)),
                ('field_desc', models.CharField(max_length=128)),
                ('field_type', models.CharField(max_length=16)),
                ('old_value_integer', models.IntegerField(blank=True, null=True)),
                ('old_value_float', models.DecimalField(blank=True, decimal_places=2, max_digits=16, null=True)),
                ('old_value_monetary', models.DecimalField(blank=True, decimal_places=2, max_digits=16, null=True)),
                ('old_value_char', models.CharField(blank=True, max_length=256)),
                ('old_value_text', models.TextField(blank=True)),
                ('old_value_datetime', models.DateTimeField(blank=True, null=True)),
                ('new_value_integer', models.IntegerField(blank=True, null=True)),
                ('new_value_float', models.DecimalField(blank=True, decimal_places=2, max_digits=16, null=True)),
                ('new_value_monetary', models.DecimalField(blank=True, decimal_places=2, max_digits=16, null=True)),
                ('new_value_char', models.CharField(blank=True, max_length=256)),
                ('new_value_text', models.TextField(blank=True)),
                ('new_value_datetime', models.DateTimeField(blank=True, null=True)),
                ('mail_message_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.mailmessage')),
            ],
            options={
                'db_table': 'mail_tracking_value',
            },
        ),
        migrations.CreateModel(
            name='MailNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('inbox', 'Inbox'), ('email', 'Email')], default='inbox', max_length=8)),
                ('notification_status', models.CharField(choices=[('ready', 'Ready to Send'), ('sent', 'Sent'), ('bounce', 'Bounced'), ('exception', 'Exception'), ('canceled', 'Canceled')], default='ready', max_length=16)),
                ('is_read', models.BooleanField(default=False)),
                ('read_date', models.DateTimeField(blank=True, null=True)),
                ('failure_type', models.CharField(blank=True, max_length=64)),
                ('failure_reason', models.TextField(blank=True)),
                ('mail_message_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.mailmessage')),
                ('res_partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.respartner')),
            ],
            options={
                'db_table': 'mail_notification',
            },
        ),
        migrations.CreateModel(
            name='MailMessageSubtype',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('description', models.TextField(blank=True)),
                ('internal', models.BooleanField(default=True)),
                ('relation_field', models.CharField(blank=True, max_length=64)),
                ('res_model', models.CharField(blank=True, max_length=64)),
                ('default', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=1)),
                ('hidden', models.BooleanField(default=False)),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.mailmessagesubtype')),
            ],
            options={
                'db_table': 'mail_message_subtype',
            },
        ),
        migrations.AddField(
            model_name='mailmessage',
            name='author_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner'),
        ),
        migrations.AddField(
            model_name='mailmessage',
            name='notification_ids',
            field=models.ManyToManyField(blank=True, to='accounting.mailnotification'),
        ),
        migrations.AddField(
            model_name='mailmessage',
            name='parent_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.mailmessage'),
        ),
        migrations.AddField(
            model_name='mailmessage',
            name='subtype_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.mailmessagesubtype'),
        ),
        migrations.AddField(
            model_name='mailmessage',
            name='tracking_value_ids',
            field=models.ManyToManyField(blank=True, to='accounting.mailtrackingvalue'),
        ),
        migrations.CreateModel(
            name='IrSequence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('code', models.CharField(db_index=True, max_length=64)),
                ('implementation', models.CharField(choices=[('standard', 'Standard'), ('no_gap', 'No Gap')], default='standard', max_length=8)),
                ('active', models.BooleanField(default=True)),
                ('prefix', models.CharField(blank=True, max_length=64)),
                ('suffix', models.CharField(blank=True, max_length=64)),
                ('number_next', models.IntegerField(default=1)),
                ('number_increment', models.IntegerField(default=1)),
                ('padding', models.IntegerField(default=0)),
                ('use_date_range', models.BooleanField(default=False)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'ir_sequence',
            },
        ),
        migrations.CreateModel(
            name='IrCron',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('interval_number', models.IntegerField(default=1)),
                ('interval_type', models.CharField(choices=[('minutes', 'Minutes'), ('hours', 'Hours'), ('days', 'Days'), ('weeks', 'Weeks'), ('months', 'Months')], default='months', max_length=8)),
                ('numbercall', models.IntegerField(default=-1, help_text='Number of calls, -1 for unlimited')),
                ('doall', models.BooleanField(default=True)),
                ('nextcall', models.DateTimeField()),
                ('model_id', models.CharField(max_length=64)),
                ('function', models.CharField(max_length=64)),
                ('args', models.TextField(blank=True)),
                ('priority', models.IntegerField(default=5)),
                ('user_id', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'ir_cron',
            },
        ),
        migrations.CreateModel(
            name='CrossoveredBudgetLines',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_from', models.DateField()),
                ('date_to', models.DateField()),
                ('planned_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('practical_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('theoretical_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('percentage', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('analytic_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountanalyticaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('crossovered_budget_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crossovered_budget_line', to='accounting.crossoveredbudget')),
                ('general_budget_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountbudgetpost')),
            ],
            options={
                'db_table': 'crossovered_budget_lines',
            },
        ),
        migrations.AddField(
            model_name='crossoveredbudget',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.AddField(
            model_name='crossoveredbudget',
            name='user_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='budgets', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='AccountTaxRepartitionLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('factor_percent', models.DecimalField(decimal_places=4, default=100, max_digits=16)),
                ('repartition_type', models.CharField(choices=[('base', 'Base'), ('tax', 'Tax')], default='tax', max_length=8)),
                ('sequence', models.IntegerField(default=1)),
                ('account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('invoice_tax_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='invoice_repartition_lines', to='accounting.accounttax')),
                ('refund_tax_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='refund_repartition_lines', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'account_tax_repartition_line',
            },
        ),
        migrations.AddField(
            model_name='accounttax',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.AddField(
            model_name='accounttax',
            name='invoice_repartition_line_ids',
            field=models.ManyToManyField(blank=True, to='accounting.accounttaxrepartitionline'),
        ),
        migrations.AddField(
            model_name='accounttax',
            name='refund_repartition_line_ids',
            field=models.ManyToManyField(blank=True, related_name='refund_tax_ids', to='accounting.accounttaxrepartitionline'),
        ),
        migrations.CreateModel(
            name='AccountReconcileModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('sequence', models.IntegerField(default=10)),
                ('rule_type', models.CharField(choices=[('writeoff_button', 'Button to generate counterpart entry'), ('writeoff_suggestion', 'Rule to suggest counterpart entry'), ('invoice_matching', 'Rule to match invoices/bills')], default='writeoff_button', max_length=25)),
                ('auto_reconcile', models.BooleanField(default=False)),
                ('to_check', models.BooleanField(default=False)),
                ('match_nature', models.CharField(blank=True, choices=[('amount_received', 'Amount Received'), ('amount_paid', 'Amount Paid')], max_length=16)),
                ('match_amount', models.CharField(blank=True, choices=[('lower', 'Is Lower Than'), ('greater', 'Is Greater Than'), ('between', 'Is Between')], max_length=8)),
                ('match_amount_min', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('match_amount_max', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('match_label', models.CharField(blank=True, choices=[('contains', 'Contains'), ('not_contains', 'Not Contains'), ('match_regex', 'Match Regex')], max_length=16)),
                ('match_label_param', models.CharField(blank=True, max_length=100)),
                ('match_note', models.CharField(blank=True, choices=[('contains', 'Contains'), ('not_contains', 'Not Contains'), ('match_regex', 'Match Regex')], max_length=16)),
                ('match_note_param', models.CharField(blank=True, max_length=100)),
                ('match_transaction_type', models.CharField(blank=True, max_length=100)),
                ('match_same_currency', models.BooleanField(default=True)),
                ('label', models.CharField(blank=True, max_length=64)),
                ('amount_type', models.CharField(choices=[('fixed', 'Fixed'), ('percentage', 'Percentage of balance')], default='percentage', max_length=16)),
                ('amount', models.DecimalField(decimal_places=2, default=100, max_digits=16)),
                ('account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reconcile_models', to='accounting.accountjournal')),
                ('match_journal_ids', models.ManyToManyField(blank=True, related_name='reconcile_models_match', to='accounting.accountjournal')),
                ('tax_ids', models.ManyToManyField(blank=True, related_name='reconcile_models', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'account_reconcile_model',
            },
        ),
        migrations.CreateModel(
            name='AccountPaymentTermLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.CharField(choices=[('balance', 'Balance'), ('percent', 'Percent'), ('fixed', 'Fixed Amount')], default='balance', max_length=8)),
                ('value_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('days', models.IntegerField(default=0)),
                ('sequence', models.IntegerField(default=10)),
                ('payment_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='accounting.accountpaymentterm')),
            ],
            options={
                'db_table': 'account_payment_term_line',
            },
        ),
        migrations.AddField(
            model_name='accountpaymentterm',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.CreateModel(
            name='AccountPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='/', max_length=64)),
                ('payment_type', models.CharField(choices=[('outbound', 'Send Money'), ('inbound', 'Receive Money')], max_length=8)),
                ('partner_type', models.CharField(choices=[('customer', 'Customer'), ('supplier', 'Vendor')], max_length=8)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Validated'), ('sent', 'Sent'), ('reconciled', 'Reconciled'), ('cancelled', 'Cancelled')], default='draft', max_length=16)),
                ('reconciled_invoices_count', models.IntegerField(default=0)),
                ('ref', models.CharField(blank=True, max_length=64)),
                ('date', models.DateField(default=datetime.date.today)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('destination_account_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='accounting.accountaccount')),
                ('journal_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='accounting.accountjournal')),
                ('move_id', models.OneToOneField(help_text='Journal Entry', on_delete=django.db.models.deletion.CASCADE, to='accounting.accountmove')),
                ('outstanding_account_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='outstanding_payments', to='accounting.accountaccount')),
                ('partner_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='accounting.respartner')),
                ('reconciled_invoice_ids', models.ManyToManyField(blank=True, help_text='Invoices reconciled with this payment', related_name='reconciled_payments', to='accounting.accountmove')),
            ],
            options={
                'db_table': 'account_payment',
            },
        ),
        migrations.CreateModel(
            name='AccountPartialReconcile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('amount_currency', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('currency_id', models.CharField(blank=True, max_length=3)),
                ('credit_move_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_partial_reconcile_ids', to='accounting.accountmoveline')),
                ('debit_move_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='debit_partial_reconcile_ids', to='accounting.accountmoveline')),
                ('full_reconcile_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountfullreconcile')),
            ],
            options={
                'db_table': 'account_partial_reconcile',
            },
        ),
        migrations.CreateModel(
            name='AccountMoveLineReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(db_index=True)),
                ('account_type', models.CharField(db_index=True, max_length=64)),
                ('account_code', models.CharField(db_index=True, max_length=64)),
                ('account_name', models.CharField(max_length=128)),
                ('debit', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('credit', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('reconciled', models.BooleanField(db_index=True, default=False)),
                ('account_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
                ('move_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountmove')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
            ],
            options={
                'db_table': 'account_move_line_report',
                'managed': True,
            },
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='full_reconcile_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfullreconcile'),
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='move_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='accounting.accountmove'),
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='partner_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='accounting.respartner'),
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='tax_ids',
            field=models.ManyToManyField(blank=True, related_name='move_line_tax_ids', to='accounting.accounttax'),
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='tax_line_id',
            field=models.ForeignKey(blank=True, help_text='Tax line', null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='tax_line_move_ids', to='accounting.accounttax'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='commercial_partner_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='commercial_moves', to='accounting.respartner'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='create_uid',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_moves', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='journal_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='partner_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='accounting.respartner'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='reversed_entry_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='write_uid',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_moves', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accountjournal',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.AddField(
            model_name='accountjournal',
            name='default_account_id',
            field=models.ForeignKey(help_text='Default account for journal entries', on_delete=django.db.models.deletion.RESTRICT, to='accounting.accountaccount'),
        ),
        migrations.CreateModel(
            name='AccountInvoiceReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(db_index=True)),
                ('invoice_date', models.DateField(blank=True, db_index=True, null=True)),
                ('invoice_date_due', models.DateField(blank=True, db_index=True, null=True)),
                ('country_id', models.CharField(blank=True, db_index=True, max_length=2)),
                ('move_type', models.CharField(db_index=True, max_length=16)),
                ('state', models.CharField(db_index=True, max_length=8)),
                ('currency_id', models.CharField(db_index=True, max_length=3)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_average', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('residual', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('quantity', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountaccount')),
                ('commercial_partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='commercial_invoice_reports', to='accounting.respartner')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('invoice_user_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('journal_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountjournal')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
            ],
            options={
                'db_table': 'account_invoice_report',
                'managed': True,
            },
        ),
        migrations.AddField(
            model_name='accountfullreconcile',
            name='exchange_move_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove'),
        ),
        migrations.AddField(
            model_name='accountfullreconcile',
            name='reconciled_line_ids',
            field=models.ManyToManyField(related_name='full_reconcile_ids', to='accounting.accountmoveline'),
        ),
        migrations.CreateModel(
            name='AccountFiscalPositionTax',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('position_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tax_ids', to='accounting.accountfiscalposition')),
                ('tax_dest_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='fiscal_position_taxes_dest', to='accounting.accounttax')),
                ('tax_src_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fiscal_position_taxes_src', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'account_fiscal_position_tax',
            },
        ),
        migrations.CreateModel(
            name='AccountFiscalPositionAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_dest_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fiscal_position_accounts_dest', to='accounting.accountaccount')),
                ('account_src_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fiscal_position_accounts_src', to='accounting.accountaccount')),
                ('position_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='account_ids', to='accounting.accountfiscalposition')),
            ],
            options={
                'db_table': 'account_fiscal_position_account',
            },
        ),
        migrations.AddField(
            model_name='accountfiscalposition',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.CreateModel(
            name='AccountFinancialReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('sequence', models.IntegerField(default=10)),
                ('level', models.IntegerField(default=0)),
                ('type', models.CharField(choices=[('sum', 'View'), ('accounts', 'Accounts'), ('account_type', 'Account Type'), ('account_report', 'Report Value')], default='sum', max_length=16)),
                ('account_type_ids', models.CharField(blank=True, max_length=256)),
                ('sign', models.IntegerField(choices=[(1, 'Positive'), (-1, 'Negative')], default=1)),
                ('display_detail', models.CharField(choices=[('no_detail', 'No detail'), ('detail_flat', 'Display children flat'), ('detail_with_hierarchy', 'Display children with hierarchy')], default='detail_flat', max_length=32)),
                ('style_overwrite', models.BooleanField(default=False)),
                ('account_ids', models.ManyToManyField(blank=True, to='accounting.accountaccount')),
                ('account_report_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='report_references', to='accounting.accountfinancialreport')),
                ('children_ids', models.ManyToManyField(blank=True, related_name='parent_reports', to='accounting.accountfinancialreport')),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children_reports', to='accounting.accountfinancialreport')),
            ],
            options={
                'db_table': 'account_financial_report',
            },
        ),
        migrations.AddField(
            model_name='accountbudgetpost',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.CreateModel(
            name='AccountBankStatementLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sequence', models.IntegerField(default=1)),
                ('date', models.DateField(db_index=True)),
                ('name', models.CharField(max_length=64)),
                ('ref', models.CharField(blank=True, max_length=32)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('account_number', models.CharField(blank=True, max_length=32)),
                ('partner_name', models.CharField(blank=True, max_length=128)),
                ('transaction_type', models.CharField(blank=True, max_length=32)),
                ('is_reconciled', models.BooleanField(default=False)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('move_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('statement_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='accounting.accountbankstatement')),
            ],
            options={
                'db_table': 'account_bank_statement_line',
            },
        ),
        migrations.AddField(
            model_name='accountbankstatement',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.AddField(
            model_name='accountbankstatement',
            name='journal_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal'),
        ),
        migrations.CreateModel(
            name='AccountAutomaticEntryWizard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('change_period', 'Change Period'), ('change_account', 'Change Account'), ('reverse', 'Reverse')], max_length=16)),
                ('date', models.DateField()),
                ('account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
                ('move_ids', models.ManyToManyField(help_text='Moves to process', to='accounting.accountmove')),
            ],
            options={
                'db_table': 'account_automatic_entry_wizard',
            },
        ),
        migrations.CreateModel(
            name='AccountAssetDepreciationLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('sequence', models.IntegerField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('remaining_value', models.DecimalField(decimal_places=2, max_digits=16)),
                ('depreciated_value', models.DecimalField(decimal_places=2, max_digits=16)),
                ('depreciation_date', models.DateField(db_index=True)),
                ('move_posted', models.BooleanField(default=False)),
                ('asset_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='depreciation_line_ids', to='accounting.accountasset')),
                ('move_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove')),
            ],
            options={
                'db_table': 'account_asset_depreciation_line',
            },
        ),
        migrations.CreateModel(
            name='AccountAssetCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('type', models.CharField(choices=[('sale', 'Deferred Revenue'), ('purchase', 'Deferred Expense'), ('expense', 'Expense')], default='purchase', max_length=8)),
                ('method', models.CharField(choices=[('linear', 'Linear'), ('degressive', 'Degressive'), ('degressive_then_linear', 'Degressive then Linear')], default='linear', max_length=25)),
                ('method_number', models.IntegerField(default=5, help_text='Number of depreciations')),
                ('method_period', models.IntegerField(default=1, help_text='Period length')),
                ('method_progress_factor', models.DecimalField(decimal_places=2, default=0.3, max_digits=4)),
                ('prorata', models.BooleanField(default=True)),
                ('active', models.BooleanField(default=True)),
                ('account_asset_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='asset_categories', to='accounting.accountaccount')),
                ('account_depreciation_expense_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='asset_expense_categories', to='accounting.accountaccount')),
                ('account_depreciation_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='depreciation_categories', to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
            ],
            options={
                'db_table': 'account_asset_category',
            },
        ),
        migrations.AddField(
            model_name='accountasset',
            name='category_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountassetcategory'),
        ),
        migrations.AddField(
            model_name='accountasset',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.AddField(
            model_name='accountasset',
            name='invoice_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove'),
        ),
        migrations.AddField(
            model_name='accountasset',
            name='partner_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner'),
        ),
        migrations.CreateModel(
            name='AccountAnalyticLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=256)),
                ('date', models.DateField(db_index=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('unit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('account_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='accounting.accountanalyticaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('general_account_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='accounting.accountaccount')),
                ('move_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountmove')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
            ],
            options={
                'db_table': 'account_analytic_line',
            },
        ),
        migrations.AddField(
            model_name='accountanalyticaccount',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.AddField(
            model_name='accountanalyticaccount',
            name='partner_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner'),
        ),
        migrations.CreateModel(
            name='AccountAgedTrialBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_date', models.DateField(db_index=True)),
                ('not_due', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('period_1', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('period_2', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('period_3', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('period_4', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('period_5', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('account_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
            ],
            options={
                'db_table': 'account_aged_trial_balance',
                'managed': True,
            },
        ),
        migrations.AddField(
            model_name='accountaccount',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.AddIndex(
            model_name='respartner',
            index=models.Index(fields=['name'], name='res_partner_name_0c4920_idx'),
        ),
        migrations.AddIndex(
            model_name='respartner',
            index=models.Index(fields=['customer_rank'], name='res_partner_custome_9a0c0e_idx'),
        ),
        migrations.AddIndex(
            model_name='respartner',
            index=models.Index(fields=['supplier_rank'], name='res_partner_supplie_c6dab8_idx'),
        ),
        migrations.AddIndex(
            model_name='mailmessage',
            index=models.Index(fields=['model', 'res_id'], name='mail_messag_model_4f1aa2_idx'),
        ),
        migrations.AddIndex(
            model_name='mailmessage',
            index=models.Index(fields=['date'], name='mail_messag_date_4319bc_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='irsequence',
            unique_together={('code', 'company_id')},
        ),
        migrations.AddIndex(
            model_name='accountpayment',
            index=models.Index(fields=['partner_id', 'state'], name='account_pay_partner_41ca57_idx'),
        ),
        migrations.AddIndex(
            model_name='accountpayment',
            index=models.Index(fields=['date', 'journal_id'], name='account_pay_date_fc7baf_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['account_id', 'date'], name='account_mov_account_eeaeb4_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['partner_id', 'account_id'], name='account_mov_partner_6a4ecd_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['reconciled', 'account_id'], name='account_mov_reconci_5c6b3e_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['date', 'move_id'], name='account_mov_date_ed03df_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['date', 'journal_id'], name='account_mov_date_586ef6_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['state', 'move_type'], name='account_mov_state_c8b985_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['partner_id', 'state'], name='account_mov_partner_6c4f1b_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='accountjournal',
            unique_together={('code', 'company_id')},
        ),
        migrations.AddIndex(
            model_name='accountaccount',
            index=models.Index(fields=['code', 'company_id'], name='account_acc_code_5f9be8_idx'),
        ),
        migrations.AddIndex(
            model_name='accountaccount',
            index=models.Index(fields=['account_type'], name='account_acc_account_9c1489_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='accountaccount',
            unique_together={('code', 'company_id')},
        ),
    ]
