"""
Manufacturing Module Models - Complete Odoo MRP (Manufacturing Resource Planning)
Based on Odoo's Manufacturing Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime, timedelta

# Import models from other modules for integration
from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountAnalyticAccount, ProductCategory, ProductTemplate, ProductProduct
)
from inventory.models import StockLocation, StockWarehouse, StockPicking, StockMove, StockQuant


class MrpWorkcenter(models.Model):
    """Work Center - Based on Odoo mrp.workcenter"""

    name = models.CharField(max_length=128)
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=1)
    color = models.IntegerField(default=0, help_text="Color Index")

    # Description
    note = models.TextField(blank=True, help_text="Description")

    # Capacity and Time
    capacity = models.DecimalField(max_digits=8, decimal_places=2, default=1, help_text="Working Capacity")
    time_efficiency = models.DecimalField(max_digits=5, decimal_places=2, default=100, help_text="Time Efficiency (%)")

    # Costs
    costs_hour = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Cost per Hour")
    time_start = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Setup Time (minutes)")
    time_stop = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Cleanup Time (minutes)")

    # Location
    default_capacity = models.DecimalField(max_digits=8, decimal_places=2, default=1, help_text="Default Capacity")

    # Resource Calendar
    resource_calendar_id = models.IntegerField(null=True, blank=True, help_text="Working Hours")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'mrp_workcenter'

    def __str__(self):
        return self.name


class MrpRoutingWorkcenter(models.Model):
    """Routing Work Center - Based on Odoo mrp.routing.workcenter"""

    name = models.CharField(max_length=128)
    sequence = models.IntegerField(default=1)

    # Routing
    routing_id = models.ForeignKey('MrpRouting', on_delete=models.CASCADE, related_name='operation_ids')
    workcenter_id = models.ForeignKey(MrpWorkcenter, on_delete=models.CASCADE)

    # Time
    time_mode = models.CharField(max_length=16, choices=[('auto', 'Compute based on tracked time'), ('manual', 'Set duration manually')], default='auto')
    time_mode_batch = models.IntegerField(default=10, help_text="Based on")
    time_cycle_manual = models.DecimalField(max_digits=8, decimal_places=2, default=60, help_text="Manual Duration (minutes)")
    time_cycle = models.DecimalField(max_digits=8, decimal_places=2, default=60, help_text="Duration (minutes)")

    # Batch
    batch = models.CharField(max_length=16, choices=[('no', 'Once all products are processed'), ('yes', 'Once some products are processed')], default='no')
    batch_size = models.DecimalField(max_digits=8, decimal_places=2, default=1, help_text="Batch Size")

    # Description
    note = models.TextField(blank=True, help_text="Description")

    # Worksheet
    worksheet_type = models.CharField(max_length=16, choices=[('pdf', 'PDF'), ('google_slide', 'Google Slide'), ('text', 'Text')], default='pdf')
    worksheet = models.TextField(blank=True, help_text="Worksheet")

    class Meta:
        db_table = 'mrp_routing_workcenter'
        ordering = ['sequence']

    def __str__(self):
        return f"{self.routing_id.name} - {self.name}"


class MrpRouting(models.Model):
    """Routing - Based on Odoo mrp.routing"""

    name = models.CharField(max_length=128)
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=1)

    # Description
    note = models.TextField(blank=True, help_text="Description")

    # Location
    location_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, help_text="Production Location")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'mrp_routing'

    def __str__(self):
        return self.name


class MrpBom(models.Model):
    """Bill of Materials - Based on Odoo mrp.bom"""

    BOM_TYPES = [
        ('normal', 'Manufacture this product'),
        ('phantom', 'Kit'),
        ('subcontract', 'Subcontracting'),
    ]

    # Basic Information
    product_tmpl_id = models.ForeignKey(ProductTemplate, on_delete=models.CASCADE, help_text="Product")
    product_id = models.ForeignKey(ProductProduct, null=True, blank=True, on_delete=models.CASCADE, help_text="Product Variant")

    # BOM Configuration
    product_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1, help_text="Quantity")
    product_uom_id = models.IntegerField(default=1, help_text="Unit of Measure")

    # Type and Routing
    type = models.CharField(max_length=16, choices=BOM_TYPES, default='normal')
    routing_id = models.ForeignKey(MrpRouting, null=True, blank=True, on_delete=models.SET_NULL, help_text="Routing")

    # Sequence and Active
    sequence = models.IntegerField(default=1)
    active = models.BooleanField(default=True)

    # Locations
    picking_type_id = models.IntegerField(null=True, blank=True, help_text="Operation Type")

    # Consumption
    consumption = models.CharField(max_length=16, choices=[('flexible', 'Allowed'), ('warning', 'Allowed with warning'), ('strict', 'Blocked')], default='flexible')

    # Ready to Produce
    ready_to_produce = models.CharField(max_length=16, choices=[('all_available', 'When all components are available'), ('asap', 'When components for 1st operation are available')], default='asap')

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'mrp_bom'

    def __str__(self):
        return f"BoM {self.product_tmpl_id.name}"

    @property
    def display_name(self):
        """Display name for BOM"""
        if self.product_id:
            return f"{self.product_id.name}"
        return f"{self.product_tmpl_id.name}"


class MrpBomLine(models.Model):
    """Bill of Materials Line - Based on Odoo mrp.bom.line"""

    # BOM Reference
    bom_id = models.ForeignKey(MrpBom, on_delete=models.CASCADE, related_name='bom_line_ids')

    # Product
    product_id = models.ForeignKey(ProductProduct, on_delete=models.CASCADE, help_text="Component")

    # Quantity
    product_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1, help_text="Quantity")
    product_uom_id = models.IntegerField(default=1, help_text="Unit of Measure")

    # Sequence
    sequence = models.IntegerField(default=1)

    # Operation
    operation_id = models.ForeignKey(MrpRoutingWorkcenter, null=True, blank=True, on_delete=models.SET_NULL, help_text="Consumed in Operation")

    # Manual Consumption
    manual_consumption = models.BooleanField(default=False, help_text="Manual Consumption")

    class Meta:
        db_table = 'mrp_bom_line'
        ordering = ['sequence']

    def __str__(self):
        return f"{self.bom_id.display_name} - {self.product_id.name}"


class MrpProduction(models.Model):
    """Manufacturing Order - Based on Odoo mrp.production"""

    STATES = [
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('progress', 'In Progress'),
        ('to_close', 'To Close'),
        ('done', 'Done'),
        ('cancel', 'Cancelled'),
    ]

    PRIORITIES = [
        ('0', 'Not urgent'),
        ('1', 'Normal'),
        ('2', 'Urgent'),
        ('3', 'Very Urgent'),
    ]

    # Basic Information
    name = models.CharField(max_length=64, unique=True, help_text="Reference")
    origin = models.CharField(max_length=64, blank=True, help_text="Source Document")

    # Product
    product_id = models.ForeignKey(ProductProduct, on_delete=models.CASCADE, help_text="Product to Produce")
    product_tmpl_id = models.ForeignKey(ProductTemplate, on_delete=models.CASCADE, help_text="Product Template")

    # Quantity
    product_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1, help_text="Quantity to Produce")
    product_uom_id = models.IntegerField(default=1, help_text="Unit of Measure")
    qty_producing = models.DecimalField(max_digits=16, decimal_places=4, default=0, help_text="Currently Produced Quantity")
    qty_produced = models.DecimalField(max_digits=16, decimal_places=4, default=0, help_text="Produced Quantity")

    # BOM and Routing
    bom_id = models.ForeignKey(MrpBom, null=True, blank=True, on_delete=models.SET_NULL, help_text="Bill of Materials")
    routing_id = models.ForeignKey(MrpRouting, null=True, blank=True, on_delete=models.SET_NULL, help_text="Routing")

    # Dates
    date_planned_start = models.DateTimeField(help_text="Scheduled Start Date")
    date_planned_finished = models.DateTimeField(help_text="Scheduled End Date")
    date_start = models.DateTimeField(null=True, blank=True, help_text="Start Date")
    date_finished = models.DateTimeField(null=True, blank=True, help_text="End Date")
    date_deadline = models.DateTimeField(null=True, blank=True, help_text="Deadline")

    # State and Priority
    state = models.CharField(max_length=16, choices=STATES, default='draft')
    priority = models.CharField(max_length=1, choices=PRIORITIES, default='1')

    # Locations
    location_src_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, related_name='mrp_production_src', help_text="Components Location")
    location_dest_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, related_name='mrp_production_dest', help_text="Finished Products Location")
    picking_type_id = models.IntegerField(help_text="Operation Type")

    # User and Company
    user_id = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, help_text="Responsible")
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Procurement Group
    procurement_group_id = models.IntegerField(null=True, blank=True, help_text="Procurement Group")

    # Analytic Account
    analytic_account_id = models.ForeignKey(AccountAnalyticAccount, null=True, blank=True, on_delete=models.SET_NULL, help_text="Analytic Account")

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'mrp_production'
        ordering = ['-date_planned_start']

    def __str__(self):
        return f"{self.name} - {self.product_id.name}"

    def action_confirm(self):
        """Confirm manufacturing order"""
        if self.state == 'draft':
            self.state = 'confirmed'
            self.save()
            self._create_stock_moves()
            return True
        return False

    def action_assign(self):
        """Reserve components"""
        if self.state == 'confirmed':
            # Logic to reserve components would go here
            return True
        return False

    def button_plan(self):
        """Plan manufacturing order"""
        if self.state in ['confirmed', 'progress']:
            # Logic to plan production would go here
            return True
        return False

    def button_mark_done(self):
        """Mark manufacturing order as done"""
        if self.state in ['progress', 'to_close']:
            self.state = 'done'
            self.date_finished = datetime.now()
            self.qty_produced = self.product_qty
            self.save()
            return True
        return False

    def action_cancel(self):
        """Cancel manufacturing order"""
        if self.state not in ['done', 'cancel']:
            self.state = 'cancel'
            self.save()
            return True
        return False

    def _create_stock_moves(self):
        """Create stock moves for components and finished product"""
        # This would create stock moves for the manufacturing process
        pass


class MrpWorkorder(models.Model):
    """Work Order - Based on Odoo mrp.workorder"""

    STATES = [
        ('pending', 'Waiting for another WO'),
        ('waiting', 'Waiting for components'),
        ('ready', 'Ready'),
        ('progress', 'In Progress'),
        ('done', 'Finished'),
        ('cancel', 'Cancelled'),
    ]

    # Basic Information
    name = models.CharField(max_length=128)

    # Production Order
    production_id = models.ForeignKey(MrpProduction, on_delete=models.CASCADE, related_name='workorder_ids')

    # Work Center and Operation
    workcenter_id = models.ForeignKey(MrpWorkcenter, on_delete=models.CASCADE)
    operation_id = models.ForeignKey(MrpRoutingWorkcenter, null=True, blank=True, on_delete=models.SET_NULL)

    # Product
    product_id = models.ForeignKey(ProductProduct, on_delete=models.CASCADE)

    # Quantity
    qty_production = models.DecimalField(max_digits=16, decimal_places=4, default=1, help_text="Original Production Quantity")
    qty_produced = models.DecimalField(max_digits=16, decimal_places=4, default=0, help_text="Produced Quantity")
    qty_producing = models.DecimalField(max_digits=16, decimal_places=4, default=0, help_text="Currently Produced Quantity")

    # State and Sequence
    state = models.CharField(max_length=16, choices=STATES, default='pending')
    sequence = models.IntegerField(default=1)

    # Dates
    date_planned_start = models.DateTimeField(help_text="Scheduled Start Date")
    date_planned_finished = models.DateTimeField(help_text="Scheduled End Date")
    date_start = models.DateTimeField(null=True, blank=True, help_text="Start Date")
    date_finished = models.DateTimeField(null=True, blank=True, help_text="End Date")

    # Duration
    duration_expected = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Expected Duration (minutes)")
    duration = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Real Duration (minutes)")

    # Costs
    costs_hour = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Cost per Hour")

    # Next Work Order
    next_work_order_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL, help_text="Next Work Order")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'mrp_workorder'
        ordering = ['sequence']

    def __str__(self):
        return f"{self.production_id.name} - {self.name}"

    def button_start(self):
        """Start work order"""
        if self.state == 'ready':
            self.state = 'progress'
            self.date_start = datetime.now()
            self.save()
            return True
        return False

    def button_finish(self):
        """Finish work order"""
        if self.state == 'progress':
            self.state = 'done'
            self.date_finished = datetime.now()
            self.qty_produced = self.qty_production
            if self.date_start:
                duration = (datetime.now() - self.date_start).total_seconds() / 60
                self.duration = duration
            self.save()
            return True
        return False

    def button_pending(self):
        """Set work order to pending"""
        if self.state in ['ready', 'progress']:
            self.state = 'pending'
            self.save()
            return True
        return False


class QualityPoint(models.Model):
    """Quality Control Point - Based on Odoo quality.point"""

    TYPES = [
        ('instructions', 'Instructions'),
        ('pass_fail', 'Pass - Fail'),
        ('measure', 'Take a measure'),
    ]

    title = models.CharField(max_length=128)

    # Product
    product_ids = models.ManyToManyField(ProductProduct, blank=True, help_text="Products")
    product_tmpl_ids = models.ManyToManyField(ProductTemplate, blank=True, help_text="Product Templates")

    # Operation
    operation_id = models.ForeignKey(MrpRoutingWorkcenter, null=True, blank=True, on_delete=models.CASCADE, help_text="Operation")

    # Type
    test_type = models.CharField(max_length=16, choices=TYPES, default='instructions')

    # Instructions
    note = models.TextField(blank=True, help_text="Instructions")

    # Measure
    norm = models.DecimalField(max_digits=16, decimal_places=4, default=0, help_text="Norm")
    tolerance_min = models.DecimalField(max_digits=16, decimal_places=4, default=0, help_text="Min Tolerance")
    tolerance_max = models.DecimalField(max_digits=16, decimal_places=4, default=0, help_text="Max Tolerance")
    norm_unit = models.CharField(max_length=16, blank=True, help_text="Unit of Measure")

    # Sequence
    sequence = models.IntegerField(default=1)
    active = models.BooleanField(default=True)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'quality_point'
        ordering = ['sequence']

    def __str__(self):
        return self.title


class QualityCheck(models.Model):
    """Quality Check - Based on Odoo quality.check"""

    STATES = [
        ('none', 'To do'),
        ('pass', 'Passed'),
        ('fail', 'Failed'),
    ]

    # Quality Point
    point_id = models.ForeignKey(QualityPoint, on_delete=models.CASCADE, help_text="Control Point")

    # Production
    production_id = models.ForeignKey(MrpProduction, null=True, blank=True, on_delete=models.CASCADE, help_text="Production Order")
    workorder_id = models.ForeignKey(MrpWorkorder, null=True, blank=True, on_delete=models.CASCADE, help_text="Work Order")

    # Product
    product_id = models.ForeignKey(ProductProduct, on_delete=models.CASCADE, help_text="Product")

    # Quality
    quality_state = models.CharField(max_length=16, choices=STATES, default='none')

    # User
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, help_text="Responsible")

    # Measure
    measure = models.DecimalField(max_digits=16, decimal_places=4, null=True, blank=True, help_text="Measure")
    measure_success = models.CharField(max_length=16, choices=[('none', 'No measure'), ('pass', 'Success'), ('fail', 'Failure')], default='none')

    # Note
    note = models.TextField(blank=True, help_text="Note")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'quality_check'

    def __str__(self):
        return f"{self.point_id.title} - {self.product_id.name}"

    def action_pass(self):
        """Mark quality check as passed"""
        self.quality_state = 'pass'
        self.save()
        return True

    def action_fail(self):
        """Mark quality check as failed"""
        self.quality_state = 'fail'
        self.save()
        return True
