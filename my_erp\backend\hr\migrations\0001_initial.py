# Generated by Django 4.2.21 on 2025-07-15 19:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0002_remove_stockmove_account_move_ids_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='HrContract',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('state', models.CharField(choices=[('draft', 'New'), ('open', 'Running'), ('pending', 'To Renew'), ('close', 'Expired'), ('cancel', 'Cancelled')], default='draft', max_length=16)),
                ('date_start', models.DateField()),
                ('date_end', models.DateField(blank=True, null=True)),
                ('wage', models.DecimalField(decimal_places=2, default=0, help_text='Basic Wage', max_digits=16)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('resource_calendar_id', models.IntegerField(blank=True, help_text='Working Schedule', null=True)),
                ('working_hours', models.CharField(blank=True, help_text='Working Schedule', max_length=64)),
                ('trial_date_end', models.DateField(blank=True, help_text='End of Trial Period', null=True)),
                ('notes', models.TextField(blank=True)),
                ('analytic_account_id', models.IntegerField(blank=True, help_text='Analytic Account', null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'hr_contract',
            },
        ),
        migrations.CreateModel(
            name='HrContractType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('sequence', models.IntegerField(default=10)),
            ],
            options={
                'db_table': 'hr_contract_type',
            },
        ),
        migrations.CreateModel(
            name='HrDepartment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('complete_name', models.CharField(blank=True, max_length=256)),
                ('active', models.BooleanField(default=True)),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('note', models.TextField(blank=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'hr_department',
            },
        ),
        migrations.CreateModel(
            name='HrEmployee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('active', models.BooleanField(default=True)),
                ('private_name', models.CharField(blank=True, help_text='Private Name', max_length=128)),
                ('gender', models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other')], max_length=8)),
                ('marital', models.CharField(blank=True, choices=[('single', 'Single'), ('married', 'Married'), ('cohabitant', 'Legal Cohabitant'), ('widower', 'Widower'), ('divorced', 'Divorced')], max_length=16)),
                ('spouse_complete_name', models.CharField(blank=True, max_length=128)),
                ('spouse_birthdate', models.DateField(blank=True, null=True)),
                ('children', models.IntegerField(default=0)),
                ('birthday', models.DateField(blank=True, null=True)),
                ('private_street', models.CharField(blank=True, max_length=128)),
                ('private_street2', models.CharField(blank=True, max_length=128)),
                ('private_city', models.CharField(blank=True, max_length=64)),
                ('private_zip', models.CharField(blank=True, max_length=16)),
                ('private_phone', models.CharField(blank=True, max_length=32)),
                ('private_email', models.EmailField(blank=True, max_length=254)),
                ('work_phone', models.CharField(blank=True, max_length=32)),
                ('work_email', models.EmailField(blank=True, max_length=254)),
                ('work_location', models.CharField(blank=True, max_length=128)),
                ('employee_type', models.CharField(choices=[('employee', 'Employee'), ('student', 'Student'), ('trainee', 'Trainee'), ('contractor', 'Contractor'), ('freelance', 'Freelancer')], default='employee', max_length=16)),
                ('identification_id', models.CharField(blank=True, help_text='Identification No', max_length=32)),
                ('passport_id', models.CharField(blank=True, help_text='Passport No', max_length=32)),
                ('bank_account_id', models.CharField(blank=True, help_text='Bank Account Number', max_length=64)),
                ('pin', models.CharField(blank=True, help_text='PIN Code', max_length=16)),
                ('barcode', models.CharField(blank=True, help_text='Badge ID', max_length=128)),
                ('notes', models.TextField(blank=True)),
                ('color', models.IntegerField(default=0)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('address_home_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee_home', to='accounting.respartner')),
                ('coach_id', models.ForeignKey(blank=True, help_text='Coach', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='coached_employees', to='hr.hremployee')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('department_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee_ids', to='hr.hrdepartment')),
            ],
            options={
                'db_table': 'hr_employee',
            },
        ),
        migrations.CreateModel(
            name='HrPayslip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('number', models.CharField(blank=True, max_length=64)),
                ('date_from', models.DateField(help_text='Date From')),
                ('date_to', models.DateField(help_text='Date To')),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('verify', 'Waiting'), ('done', 'Done'), ('cancel', 'Rejected')], default='draft', max_length=16)),
                ('basic_wage', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('net_wage', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('note', models.TextField(blank=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('contract_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrcontract')),
                ('employee_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='slip_ids', to='hr.hremployee')),
                ('move_id', models.ForeignKey(blank=True, help_text='Accounting Entry', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove')),
            ],
            options={
                'db_table': 'hr_payslip',
            },
        ),
        migrations.CreateModel(
            name='HrLeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('sequence', models.IntegerField(default=100)),
                ('active', models.BooleanField(default=True)),
                ('allocation_type', models.CharField(choices=[('no', 'No Allocation'), ('fixed', 'Fixed Allocation'), ('fixed_allocation', 'Fixed by HR')], default='fixed', max_length=16)),
                ('validity_start', models.DateField(blank=True, null=True)),
                ('validity_stop', models.DateField(blank=True, null=True)),
                ('color_name', models.CharField(blank=True, help_text='Color', max_length=32)),
                ('color', models.IntegerField(default=0)),
                ('max_leaves', models.DecimalField(decimal_places=2, default=0, help_text='Maximum Allowed', max_digits=8)),
                ('leaves_taken', models.DecimalField(decimal_places=2, default=0, help_text='Leaves Already Taken', max_digits=8)),
                ('double_validation', models.BooleanField(default=False, help_text='Apply Double Validation')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'hr_leave_type',
            },
        ),
        migrations.CreateModel(
            name='HrLeave',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=64)),
                ('state', models.CharField(choices=[('draft', 'To Submit'), ('confirm', 'To Approve'), ('refuse', 'Refused'), ('validate1', 'Second Approval'), ('validate', 'Approved'), ('cancel', 'Cancelled')], default='confirm', max_length=16)),
                ('request_type', models.CharField(choices=[('remove', 'Leave Request'), ('add', 'Allocation Request')], default='remove', max_length=8)),
                ('date_from', models.DateTimeField(help_text='Start Date')),
                ('date_to', models.DateTimeField(help_text='End Date')),
                ('request_date_from', models.DateField(help_text='Request Start Date')),
                ('request_date_to', models.DateField(help_text='Request End Date')),
                ('number_of_days', models.DecimalField(decimal_places=2, default=0, help_text='Number of Days', max_digits=8)),
                ('number_of_hours_display', models.CharField(blank=True, help_text='Duration in Hours', max_length=16)),
                ('notes', models.TextField(blank=True, help_text='Reason')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('department_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrdepartment')),
                ('employee_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_ids', to='hr.hremployee')),
                ('holiday_status_id', models.ForeignKey(help_text='Leave Type', on_delete=django.db.models.deletion.CASCADE, to='hr.hrleavetype')),
                ('manager_id', models.ForeignKey(blank=True, help_text='Manager', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_leaves', to='hr.hremployee')),
                ('user_id', models.ForeignKey(blank=True, help_text='Responsible User', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'hr_leave',
            },
        ),
        migrations.CreateModel(
            name='HrJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('description', models.TextField(blank=True, help_text='Job Description')),
                ('requirements', models.TextField(blank=True, help_text='Job Requirements')),
                ('state', models.CharField(choices=[('recruit', 'Recruitment in Progress'), ('open', 'Not Recruiting')], default='open', max_length=16)),
                ('no_of_recruitment', models.IntegerField(default=1, help_text='Expected New Employees')),
                ('no_of_hired_employee', models.IntegerField(default=0, help_text='Hired Employees')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('contract_type_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrcontracttype')),
                ('department_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrdepartment')),
            ],
            options={
                'db_table': 'hr_job',
            },
        ),
        migrations.AddField(
            model_name='hremployee',
            name='job_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee_ids', to='hr.hrjob'),
        ),
        migrations.AddField(
            model_name='hremployee',
            name='parent_id',
            field=models.ForeignKey(blank=True, help_text='Manager', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='child_ids', to='hr.hremployee'),
        ),
        migrations.AddField(
            model_name='hremployee',
            name='private_country_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee_private_country', to='accounting.rescountry'),
        ),
        migrations.AddField(
            model_name='hremployee',
            name='private_state_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee_private_state', to='accounting.rescountrystate'),
        ),
        migrations.AddField(
            model_name='hremployee',
            name='user_id',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='hrdepartment',
            name='manager_id',
            field=models.ForeignKey(blank=True, help_text='Department Manager', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='hr.hremployee'),
        ),
        migrations.AddField(
            model_name='hrdepartment',
            name='parent_id',
            field=models.ForeignKey(blank=True, help_text='Parent Department', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_departments', to='hr.hrdepartment'),
        ),
        migrations.AddField(
            model_name='hrcontract',
            name='department_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrdepartment'),
        ),
        migrations.AddField(
            model_name='hrcontract',
            name='employee_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contract_ids', to='hr.hremployee'),
        ),
        migrations.AddField(
            model_name='hrcontract',
            name='job_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrjob'),
        ),
        migrations.AddField(
            model_name='hrcontract',
            name='type_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrcontracttype'),
        ),
        migrations.CreateModel(
            name='HrAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('check_in', models.DateTimeField(help_text='Check In')),
                ('check_out', models.DateTimeField(blank=True, help_text='Check Out', null=True)),
                ('worked_hours', models.DecimalField(decimal_places=2, default=0, help_text='Worked Hours', max_digits=8)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('employee_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_ids', to='hr.hremployee')),
            ],
            options={
                'db_table': 'hr_attendance',
                'ordering': ['-check_in'],
            },
        ),
    ]
