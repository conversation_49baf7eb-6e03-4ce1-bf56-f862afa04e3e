#!/usr/bin/env python
"""
Test Setup API endpoints
"""
import os
import sys
import django
import requests

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token

def test_setup_api():
    # Create test user and token
    user, created = User.objects.get_or_create(
        username='testuser', 
        defaults={'email': '<EMAIL>', 'is_staff': True, 'is_superuser': True}
    )
    if created:
        user.set_password('testpass')
        user.save()
        print(f"✅ Created test user: {user.username}")
    
    # Get or create token
    token, created = Token.objects.get_or_create(user=user)
    print(f"🔑 Token: {token.key}")
    
    # Test API endpoints
    base_url = 'http://localhost:8000/api/setup'
    headers = {'Authorization': f'Token {token.key}'}
    
    endpoints = [
        '/countries/',
        '/currencies/',
        '/timezones/',
        '/companies/'
    ]
    
    print("\n🧪 Testing Setup API endpoints:")
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", headers=headers)
            if response.status_code == 200:
                data = response.json()
                count = len(data.get('results', []))
                print(f"✅ {endpoint} - Status: {response.status_code}, Count: {count}")
            else:
                print(f"❌ {endpoint} - Status: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - Error: {e}")
    
    print(f"\n📊 Summary:")
    print(f"   Test User: {user.username}")
    print(f"   Token: {token.key}")
    print(f"   Base URL: {base_url}")

if __name__ == '__main__':
    test_setup_api()
