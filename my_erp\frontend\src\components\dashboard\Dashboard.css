/* Dashboard Styles - Professional ERP Dashboard */

.dashboard-container {
  padding: 0;
  background: #f0f2f5;
  min-height: calc(100vh - 200px);
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: #fff;
  border-radius: 8px;
}

.dashboard-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 32px;
  border-radius: 12px;
  margin-bottom: 24px;
  color: #fff;
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3);
}

.dashboard-header h2 {
  color: #fff !important;
  margin: 0;
  font-weight: 600;
}

.dashboard-header .ant-typography {
  color: rgba(255, 255, 255, 0.85);
}

.dashboard-header .ant-btn {
  border-color: rgba(255, 255, 255, 0.3);
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.dashboard-header .ant-btn:hover {
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.dashboard-header .ant-btn-primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.dashboard-header .ant-btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Metric Cards */
.metric-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  background: #fff;
}

.metric-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.metric-card .ant-statistic-title {
  color: #8c8c8c;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 8px;
}

.metric-card .ant-statistic-content {
  font-size: 28px;
  font-weight: 600;
  line-height: 1.2;
}

.metric-card .ant-statistic-content-value {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Module Cards */
.module-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fff;
  height: 140px;
  display: flex;
  align-items: center;
}

.module-card:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-4px);
  border-color: #1890ff;
}

.module-card.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.module-card.disabled:hover {
  transform: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border-color: #f0f0f0;
}

.module-card .ant-card-body {
  padding: 20px;
  height: 100%;
  display: flex;
  align-items: center;
}

.module-card h5 {
  color: #262626;
  font-weight: 600;
  margin: 0;
  font-size: 16px;
}

.module-card .ant-avatar {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Activity and Tasks Cards */
.activity-card,
.tasks-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  background: #fff;
}

.activity-card .ant-card-head,
.tasks-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 12px 12px 0 0;
}

.activity-card .ant-card-head-title,
.tasks-card .ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

.activity-card .ant-list-item,
.tasks-card .ant-list-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.activity-card .ant-list-item:last-child,
.tasks-card .ant-list-item:last-child {
  border-bottom: none;
}

.activity-card .ant-list-item-meta-title,
.tasks-card .ant-list-item-meta-title {
  color: #262626;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
}

.activity-card .ant-list-item-meta-description,
.tasks-card .ant-list-item-meta-description {
  color: #8c8c8c;
  font-size: 12px;
}

/* Quick Actions */
.dashboard-container .ant-btn {
  border-radius: 8px;
  font-weight: 500;
  height: 40px;
  transition: all 0.3s ease;
}

.dashboard-container .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dashboard-container .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.dashboard-container .ant-btn-primary:hover {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
}

/* Progress Indicators */
.progress-indicator {
  margin-top: 8px;
}

.progress-indicator .ant-progress-text {
  font-size: 12px;
  font-weight: 500;
}

/* Badge Styles */
.ant-badge {
  font-size: 12px;
}

.ant-badge-count {
  background: #ff4d4f;
  box-shadow: 0 0 0 1px #fff;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .module-card {
    height: 120px;
  }
  
  .module-card .ant-card-body {
    padding: 16px;
  }
  
  .metric-card .ant-statistic-content {
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 24px;
    margin-bottom: 16px;
  }
  
  .dashboard-header h2 {
    font-size: 20px;
  }
  
  .module-card {
    height: 100px;
  }
  
  .module-card .ant-card-body {
    padding: 12px;
  }
  
  .module-card h5 {
    font-size: 14px;
  }
  
  .module-card .ant-avatar {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .metric-card .ant-statistic-content {
    font-size: 20px;
  }
  
  .metric-card .ant-statistic-title {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 0;
  }
  
  .dashboard-header {
    padding: 16px;
    border-radius: 8px;
  }
  
  .dashboard-header h2 {
    font-size: 18px;
  }
  
  .module-card {
    height: 80px;
  }
  
  .module-card .ant-card-body {
    padding: 8px;
  }
  
  .module-card h5 {
    font-size: 12px;
  }
  
  .module-card .ant-typography {
    font-size: 10px;
  }
  
  .metric-card .ant-statistic-content {
    font-size: 18px;
  }
}

/* Animation Styles */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-container > * {
  animation: fadeInUp 0.6s ease-out;
}

.dashboard-container > *:nth-child(1) { animation-delay: 0.1s; }
.dashboard-container > *:nth-child(2) { animation-delay: 0.2s; }
.dashboard-container > *:nth-child(3) { animation-delay: 0.3s; }
.dashboard-container > *:nth-child(4) { animation-delay: 0.4s; }

/* Loading Animation */
.dashboard-loading .ant-spin {
  color: #1890ff;
}

.dashboard-loading .ant-spin-dot-item {
  background-color: #1890ff;
}

/* Card Hover Effects */
.metric-card,
.module-card,
.activity-card,
.tasks-card {
  position: relative;
  overflow: hidden;
}

.metric-card::before,
.module-card::before,
.activity-card::before,
.tasks-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.metric-card:hover::before,
.module-card:hover::before,
.activity-card:hover::before,
.tasks-card:hover::before {
  left: 100%;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .dashboard-container {
    background: #141414;
  }
  
  .metric-card,
  .module-card,
  .activity-card,
  .tasks-card {
    background: #1f1f1f;
    border-color: #303030;
    color: #fff;
  }
  
  .metric-card:hover,
  .module-card:hover,
  .activity-card:hover,
  .tasks-card:hover {
    border-color: #1890ff;
  }
  
  .activity-card .ant-card-head,
  .tasks-card .ant-card-head {
    background: #262626;
    border-color: #303030;
  }
  
  .module-card h5,
  .activity-card .ant-card-head-title,
  .tasks-card .ant-card-head-title {
    color: #fff;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .metric-card,
  .module-card,
  .activity-card,
  .tasks-card {
    border: 2px solid #000;
  }
  
  .dashboard-header {
    background: #000;
    color: #fff;
  }
  
  .module-card:hover {
    border-color: #000;
    background: #f0f0f0;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .metric-card,
  .module-card,
  .activity-card,
  .tasks-card,
  .dashboard-container .ant-btn {
    transition: none;
    animation: none;
  }
  
  .metric-card:hover,
  .module-card:hover,
  .dashboard-container .ant-btn:hover {
    transform: none;
  }
  
  .dashboard-container > * {
    animation: none;
  }
}
