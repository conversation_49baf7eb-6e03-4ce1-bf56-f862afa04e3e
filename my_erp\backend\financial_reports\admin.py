"""
Financial Reports Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from django.http import HttpResponse
import json
from .models import (
    DateRange, AccountAgeReportConfiguration, AccountAgeReportConfigurationLine,
    TrialBalanceReport, GeneralLedgerReport, AgedPartnerBalanceReport,
    OpenItemsReport, JournalLedgerReport, VatReport
)


@admin.register(DateRange)
class DateRangeAdmin(admin.ModelAdmin):
    list_display = ['name', 'type_id', 'date_start', 'date_end', 'active', 'company_id']
    list_filter = ['type_id', 'active', 'company_id']
    search_fields = ['name']
    ordering = ['-date_start']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'type_id', 'active')
        }),
        ('Date Range', {
            'fields': ('date_start', 'date_end')
        }),
    )


class AccountAgeReportConfigurationLineInline(admin.TabularInline):
    model = AccountAgeReportConfigurationLine
    extra = 0
    fields = ['sequence', 'name', 'days_from', 'days_to']
    ordering = ['sequence']


@admin.register(AccountAgeReportConfiguration)
class AccountAgeReportConfigurationAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'company_id', 'line_count']
    list_filter = ['active', 'company_id']
    search_fields = ['name']
    inlines = [AccountAgeReportConfigurationLineInline]

    def line_count(self, obj):
        count = obj.line_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    line_count.short_description = 'Intervals'


@admin.register(TrialBalanceReport)
class TrialBalanceReportAdmin(admin.ModelAdmin):
    list_display = ['name', 'date_from', 'date_to', 'only_posted_moves', 'show_partner_details', 'company_id']
    list_filter = ['only_posted_moves', 'show_partner_details', 'company_id', 'date_from']
    search_fields = ['name']
    filter_horizontal = ['account_ids', 'partner_ids', 'journal_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'date_from', 'date_to')
        }),
        ('Filters', {
            'fields': ('account_ids', 'partner_ids', 'journal_ids')
        }),
        ('Options', {
            'fields': ('only_posted_moves', 'show_move_line_details', 'hide_account_at_0', 'show_partner_details')
        }),
        ('Hierarchy', {
            'fields': ('limit_hierarchy_level', 'hierarchy_level', 'show_hierarchy_level'),
            'classes': ('collapse',)
        }),
    )

    actions = ['generate_report', 'export_excel']

    def generate_report(self, request, queryset):
        for report in queryset:
            data = report.generate_report_data()
            # Here you would generate the actual report
        self.message_user(request, f'{queryset.count()} trial balance reports generated successfully.')
    generate_report.short_description = "Generate trial balance reports"

    def export_excel(self, request, queryset):
        # Here you would export to Excel
        self.message_user(request, f'{queryset.count()} reports exported to Excel successfully.')
    export_excel.short_description = "Export to Excel"


@admin.register(GeneralLedgerReport)
class GeneralLedgerReportAdmin(admin.ModelAdmin):
    list_display = ['name', 'date_from', 'date_to', 'only_posted_moves', 'centralize', 'company_id']
    list_filter = ['only_posted_moves', 'centralize', 'show_analytic_tags', 'company_id', 'date_from']
    search_fields = ['name']
    filter_horizontal = ['account_ids', 'partner_ids', 'journal_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'date_from', 'date_to')
        }),
        ('Filters', {
            'fields': ('account_ids', 'partner_ids', 'journal_ids')
        }),
        ('Options', {
            'fields': ('only_posted_moves', 'show_move_line_details', 'hide_account_at_0', 'centralize', 'show_analytic_tags')
        }),
    )

    actions = ['generate_report', 'export_excel']

    def generate_report(self, request, queryset):
        for report in queryset:
            data = report.generate_report_data()
        self.message_user(request, f'{queryset.count()} general ledger reports generated successfully.')
    generate_report.short_description = "Generate general ledger reports"

    def export_excel(self, request, queryset):
        self.message_user(request, f'{queryset.count()} reports exported to Excel successfully.')
    export_excel.short_description = "Export to Excel"


@admin.register(AgedPartnerBalanceReport)
class AgedPartnerBalanceReportAdmin(admin.ModelAdmin):
    list_display = ['name', 'account_type', 'date_from', 'date_to', 'only_posted_moves', 'company_id']
    list_filter = ['account_type', 'only_posted_moves', 'company_id', 'date_from']
    search_fields = ['name']
    filter_horizontal = ['account_ids', 'partner_ids', 'journal_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'account_type', 'date_from', 'date_to')
        }),
        ('Filters', {
            'fields': ('account_ids', 'partner_ids', 'journal_ids')
        }),
        ('Options', {
            'fields': ('only_posted_moves', 'show_move_line_details', 'hide_account_at_0')
        }),
    )

    actions = ['generate_report', 'export_excel']

    def generate_report(self, request, queryset):
        for report in queryset:
            data = report.generate_report_data()
        self.message_user(request, f'{queryset.count()} aged partner balance reports generated successfully.')
    generate_report.short_description = "Generate aged partner balance reports"

    def export_excel(self, request, queryset):
        self.message_user(request, f'{queryset.count()} reports exported to Excel successfully.')
    export_excel.short_description = "Export to Excel"


@admin.register(OpenItemsReport)
class OpenItemsReportAdmin(admin.ModelAdmin):
    list_display = ['name', 'account_type', 'date_from', 'date_to', 'target_move', 'company_id']
    list_filter = ['account_type', 'target_move', 'only_posted_moves', 'company_id', 'date_from']
    search_fields = ['name']
    filter_horizontal = ['account_ids', 'partner_ids', 'journal_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'account_type', 'date_from', 'date_to')
        }),
        ('Filters', {
            'fields': ('account_ids', 'partner_ids', 'journal_ids')
        }),
        ('Options', {
            'fields': ('only_posted_moves', 'target_move', 'hide_account_at_0')
        }),
    )

    actions = ['generate_report', 'export_excel']

    def generate_report(self, request, queryset):
        for report in queryset:
            data = report.generate_report_data()
        self.message_user(request, f'{queryset.count()} open items reports generated successfully.')
    generate_report.short_description = "Generate open items reports"

    def export_excel(self, request, queryset):
        self.message_user(request, f'{queryset.count()} reports exported to Excel successfully.')
    export_excel.short_description = "Export to Excel"


@admin.register(JournalLedgerReport)
class JournalLedgerReportAdmin(admin.ModelAdmin):
    list_display = ['name', 'date_from', 'date_to', 'sort_option', 'group_option', 'company_id']
    list_filter = ['sort_option', 'group_option', 'only_posted_moves', 'company_id', 'date_from']
    search_fields = ['name']
    filter_horizontal = ['account_ids', 'partner_ids', 'journal_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'date_from', 'date_to')
        }),
        ('Filters', {
            'fields': ('account_ids', 'partner_ids', 'journal_ids')
        }),
        ('Options', {
            'fields': ('only_posted_moves', 'show_move_line_details', 'sort_option', 'group_option')
        }),
    )

    actions = ['generate_report', 'export_excel']

    def generate_report(self, request, queryset):
        for report in queryset:
            data = report.generate_report_data()
        self.message_user(request, f'{queryset.count()} journal ledger reports generated successfully.')
    generate_report.short_description = "Generate journal ledger reports"

    def export_excel(self, request, queryset):
        self.message_user(request, f'{queryset.count()} reports exported to Excel successfully.')
    export_excel.short_description = "Export to Excel"


@admin.register(VatReport)
class VatReportAdmin(admin.ModelAdmin):
    list_display = ['name', 'date_from', 'date_to', 'based_on', 'tax_detail', 'company_id']
    list_filter = ['based_on', 'tax_detail', 'only_posted_moves', 'company_id', 'date_from']
    search_fields = ['name']
    filter_horizontal = ['account_ids', 'partner_ids', 'journal_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'date_from', 'date_to')
        }),
        ('Filters', {
            'fields': ('account_ids', 'partner_ids', 'journal_ids')
        }),
        ('Options', {
            'fields': ('only_posted_moves', 'based_on', 'tax_detail', 'hide_account_at_0')
        }),
    )

    actions = ['generate_report', 'export_excel']

    def generate_report(self, request, queryset):
        for report in queryset:
            data = report.generate_report_data()
        self.message_user(request, f'{queryset.count()} VAT reports generated successfully.')
    generate_report.short_description = "Generate VAT reports"

    def export_excel(self, request, queryset):
        self.message_user(request, f'{queryset.count()} reports exported to Excel successfully.')
    export_excel.short_description = "Export to Excel"


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - Financial Reports"
