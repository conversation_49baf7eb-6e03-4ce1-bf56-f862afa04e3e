"""
API Serializers for Accounting Module - Based on Odoo's Structure
"""
from rest_framework import serializers
from .models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal
)


class CompanySerializer(serializers.ModelSerializer):
    """Company serializer - Based on Odoo res.company"""
    class Meta:
        model = ResCompany
        fields = '__all__'


class PartnerSerializer(serializers.ModelSerializer):
    """Partner serializer - Based on Odoo res.partner"""
    is_customer = serializers.BooleanField(read_only=True)
    is_vendor = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = ResPartner
        fields = '__all__'


class AccountSerializer(serializers.ModelSerializer):
    """Account serializer - Based on Odoo account.account"""
    balance = serializers.DecimalField(max_digits=16, decimal_places=2, read_only=True)
    
    class Meta:
        model = AccountAccount
        fields = '__all__'


class JournalSerializer(serializers.ModelSerializer):
    """Journal serializer - Based on Odoo account.journal"""
    default_account_name = serializers.CharField(source='default_account_id.name', read_only=True)
    
    class Meta:
        model = AccountJournal
        fields = '__all__'
