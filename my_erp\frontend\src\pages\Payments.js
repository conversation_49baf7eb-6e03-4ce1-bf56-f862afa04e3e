import React, { useState, useEffect } from 'react';
import { Table, Button, Typography, Tag, Space } from 'antd';
import { PlusOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Payments = () => {
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(false);

  // Mock data
  const mockPayments = [
    {
      id: 1,
      name: 'BNK1/2024/001',
      partner_name: 'ABC Company Ltd.',
      date: '2024-01-15',
      amount: 25000,
      payment_type: 'inbound',
      partner_type: 'customer',
      state: 'posted',
      ref: 'Invoice Payment',
    },
    {
      id: 2,
      name: 'BNK1/2024/002',
      partner_name: 'Office Supplies Co.',
      date: '2024-01-12',
      amount: 15000,
      payment_type: 'outbound',
      partner_type: 'supplier',
      state: 'posted',
      ref: 'Bill Payment',
    },
  ];

  const columns = [
    {
      title: 'Payment #',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: 'Partner',
      dataIndex: 'partner_name',
      key: 'partner_name',
    },
    {
      title: 'Type',
      dataIndex: 'payment_type',
      key: 'payment_type',
      render: (type, record) => {
        const isInbound = type === 'inbound';
        return (
          <Tag color={isInbound ? 'green' : 'red'}>
            {isInbound ? 'Receive' : 'Send'} ({record.partner_type})
          </Tag>
        );
      },
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount, record) => (
        <span className={record.payment_type === 'inbound' ? 'balance-positive' : 'balance-negative'}>
          PKR {amount.toLocaleString()}
        </span>
      ),
      align: 'right',
    },
    {
      title: 'Reference',
      dataIndex: 'ref',
      key: 'ref',
    },
    {
      title: 'Status',
      dataIndex: 'state',
      key: 'state',
      render: (state) => (
        <Tag color={state === 'posted' ? 'green' : 'orange'}>
          {state === 'posted' ? 'Posted' : 'Draft'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <Space>
          <Button type="text" icon={<EyeOutlined />} size="small" />
          <Button type="text" icon={<EditOutlined />} size="small" />
        </Space>
      ),
    },
  ];

  useEffect(() => {
    setPayments(mockPayments);
  }, []);

  return (
    <div>
      <div className="page-header">
        <Title level={2}>Payments</Title>
        <p>Manage customer and vendor payments</p>
      </div>

      <div className="action-buttons">
        <Space>
          <Button type="primary" icon={<PlusOutlined />}>
            Register Payment
          </Button>
          <Button>Send Money</Button>
          <Button>Receive Money</Button>
        </Space>
      </div>

      <div className="table-container">
        <Table
          columns={columns}
          dataSource={payments}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} payments`,
          }}
        />
      </div>
    </div>
  );
};

export default Payments;
