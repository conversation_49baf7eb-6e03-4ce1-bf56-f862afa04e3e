# Generated by Django 4.2.21 on 2025-07-15 17:53

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='New', max_length=64)),
                ('origin', models.CharField(blank=True, help_text='Source Document', max_length=64)),
                ('partner_ref', models.CharField(blank=True, help_text='Vendor Reference', max_length=64)),
                ('state', models.CharField(choices=[('draft', 'RFQ'), ('sent', 'RFQ Sent'), ('to approve', 'To Approve'), ('purchase', 'Purchase Order'), ('done', 'Locked'), ('cancel', 'Cancelled')], default='draft', max_length=12)),
                ('date_order', models.DateTimeField(default=datetime.datetime.now)),
                ('date_approve', models.DateTimeField(blank=True, null=True)),
                ('date_planned', models.DateTimeField(blank=True, help_text='Expected Date', null=True)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('amount_untaxed', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('invoice_status', models.CharField(choices=[('no', 'Nothing to Bill'), ('to invoice', 'Waiting Bills'), ('invoiced', 'Fully Billed')], default='no', max_length=16)),
                ('invoice_count', models.IntegerField(default=0)),
                ('picking_type_id', models.IntegerField(blank=True, help_text='Operation Type', null=True)),
                ('group_id', models.IntegerField(blank=True, help_text='Procurement Group', null=True)),
                ('notes', models.TextField(blank=True, help_text='Terms and Conditions')),
                ('receipt_reminder_email', models.BooleanField(default=True)),
                ('reminder_date_before_receipt', models.IntegerField(default=1, help_text='Days before receipt')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('alternative_po_ids', models.ManyToManyField(blank=True, help_text='Alternative POs', to='purchase.purchaseorder')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('dest_address_id', models.ForeignKey(blank=True, help_text='Drop Ship Address', null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='purchase_dest_orders', to='accounting.respartner')),
                ('fiscal_position_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfiscalposition')),
                ('invoice_ids', models.ManyToManyField(blank=True, help_text='Generated Bills', related_name='purchase_orders', to='accounting.accountmove')),
                ('partner_id', models.ForeignKey(help_text='Vendor', on_delete=django.db.models.deletion.RESTRICT, to='accounting.respartner')),
                ('payment_term_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountpaymentterm')),
                ('user_id', models.ForeignKey(default=1, help_text='Purchase Representative', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'purchase_order',
                'ordering': ['-date_order', '-name'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(help_text='Description')),
                ('sequence', models.IntegerField(default=10)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1, help_text='Quantity', max_digits=16)),
                ('product_uom', models.IntegerField(blank=True, help_text='Unit of Measure', null=True)),
                ('qty_received', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('qty_invoiced', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('qty_to_invoice', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_tax', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('invoice_status', models.CharField(choices=[('no', 'Nothing to Bill'), ('to invoice', 'Waiting Bills'), ('invoiced', 'Fully Billed')], default='no', max_length=16)),
                ('analytic_tag_ids', models.CharField(blank=True, help_text='Analytic Tags', max_length=256)),
                ('date_planned', models.DateTimeField(help_text='Scheduled Date')),
                ('display_type', models.CharField(blank=True, choices=[('line_section', 'Section'), ('line_note', 'Note')], max_length=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_analytic_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountanalyticaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('invoice_lines', models.ManyToManyField(blank=True, help_text='Bill Lines', related_name='purchase_order_lines', to='accounting.accountmoveline')),
                ('order_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_line', to='purchase.purchaseorder')),
                ('product_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='accounting.productproduct')),
                ('product_template_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='accounting.producttemplate')),
                ('taxes_id', models.ManyToManyField(blank=True, help_text='Taxes', related_name='purchase_order_lines', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'purchase_order_line',
            },
        ),
    ]
