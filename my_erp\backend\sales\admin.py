"""
Sales Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from .models import SaleOrder, SaleOrderLine


class SaleOrderLineInline(admin.TabularInline):
    model = SaleOrderLine
    extra = 1
    fields = ['product_id', 'name', 'product_uom_qty', 'price_unit', 'discount', 'price_subtotal', 'qty_delivered', 'qty_invoiced']
    readonly_fields = ['price_subtotal', 'qty_delivered', 'qty_invoiced']


@admin.register(SaleOrder)
class SaleOrderAdmin(admin.ModelAdmin):
    list_display = ['name', 'date_order', 'partner_id', 'user_id', 'team_id', 'state', 'invoice_status', 'amount_total_display']
    list_filter = ['state', 'invoice_status', 'date_order', 'team_id', 'company_id']
    search_fields = ['name', 'partner_id__name', 'client_order_ref', 'origin']
    inlines = [SaleOrderLineInline]
    readonly_fields = ['amount_untaxed', 'amount_tax', 'amount_total', 'invoice_count']
    ordering = ['-date_order', '-name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'origin', 'client_order_ref', 'state')
        }),
        ('Customer Information', {
            'fields': ('partner_id', 'partner_invoice_id', 'partner_shipping_id')
        }),
        ('Sales Information', {
            'fields': ('user_id', 'team_id', 'date_order', 'validity_date', 'expected_date')
        }),
        ('Accounting', {
            'fields': ('payment_term_id', 'fiscal_position_id', 'analytic_account_id')
        }),
        ('Amounts', {
            'fields': ('amount_untaxed', 'amount_tax', 'amount_total', 'currency_id')
        }),
        ('Invoice Status', {
            'fields': ('invoice_status', 'invoice_count')
        }),
        ('Other', {
            'fields': ('note', 'signature', 'signed_by', 'signed_on'),
            'classes': ('collapse',)
        }),
    )

    def amount_total_display(self, obj):
        return format_html(
            '<span style="font-weight: bold; color: {};">{} {:,.2f}</span>',
            'green' if obj.amount_total > 0 else 'black',
            obj.currency_id,
            obj.amount_total
        )
    amount_total_display.short_description = 'Total Amount'

    actions = ['action_confirm_orders', 'action_cancel_orders']

    def action_confirm_orders(self, request, queryset):
        confirmed = 0
        for order in queryset:
            if order.action_confirm():
                confirmed += 1
        self.message_user(request, f'{confirmed} orders confirmed successfully.')
    action_confirm_orders.short_description = "Confirm selected orders"

    def action_cancel_orders(self, request, queryset):
        cancelled = 0
        for order in queryset:
            if order.action_cancel():
                cancelled += 1
        self.message_user(request, f'{cancelled} orders cancelled successfully.')
    action_cancel_orders.short_description = "Cancel selected orders"


@admin.register(SaleOrderLine)
class SaleOrderLineAdmin(admin.ModelAdmin):
    list_display = ['order_id', 'product_id', 'name', 'product_uom_qty', 'price_unit', 'discount', 'price_subtotal', 'invoice_status']
    list_filter = ['invoice_status', 'order_id__state', 'company_id']
    search_fields = ['order_id__name', 'product_id__product_tmpl_id__name', 'name']
    readonly_fields = ['price_subtotal', 'price_total', 'price_tax', 'qty_delivered', 'qty_invoiced', 'qty_to_invoice']
    ordering = ['-order_id__date_order', 'sequence']

    fieldsets = (
        ('Order Information', {
            'fields': ('order_id', 'sequence', 'name')
        }),
        ('Product Information', {
            'fields': ('product_id', 'product_template_id', 'product_uom_qty', 'product_uom')
        }),
        ('Pricing', {
            'fields': ('price_unit', 'discount', 'price_subtotal', 'price_tax', 'price_total')
        }),
        ('Quantities', {
            'fields': ('qty_delivered', 'qty_invoiced', 'qty_to_invoice')
        }),
        ('Taxes & Analytics', {
            'fields': ('tax_id', 'analytic_account_id')
        }),
        ('Invoice Status', {
            'fields': ('invoice_status',)
        }),
    )

    filter_horizontal = ['tax_id', 'invoice_lines']


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - Sales Module"
