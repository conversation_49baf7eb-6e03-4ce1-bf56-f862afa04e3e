# Generated by Django 4.2.21 on 2025-07-15 17:47

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SaleOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='New', max_length=64)),
                ('origin', models.CharField(blank=True, help_text='Source Document', max_length=64)),
                ('client_order_ref', models.CharField(blank=True, help_text='Customer Reference', max_length=64)),
                ('reference', models.CharField(blank=True, max_length=64)),
                ('state', models.CharField(choices=[('draft', 'Quotation'), ('sent', 'Quotation Sent'), ('sale', 'Sales Order'), ('done', 'Locked'), ('cancel', 'Cancelled')], default='draft', max_length=8)),
                ('date_order', models.DateTimeField(default=datetime.datetime.now)),
                ('validity_date', models.DateField(blank=True, null=True)),
                ('confirmation_date', models.DateTimeField(blank=True, null=True)),
                ('expected_date', models.DateTimeField(blank=True, null=True)),
                ('pricelist_id', models.IntegerField(blank=True, help_text='Pricelist reference', null=True)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('amount_untaxed', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_undiscounted', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('invoice_status', models.CharField(choices=[('upselling', 'Upselling Opportunity'), ('invoiced', 'Fully Invoiced'), ('to invoice', 'To Invoice'), ('no', 'Nothing to Invoice')], default='no', max_length=16)),
                ('invoice_count', models.IntegerField(default=0)),
                ('picking_policy', models.CharField(choices=[('direct', 'As soon as possible'), ('one', 'When all products are ready')], default='direct', max_length=16)),
                ('warehouse_id', models.IntegerField(blank=True, help_text='Warehouse reference', null=True)),
                ('note', models.TextField(blank=True, help_text='Terms and Conditions')),
                ('signature', models.TextField(blank=True)),
                ('signed_by', models.CharField(blank=True, max_length=64)),
                ('signed_on', models.DateTimeField(blank=True, null=True)),
                ('opportunity_id', models.IntegerField(blank=True, help_text='CRM Opportunity reference', null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('analytic_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountanalyticaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('fiscal_position_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfiscalposition')),
                ('invoice_ids', models.ManyToManyField(blank=True, help_text='Generated Invoices', related_name='sale_orders', to='accounting.accountmove')),
                ('partner_id', models.ForeignKey(help_text='Customer', on_delete=django.db.models.deletion.RESTRICT, to='accounting.respartner')),
                ('partner_invoice_id', models.ForeignKey(help_text='Invoice Address', on_delete=django.db.models.deletion.RESTRICT, related_name='sales_invoice_orders', to='accounting.respartner')),
                ('partner_shipping_id', models.ForeignKey(help_text='Delivery Address', on_delete=django.db.models.deletion.RESTRICT, related_name='sales_shipping_orders', to='accounting.respartner')),
                ('payment_term_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountpaymentterm')),
            ],
            options={
                'db_table': 'sale_order',
                'ordering': ['-date_order', '-name'],
            },
        ),
        migrations.CreateModel(
            name='SalesTeam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('invoiced_target', models.DecimalField(decimal_places=2, default=0, help_text='Monthly Target', max_digits=16)),
                ('quotation_count', models.IntegerField(default=0)),
                ('sale_order_count', models.IntegerField(default=0)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('member_ids', models.ManyToManyField(blank=True, help_text='Team Members', related_name='sales_teams', to=settings.AUTH_USER_MODEL)),
                ('user_id', models.ForeignKey(blank=True, help_text='Team Leader', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'crm_team',
            },
        ),
        migrations.CreateModel(
            name='SaleOrderLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(help_text='Description')),
                ('sequence', models.IntegerField(default=10)),
                ('product_uom_qty', models.DecimalField(decimal_places=4, default=1, help_text='Ordered Quantity', max_digits=16)),
                ('product_uom', models.IntegerField(blank=True, help_text='Unit of Measure', null=True)),
                ('qty_delivered', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('qty_delivered_method', models.CharField(choices=[('manual', 'Manual'), ('stock_move', 'Stock Moves')], default='manual', max_length=16)),
                ('qty_invoiced', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('qty_to_invoice', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_tax', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('discount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('invoice_status', models.CharField(choices=[('upselling', 'Upselling Opportunity'), ('invoiced', 'Fully Invoiced'), ('to invoice', 'To Invoice'), ('no', 'Nothing to Invoice')], default='no', max_length=16)),
                ('customer_lead', models.DecimalField(decimal_places=2, default=0, help_text='Delivery Lead Time', max_digits=16)),
                ('display_type', models.CharField(blank=True, choices=[('line_section', 'Section'), ('line_note', 'Note')], max_length=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('analytic_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountanalyticaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('invoice_lines', models.ManyToManyField(blank=True, help_text='Invoice Lines', related_name='sale_order_lines', to='accounting.accountmoveline')),
                ('order_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_line', to='sales.saleorder')),
                ('product_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='accounting.productproduct')),
                ('product_template_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='accounting.producttemplate')),
                ('tax_id', models.ManyToManyField(blank=True, help_text='Taxes', related_name='sale_order_lines', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'sale_order_line',
            },
        ),
        migrations.AddField(
            model_name='saleorder',
            name='team_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesteam'),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='user_id',
            field=models.ForeignKey(default=1, help_text='Salesperson', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
