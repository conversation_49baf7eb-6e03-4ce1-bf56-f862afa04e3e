/**
 * Chart of Accounts Setup Component
 * Following Odoo's chart of accounts configuration
 */
import React from 'react';
import { Card, Typography, Alert } from 'antd';
import { AccountBookOutlined } from '@ant-design/icons';

const { Title } = Typography;

const ChartOfAccountsSetup = () => {
  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <AccountBookOutlined style={{ marginRight: 8 }} />
        Chart of Accounts Setup
      </Title>
      
      <Card>
        <Alert
          message="Coming Soon"
          description="Chart of Accounts setup will be available soon. This will allow you to configure your accounting structure."
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
};

export default ChartOfAccountsSetup;
