"""
Setup Admin - Django admin configuration for setup models
Professional admin interface for ERP setup management
"""
from django.contrib import admin
from django.utils.html import format_html
from .models import (
    Country, Currency, Timezone, CompanySetup, BankAccount,
    TaxConfiguration, IncomeTaxConfiguration, PaymentTerm, SetupWizardStep, SystemConfiguration,
    SetupTemplate
)

@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'phone_code', 'currency_code', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'code']
    ordering = ['name']

@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'symbol', 'decimal_places', 'is_active']
    list_filter = ['is_active', 'decimal_places']
    search_fields = ['name', 'code']
    ordering = ['name']

@admin.register(Timezone)
class TimezoneAdmin(admin.ModelAdmin):
    list_display = ['name', 'offset', 'description', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'description']
    ordering = ['offset', 'name']

@admin.register(CompanySetup)
class CompanySetupAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'email', 'city', 'country', 'currency',
        'setup_progress_display', 'setup_completed', 'created_at'
    ]
    list_filter = ['setup_completed', 'country', 'currency', 'created_at']
    search_fields = ['name', 'email', 'city']
    readonly_fields = ['setup_progress', 'created_at', 'updated_at']

    def setup_progress_display(self, obj):
        color = 'green' if obj.setup_completed else 'orange'
        return format_html(
            '<span style="color: {};">{} %</span>',
            color,
            obj.setup_progress
        )
    setup_progress_display.short_description = 'Progress'


@admin.register(IncomeTaxConfiguration)
class IncomeTaxConfigurationAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'company', 'rate', 'calculation_method', 'min_income', 'max_income',
        'tax_year_start', 'tax_year_end', 'is_default', 'is_active'
    ]
    list_filter = ['calculation_method', 'is_default', 'is_active', 'company', 'tax_year_start']
    search_fields = ['name', 'company__name']
    ordering = ['company', 'min_income']

    fieldsets = (
        ('Basic Information', {
            'fields': ('company', 'name', 'rate', 'calculation_method', 'is_default', 'is_active')
        }),
        ('Tax Year', {
            'fields': ('tax_year_start', 'tax_year_end')
        }),
        ('Income Brackets', {
            'fields': ('min_income', 'max_income')
        }),
        ('Deductions & Exemptions', {
            'fields': ('standard_deduction', 'personal_exemption')
        }),
        ('Filing Requirements', {
            'fields': ('advance_tax_required', 'quarterly_filing', 'annual_filing_deadline')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('company')
