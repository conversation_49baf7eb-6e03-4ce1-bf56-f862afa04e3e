"""
Setup Admin - Django admin configuration for setup models
Professional admin interface for ERP setup management
"""
from django.contrib import admin
from django.utils.html import format_html
from .models import (
    Country, Currency, Timezone, CompanySetup, BankAccount,
    TaxConfiguration, PaymentTerm, SetupWizardStep, SystemConfiguration,
    SetupTemplate
)

@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'phone_code', 'currency_code', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'code']
    ordering = ['name']

@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'symbol', 'decimal_places', 'is_active']
    list_filter = ['is_active', 'decimal_places']
    search_fields = ['name', 'code']
    ordering = ['name']

@admin.register(Timezone)
class TimezoneAdmin(admin.ModelAdmin):
    list_display = ['name', 'offset', 'description', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'description']
    ordering = ['offset', 'name']

@admin.register(CompanySetup)
class CompanySetupAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'email', 'city', 'country', 'currency',
        'setup_progress_display', 'setup_completed', 'created_at'
    ]
    list_filter = ['setup_completed', 'country', 'currency', 'created_at']
    search_fields = ['name', 'email', 'city']
    readonly_fields = ['setup_progress', 'created_at', 'updated_at']

    def setup_progress_display(self, obj):
        color = 'green' if obj.setup_completed else 'orange'
        return format_html(
            '<span style="color: {};">{} %</span>',
            color,
            obj.setup_progress
        )
    setup_progress_display.short_description = 'Progress'
