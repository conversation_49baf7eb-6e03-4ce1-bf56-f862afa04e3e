"""
Setup Admin - Django admin configuration for setup models
Professional admin interface for ERP setup management
"""
from django.contrib import admin
from django.utils.html import format_html
from .models import (
    Country, Currency, Timezone, CompanySetup, BankAccount,
    TaxConfiguration, WithholdingTaxConfiguration, PaymentTerm, SetupWizardStep, SystemConfiguration,
    SetupTemplate
)

@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'phone_code', 'currency_code', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'code']
    ordering = ['name']

@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'symbol', 'decimal_places', 'is_active']
    list_filter = ['is_active', 'decimal_places']
    search_fields = ['name', 'code']
    ordering = ['name']

@admin.register(Timezone)
class TimezoneAdmin(admin.ModelAdmin):
    list_display = ['name', 'offset', 'description', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'description']
    ordering = ['offset', 'name']

@admin.register(CompanySetup)
class CompanySetupAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'email', 'city', 'country', 'currency',
        'setup_progress_display', 'setup_completed', 'created_at'
    ]
    list_filter = ['setup_completed', 'country', 'currency', 'created_at']
    search_fields = ['name', 'email', 'city']
    readonly_fields = ['setup_progress', 'created_at', 'updated_at']

    def setup_progress_display(self, obj):
        color = 'green' if obj.setup_completed else 'orange'
        return format_html(
            '<span style="color: {};">{} %</span>',
            color,
            obj.setup_progress
        )
    setup_progress_display.short_description = 'Progress'


@admin.register(WithholdingTaxConfiguration)
class WithholdingTaxConfigurationAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'code', 'company', 'rate', 'tax_type', 'applicable_to',
        'threshold_amount', 'is_default', 'is_active'
    ]
    list_filter = ['tax_type', 'applicable_to', 'is_default', 'is_active', 'company']
    search_fields = ['name', 'code', 'company__name']
    ordering = ['company', 'tax_type', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('company', 'name', 'code', 'rate', 'tax_type', 'applicable_to')
        }),
        ('Thresholds & Limits', {
            'fields': ('threshold_amount', 'exemption_limit', 'calculation_base')
        }),
        ('Compliance & Certificates', {
            'fields': ('requires_certificate', 'certificate_series', 'quarterly_return_required', 'challan_required')
        }),
        ('Due Dates', {
            'fields': ('payment_due_date', 'return_due_date')
        }),
        ('Account Mapping', {
            'fields': ('withholding_account',)
        }),
        ('Validity Period', {
            'fields': ('effective_from', 'effective_to', 'is_default', 'is_active')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('company')
