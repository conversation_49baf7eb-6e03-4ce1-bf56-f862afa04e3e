"""
Point of Sale (POS) Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import (
    PosConfig, PosSession, PosPaymentMethod, PosOrder, PosOrderLine,
    PosPayment, PosMakePayment, PosCloseSessionWizard, PosDetails, PosDailyReport
)


@admin.register(PosConfig)
class PosConfigAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'journal_id', 'currency_id', 'cash_control', 'company_id']
    list_filter = ['active', 'cash_control', 'iface_cashdrawer', 'company_id']
    search_fields = ['name']
    # filter_horizontal = ['available_pricelist_ids']
    ordering = ['name']

    fieldsets = (
        ('Basic Configuration', {
            'fields': ('name', 'active')
        }),
        ('Journal Configuration', {
            'fields': ('journal_id', 'invoice_journal_id', 'currency_id')
        }),
        ('Stock Configuration', {
            'fields': ('picking_type_id', 'stock_location_id')
        }),
        # ('Product Configuration', {
        #     'fields': ('available_pricelist_ids', 'pricelist_id')
        # }),
        ('Interface Configuration', {
            'fields': ('iface_cashdrawer', 'iface_electronic_scale', 'iface_customer_facing_display', 'iface_print_via_proxy'),
            'classes': ('collapse',)
        }),
        ('Receipt Configuration', {
            'fields': ('receipt_header', 'receipt_footer'),
            'classes': ('collapse',)
        }),
        ('Session Configuration', {
            'fields': ('cash_control',)
        }),
        ('Sequence', {
            'fields': ('sequence_id', 'sequence_line_id'),
            'classes': ('collapse',)
        }),
    )

    actions = ['open_session']

    def open_session(self, request, queryset):
        opened = 0
        for config in queryset:
            session = config.open_session_cb(request.user)
            if session:
                opened += 1
        self.message_user(request, f'{opened} POS sessions opened successfully.')
    open_session.short_description = "Open POS sessions"


class PosOrderInline(admin.TabularInline):
    model = PosOrder
    extra = 0
    fields = ['name', 'partner_id', 'amount_total', 'state']
    readonly_fields = ['name', 'amount_total']


@admin.register(PosSession)
class PosSessionAdmin(admin.ModelAdmin):
    list_display = ['name', 'config_id', 'user_id', 'state_display', 'start_at', 'stop_at', 'cash_difference_display']
    list_filter = ['state', 'start_at', 'config_id', 'company_id']
    search_fields = ['name', 'user_id__username']
    inlines = [PosOrderInline]
    ordering = ['-start_at']

    fieldsets = (
        ('Session Configuration', {
            'fields': ('config_id', 'name', 'user_id', 'state')
        }),
        ('Dates', {
            'fields': ('start_at', 'stop_at')
        }),
        ('Cash Control', {
            'fields': ('cash_register_balance_start', 'cash_register_balance_end_real',
                      'cash_register_balance_end', 'cash_register_difference')
        }),
        ('Accounting', {
            'fields': ('move_id',),
            'classes': ('collapse',)
        }),
        ('Sequence', {
            'fields': ('sequence_number',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['cash_register_balance_end', 'cash_register_difference', 'move_id']

    def state_display(self, obj):
        colors = {
            'opening_control': 'orange',
            'opened': 'green',
            'closing_control': 'blue',
            'closed': 'gray'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.state, 'black'), obj.get_state_display()
        )
    state_display.short_description = 'State'

    def cash_difference_display(self, obj):
        if obj.cash_register_difference:
            color = 'red' if obj.cash_register_difference < 0 else 'green'
            return format_html('<span style="color: {}; font-weight: bold;">${:,.2f}</span>',
                             color, obj.cash_register_difference)
        return '-'
    cash_difference_display.short_description = 'Cash Difference'

    actions = ['action_open', 'action_close_control', 'action_close']

    def action_open(self, request, queryset):
        opened = 0
        for session in queryset.filter(state='opening_control'):
            if session.action_pos_session_open():
                opened += 1
        self.message_user(request, f'{opened} sessions opened successfully.')
    action_open.short_description = "Open sessions"

    def action_close_control(self, request, queryset):
        closed = 0
        for session in queryset.filter(state='opened'):
            if session.action_pos_session_closing_control():
                closed += 1
        self.message_user(request, f'{closed} sessions moved to closing control successfully.')
    action_close_control.short_description = "Start closing control"

    def action_close(self, request, queryset):
        closed = 0
        for session in queryset.filter(state='closing_control'):
            if session.action_pos_session_close():
                closed += 1
        self.message_user(request, f'{closed} sessions closed successfully.')
    action_close.short_description = "Close sessions"


@admin.register(PosPaymentMethod)
class PosPaymentMethodAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'is_cash_count', 'journal_id', 'company_id']
    list_filter = ['active', 'is_cash_count', 'company_id']
    search_fields = ['name']
    ordering = ['name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'active')
        }),
        ('Payment Configuration', {
            'fields': ('is_cash_count', 'use_payment_terminal')
        }),
        ('Accounting', {
            'fields': ('journal_id', 'outstanding_account_id')
        }),
    )


class PosOrderLineInline(admin.TabularInline):
    model = PosOrderLine
    extra = 0
    fields = ['product_id', 'qty', 'price_unit', 'discount', 'price_subtotal_incl']
    readonly_fields = ['price_subtotal_incl']


class PosPaymentInline(admin.TabularInline):
    model = PosPayment
    extra = 0
    fields = ['payment_method_id', 'amount', 'payment_date']


@admin.register(PosOrder)
class PosOrderAdmin(admin.ModelAdmin):
    list_display = ['name', 'session_id', 'partner_id', 'amount_total_display', 'state_display', 'date_order', 'user_id']
    list_filter = ['state', 'date_order', 'config_id', 'company_id']
    search_fields = ['name', 'pos_reference', 'partner_id__name']
    inlines = [PosOrderLineInline, PosPaymentInline]
    ordering = ['-date_order']

    fieldsets = (
        ('Order Information', {
            'fields': ('name', 'date_order', 'pos_reference')
        }),
        ('Session and Config', {
            'fields': ('session_id', 'config_id')
        }),
        ('Customer', {
            'fields': ('partner_id',)
        }),
        ('Amounts', {
            'fields': ('amount_total', 'amount_tax', 'amount_paid', 'amount_return')
        }),
        ('State', {
            'fields': ('state',)
        }),
        # ('Pricelist', {
        #     'fields': ('pricelist_id', 'fiscal_position_id'),
        #     'classes': ('collapse',)
        # }),
        ('User', {
            'fields': ('user_id',)
        }),
        ('Sequence', {
            'fields': ('sequence_number',),
            'classes': ('collapse',)
        }),
        ('Accounting', {
            'fields': ('account_move', 'picking_id'),
            'classes': ('collapse',)
        }),
        ('Note', {
            'fields': ('note',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['amount_total', 'amount_tax', 'amount_paid', 'amount_return', 'account_move', 'picking_id']

    def amount_total_display(self, obj):
        return format_html('<span style="font-weight: bold; color: green;">${:,.2f}</span>', obj.amount_total)
    amount_total_display.short_description = 'Total'

    def state_display(self, obj):
        colors = {
            'draft': 'gray',
            'cancel': 'red',
            'paid': 'green',
            'done': 'blue',
            'invoiced': 'purple'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.state, 'black'), obj.get_state_display()
        )
    state_display.short_description = 'State'

    actions = ['action_paid', 'action_done', 'action_invoice']

    def action_paid(self, request, queryset):
        paid = 0
        for order in queryset.filter(state='draft'):
            if order.action_pos_order_paid():
                paid += 1
        self.message_user(request, f'{paid} orders marked as paid successfully.')
    action_paid.short_description = "Mark as paid"

    def action_done(self, request, queryset):
        done = 0
        for order in queryset.filter(state='paid'):
            if order.action_pos_order_done():
                done += 1
        self.message_user(request, f'{done} orders posted to accounting successfully.')
    action_done.short_description = "Post to accounting"

    def action_invoice(self, request, queryset):
        invoiced = 0
        for order in queryset.filter(state__in=['paid', 'done']):
            if order.action_pos_order_invoice():
                invoiced += 1
        self.message_user(request, f'{invoiced} orders invoiced successfully.')
    action_invoice.short_description = "Create invoices"


@admin.register(PosOrderLine)
class PosOrderLineAdmin(admin.ModelAdmin):
    list_display = ['order_id', 'product_id', 'qty', 'price_unit', 'discount', 'price_subtotal_incl_display']
    list_filter = ['order_id__date_order', 'product_id__product_tmpl_id__categ_id']
    search_fields = ['order_id__name', 'product_id__name', 'full_product_name']
    filter_horizontal = ['tax_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Order Reference', {
            'fields': ('order_id',)
        }),
        ('Product', {
            'fields': ('product_id', 'full_product_name')
        }),
        ('Quantity and Price', {
            'fields': ('qty', 'price_unit', 'price_subtotal', 'price_subtotal_incl')
        }),
        ('Discount', {
            'fields': ('discount',)
        }),
        ('Tax', {
            'fields': ('tax_ids',)
        }),
    )

    readonly_fields = ['price_subtotal', 'price_subtotal_incl', 'full_product_name']

    def price_subtotal_incl_display(self, obj):
        return format_html('<span style="font-weight: bold;">${:,.2f}</span>', obj.price_subtotal_incl)
    price_subtotal_incl_display.short_description = 'Subtotal'


@admin.register(PosPayment)
class PosPaymentAdmin(admin.ModelAdmin):
    list_display = ['pos_order_id', 'payment_method_id', 'amount_display', 'payment_date', 'session_id']
    list_filter = ['payment_method_id', 'payment_date', 'session_id']
    search_fields = ['pos_order_id__name', 'cardholder_name', 'transaction_id']
    ordering = ['-payment_date']

    fieldsets = (
        ('Order Reference', {
            'fields': ('pos_order_id',)
        }),
        ('Payment Details', {
            'fields': ('amount', 'payment_date', 'payment_method_id')
        }),
        ('Card Details', {
            'fields': ('card_type', 'cardholder_name', 'transaction_id'),
            'classes': ('collapse',)
        }),
        ('Session', {
            'fields': ('session_id',)
        }),
    )

    def amount_display(self, obj):
        return format_html('<span style="font-weight: bold; color: green;">${:,.2f}</span>', obj.amount)
    amount_display.short_description = 'Amount'


@admin.register(PosMakePayment)
class PosMakePaymentAdmin(admin.ModelAdmin):
    list_display = ['amount_display', 'payment_method_id', 'payment_date', 'session_id']
    list_filter = ['payment_method_id', 'payment_date', 'session_id']
    search_fields = ['payment_name']
    ordering = ['-create_date']

    fieldsets = (
        ('Payment Configuration', {
            'fields': ('amount', 'payment_method_id', 'payment_name', 'payment_date')
        }),
        ('Session', {
            'fields': ('session_id',)
        }),
    )

    def amount_display(self, obj):
        return format_html('<span style="font-weight: bold; color: green;">${:,.2f}</span>', obj.amount)
    amount_display.short_description = 'Amount'

    actions = ['process_payments']

    def process_payments(self, request, queryset):
        processed = 0
        for payment in queryset:
            if payment.process_payment():
                processed += 1
        self.message_user(request, f'{processed} payments processed successfully.')
    process_payments.short_description = "Process payments"


@admin.register(PosCloseSessionWizard)
class PosCloseSessionWizardAdmin(admin.ModelAdmin):
    list_display = ['session_id', 'cash_register_balance_end_real_display', 'create_date']
    list_filter = ['create_date']
    search_fields = ['session_id__name']
    ordering = ['-create_date']

    fieldsets = (
        ('Session', {
            'fields': ('session_id',)
        }),
        ('Cash Control', {
            'fields': ('cash_register_balance_end_real',)
        }),
    )

    def cash_register_balance_end_real_display(self, obj):
        return format_html('<span style="font-weight: bold;">${:,.2f}</span>', obj.cash_register_balance_end_real)
    cash_register_balance_end_real_display.short_description = 'Ending Balance'

    actions = ['close_sessions']

    def close_sessions(self, request, queryset):
        closed = 0
        for wizard in queryset:
            if wizard.close_session():
                closed += 1
        self.message_user(request, f'{closed} sessions closed successfully.')
    close_sessions.short_description = "Close sessions"


@admin.register(PosDetails)
class PosDetailsAdmin(admin.ModelAdmin):
    list_display = ['date_start', 'date_stop', 'config_count', 'company_id']
    list_filter = ['date_start', 'date_stop', 'company_id']
    filter_horizontal = ['pos_config_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Date Range', {
            'fields': ('date_start', 'date_stop')
        }),
        ('POS Configuration', {
            'fields': ('pos_config_ids',)
        }),
    )

    def config_count(self, obj):
        count = obj.pos_config_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    config_count.short_description = 'Configs'

    actions = ['generate_reports']

    def generate_reports(self, request, queryset):
        generated = 0
        for detail in queryset:
            report_data = detail.generate_report()
            if report_data:
                generated += 1
        self.message_user(request, f'{generated} POS detail reports generated successfully.')
    generate_reports.short_description = "Generate reports"


@admin.register(PosDailyReport)
class PosDailyReportAdmin(admin.ModelAdmin):
    list_display = ['date', 'pos_config_id', 'total_sales_display', 'total_orders', 'cash_total_display', 'card_total_display']
    list_filter = ['date', 'pos_config_id', 'company_id']
    search_fields = ['pos_config_id__name']
    ordering = ['-date']

    fieldsets = (
        ('Date', {
            'fields': ('date',)
        }),
        ('POS Configuration', {
            'fields': ('pos_config_id',)
        }),
        ('Totals', {
            'fields': ('total_sales', 'total_tax', 'total_discount', 'total_orders')
        }),
        ('Payment Methods', {
            'fields': ('cash_total', 'card_total')
        }),
    )

    readonly_fields = ['total_sales', 'total_tax', 'total_discount', 'total_orders', 'cash_total', 'card_total']

    def total_sales_display(self, obj):
        return format_html('<span style="font-weight: bold; color: green;">${:,.2f}</span>', obj.total_sales)
    total_sales_display.short_description = 'Total Sales'

    def cash_total_display(self, obj):
        return format_html('<span style="font-weight: bold; color: blue;">${:,.2f}</span>', obj.cash_total)
    cash_total_display.short_description = 'Cash Total'

    def card_total_display(self, obj):
        return format_html('<span style="font-weight: bold; color: purple;">${:,.2f}</span>', obj.card_total)
    card_total_display.short_description = 'Card Total'

    actions = ['compute_daily_totals']

    def compute_daily_totals(self, request, queryset):
        computed = 0
        for report in queryset:
            totals = report.compute_totals()
            if totals:
                computed += 1
        self.message_user(request, f'{computed} daily reports computed successfully.')
    compute_daily_totals.short_description = "Compute totals"


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - Point of Sale"
