"""
Manufacturing Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from datetime import date
from .models import (
    MrpWorkcenter, Mrp<PERSON><PERSON>ing, MrpRoutingWorkcenter, MrpBom, MrpBomLine,
    MrpProduction, MrpWorkorder, QualityPoint, QualityCheck
)


@admin.register(MrpWorkcenter)
class MrpWorkcenterAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'capacity', 'time_efficiency_display', 'costs_hour_display', 'sequence']
    list_filter = ['active', 'company_id']
    search_fields = ['name', 'note']
    ordering = ['sequence', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'active', 'sequence', 'color')
        }),
        ('Description', {
            'fields': ('note',)
        }),
        ('Capacity & Time', {
            'fields': ('capacity', 'time_efficiency', 'default_capacity')
        }),
        ('Costs', {
            'fields': ('costs_hour', 'time_start', 'time_stop')
        }),
        ('Calendar', {
            'fields': ('resource_calendar_id',),
            'classes': ('collapse',)
        }),
    )

    def time_efficiency_display(self, obj):
        return format_html('<span style="font-weight: bold;">{:.1f}%</span>', obj.time_efficiency)
    time_efficiency_display.short_description = 'Efficiency'

    def costs_hour_display(self, obj):
        return format_html('<span style="color: green; font-weight: bold;">${:.2f}</span>', obj.costs_hour)
    costs_hour_display.short_description = 'Cost/Hour'


class MrpRoutingWorkcenterInline(admin.TabularInline):
    model = MrpRoutingWorkcenter
    extra = 0
    fields = ['sequence', 'name', 'workcenter_id', 'time_cycle', 'batch_size']
    ordering = ['sequence']


@admin.register(MrpRouting)
class MrpRoutingAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'sequence', 'location_id', 'operation_count']
    list_filter = ['active', 'company_id']
    search_fields = ['name', 'note']
    inlines = [MrpRoutingWorkcenterInline]
    ordering = ['sequence', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'active', 'sequence')
        }),
        ('Description', {
            'fields': ('note',)
        }),
        ('Location', {
            'fields': ('location_id',)
        }),
    )

    def operation_count(self, obj):
        count = obj.operation_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    operation_count.short_description = 'Operations'


@admin.register(MrpRoutingWorkcenter)
class MrpRoutingWorkcenterAdmin(admin.ModelAdmin):
    list_display = ['name', 'routing_id', 'workcenter_id', 'sequence', 'time_cycle_display', 'batch_size']
    list_filter = ['time_mode', 'batch', 'workcenter_id']
    search_fields = ['name', 'note', 'routing_id__name']
    ordering = ['routing_id', 'sequence']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'sequence', 'routing_id', 'workcenter_id')
        }),
        ('Time', {
            'fields': ('time_mode', 'time_mode_batch', 'time_cycle_manual', 'time_cycle')
        }),
        ('Batch', {
            'fields': ('batch', 'batch_size')
        }),
        ('Description', {
            'fields': ('note',)
        }),
        ('Worksheet', {
            'fields': ('worksheet_type', 'worksheet'),
            'classes': ('collapse',)
        }),
    )

    def time_cycle_display(self, obj):
        return format_html('<span style="font-weight: bold;">{:.1f} min</span>', obj.time_cycle)
    time_cycle_display.short_description = 'Duration'


class MrpBomLineInline(admin.TabularInline):
    model = MrpBomLine
    extra = 0
    fields = ['sequence', 'product_id', 'product_qty', 'operation_id']
    ordering = ['sequence']


@admin.register(MrpBom)
class MrpBomAdmin(admin.ModelAdmin):
    list_display = ['display_name', 'product_tmpl_id', 'product_qty', 'type', 'routing_id', 'active', 'component_count']
    list_filter = ['type', 'active', 'consumption', 'company_id']
    search_fields = ['product_tmpl_id__name', 'product_id__name']
    inlines = [MrpBomLineInline]
    ordering = ['product_tmpl_id__name']

    fieldsets = (
        ('Product', {
            'fields': ('product_tmpl_id', 'product_id', 'product_qty', 'product_uom_id')
        }),
        ('BoM Configuration', {
            'fields': ('type', 'routing_id', 'sequence', 'active')
        }),
        ('Manufacturing', {
            'fields': ('picking_type_id', 'consumption', 'ready_to_produce'),
            'classes': ('collapse',)
        }),
    )

    def component_count(self, obj):
        count = obj.bom_line_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    component_count.short_description = 'Components'


@admin.register(MrpBomLine)
class MrpBomLineAdmin(admin.ModelAdmin):
    list_display = ['bom_id', 'product_id', 'product_qty', 'sequence', 'operation_id', 'manual_consumption']
    list_filter = ['manual_consumption', 'bom_id__type']
    search_fields = ['product_id__name', 'bom_id__product_tmpl_id__name']
    ordering = ['bom_id', 'sequence']

    fieldsets = (
        ('BoM', {
            'fields': ('bom_id', 'sequence')
        }),
        ('Component', {
            'fields': ('product_id', 'product_qty', 'product_uom_id')
        }),
        ('Operation', {
            'fields': ('operation_id', 'manual_consumption')
        }),
    )


class MrpWorkorderInline(admin.TabularInline):
    model = MrpWorkorder
    extra = 0
    fields = ['sequence', 'name', 'workcenter_id', 'state', 'qty_produced', 'duration']
    readonly_fields = ['duration']
    ordering = ['sequence']


@admin.register(MrpProduction)
class MrpProductionAdmin(admin.ModelAdmin):
    list_display = ['name', 'product_id', 'product_qty', 'state_display', 'priority_display', 'date_planned_start', 'user_id']
    list_filter = ['state', 'priority', 'date_planned_start', 'company_id']
    search_fields = ['name', 'origin', 'product_id__name']
    inlines = [MrpWorkorderInline]
    ordering = ['-date_planned_start']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'origin', 'state', 'priority')
        }),
        ('Product', {
            'fields': ('product_id', 'product_tmpl_id', 'product_qty', 'product_uom_id')
        }),
        ('Production', {
            'fields': ('qty_producing', 'qty_produced', 'bom_id', 'routing_id')
        }),
        ('Dates', {
            'fields': ('date_planned_start', 'date_planned_finished', 'date_start', 'date_finished', 'date_deadline')
        }),
        ('Locations', {
            'fields': ('location_src_id', 'location_dest_id', 'picking_type_id')
        }),
        ('Management', {
            'fields': ('user_id', 'analytic_account_id'),
            'classes': ('collapse',)
        }),
        ('Advanced', {
            'fields': ('procurement_group_id',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['qty_produced', 'date_start', 'date_finished']

    def state_display(self, obj):
        colors = {
            'draft': 'gray',
            'confirmed': 'blue',
            'progress': 'orange',
            'to_close': 'purple',
            'done': 'green',
            'cancel': 'red'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.state, 'black'), obj.get_state_display()
        )
    state_display.short_description = 'State'

    def priority_display(self, obj):
        colors = {'0': 'gray', '1': 'blue', '2': 'orange', '3': 'red'}
        labels = {'0': 'Not urgent', '1': 'Normal', '2': 'Urgent', '3': 'Very Urgent'}
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.priority, 'black'), labels.get(obj.priority, obj.priority)
        )
    priority_display.short_description = 'Priority'

    actions = ['action_confirm', 'action_mark_done', 'action_cancel']

    def action_confirm(self, request, queryset):
        confirmed = 0
        for production in queryset.filter(state='draft'):
            if production.action_confirm():
                confirmed += 1
        self.message_user(request, f'{confirmed} manufacturing orders confirmed successfully.')
    action_confirm.short_description = "Confirm manufacturing orders"

    def action_mark_done(self, request, queryset):
        done = 0
        for production in queryset.filter(state__in=['progress', 'to_close']):
            if production.button_mark_done():
                done += 1
        self.message_user(request, f'{done} manufacturing orders marked as done successfully.')
    action_mark_done.short_description = "Mark manufacturing orders as done"

    def action_cancel(self, request, queryset):
        cancelled = 0
        for production in queryset.exclude(state__in=['done', 'cancel']):
            if production.action_cancel():
                cancelled += 1
        self.message_user(request, f'{cancelled} manufacturing orders cancelled successfully.')
    action_cancel.short_description = "Cancel manufacturing orders"


@admin.register(MrpWorkorder)
class MrpWorkorderAdmin(admin.ModelAdmin):
    list_display = ['name', 'production_id', 'workcenter_id', 'state_display', 'qty_produced', 'duration_display', 'date_planned_start']
    list_filter = ['state', 'workcenter_id', 'date_planned_start', 'company_id']
    search_fields = ['name', 'production_id__name', 'product_id__name']
    ordering = ['date_planned_start', 'sequence']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'production_id', 'sequence', 'state')
        }),
        ('Work Center', {
            'fields': ('workcenter_id', 'operation_id')
        }),
        ('Product', {
            'fields': ('product_id', 'qty_production', 'qty_producing', 'qty_produced')
        }),
        ('Dates', {
            'fields': ('date_planned_start', 'date_planned_finished', 'date_start', 'date_finished')
        }),
        ('Duration', {
            'fields': ('duration_expected', 'duration', 'costs_hour')
        }),
        ('Next Operation', {
            'fields': ('next_work_order_id',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['qty_produced', 'date_start', 'date_finished', 'duration']

    def state_display(self, obj):
        colors = {
            'pending': 'gray',
            'waiting': 'orange',
            'ready': 'blue',
            'progress': 'purple',
            'done': 'green',
            'cancel': 'red'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.state, 'black'), obj.get_state_display()
        )
    state_display.short_description = 'State'

    def duration_display(self, obj):
        if obj.duration:
            hours = int(obj.duration // 60)
            minutes = int(obj.duration % 60)
            return format_html('<span style="font-weight: bold;">{}h {}m</span>', hours, minutes)
        return '-'
    duration_display.short_description = 'Duration'

    actions = ['action_start', 'action_finish']

    def action_start(self, request, queryset):
        started = 0
        for workorder in queryset.filter(state='ready'):
            if workorder.button_start():
                started += 1
        self.message_user(request, f'{started} work orders started successfully.')
    action_start.short_description = "Start work orders"

    def action_finish(self, request, queryset):
        finished = 0
        for workorder in queryset.filter(state='progress'):
            if workorder.button_finish():
                finished += 1
        self.message_user(request, f'{finished} work orders finished successfully.')
    action_finish.short_description = "Finish work orders"


@admin.register(QualityPoint)
class QualityPointAdmin(admin.ModelAdmin):
    list_display = ['title', 'test_type', 'operation_id', 'active', 'sequence']
    list_filter = ['test_type', 'active', 'company_id']
    search_fields = ['title', 'note']
    filter_horizontal = ['product_ids', 'product_tmpl_ids']
    ordering = ['sequence', 'title']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'test_type', 'active', 'sequence')
        }),
        ('Products', {
            'fields': ('product_ids', 'product_tmpl_ids')
        }),
        ('Operation', {
            'fields': ('operation_id',)
        }),
        ('Instructions', {
            'fields': ('note',)
        }),
        ('Measure', {
            'fields': ('norm', 'tolerance_min', 'tolerance_max', 'norm_unit'),
            'classes': ('collapse',)
        }),
    )


@admin.register(QualityCheck)
class QualityCheckAdmin(admin.ModelAdmin):
    list_display = ['point_id', 'product_id', 'production_id', 'quality_state_display', 'measure_display', 'user_id']
    list_filter = ['quality_state', 'measure_success', 'company_id']
    search_fields = ['point_id__title', 'product_id__name', 'production_id__name']
    ordering = ['-create_date']

    fieldsets = (
        ('Quality Point', {
            'fields': ('point_id', 'user_id')
        }),
        ('Production', {
            'fields': ('production_id', 'workorder_id')
        }),
        ('Product', {
            'fields': ('product_id',)
        }),
        ('Quality', {
            'fields': ('quality_state', 'measure', 'measure_success')
        }),
        ('Note', {
            'fields': ('note',)
        }),
    )

    def quality_state_display(self, obj):
        colors = {'none': 'orange', 'pass': 'green', 'fail': 'red'}
        labels = {'none': 'To do', 'pass': 'Passed', 'fail': 'Failed'}
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.quality_state, 'black'), labels.get(obj.quality_state, obj.quality_state)
        )
    quality_state_display.short_description = 'Quality State'

    def measure_display(self, obj):
        if obj.measure is not None:
            color = 'green' if obj.measure_success == 'pass' else 'red' if obj.measure_success == 'fail' else 'black'
            return format_html('<span style="color: {}; font-weight: bold;">{}</span>', color, obj.measure)
        return '-'
    measure_display.short_description = 'Measure'

    actions = ['action_pass', 'action_fail']

    def action_pass(self, request, queryset):
        passed = 0
        for check in queryset:
            if check.action_pass():
                passed += 1
        self.message_user(request, f'{passed} quality checks marked as passed successfully.')
    action_pass.short_description = "Mark quality checks as passed"

    def action_fail(self, request, queryset):
        failed = 0
        for check in queryset:
            if check.action_fail():
                failed += 1
        self.message_user(request, f'{failed} quality checks marked as failed successfully.')
    action_fail.short_description = "Mark quality checks as failed"


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - Manufacturing"
