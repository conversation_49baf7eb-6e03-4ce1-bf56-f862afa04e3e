import React, { useState, useEffect } from 'react';
import { Table, Button, Typography, Tag, Space, Avatar } from 'antd';
import { PlusOutlined, EditOutlined, ShopOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Vendors = () => {
  const [vendors, setVendors] = useState([]);
  const [loading, setLoading] = useState(false);

  // Mock data
  const mockVendors = [
    {
      id: 1,
      name: 'Office Supplies Co.',
      email: '<EMAIL>',
      phone: '+92-42-1234567',
      city: 'Lahore',
      supplier_rank: 1,
      outstanding_balance: 15000,
      active: true,
    },
    {
      id: 2,
      name: 'Tech Solutions Ltd.',
      email: '<EMAIL>',
      phone: '+92-21-7654321',
      city: 'Karachi',
      supplier_rank: 1,
      outstanding_balance: 0,
      active: true,
    },
  ];

  const columns = [
    {
      title: 'Vendor',
      dataIndex: 'name',
      key: 'name',
      render: (name) => (
        <Space>
          <Avatar icon={<ShopOutlined />} />
          {name}
        </Space>
      ),
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: 'City',
      dataIndex: 'city',
      key: 'city',
    },
    {
      title: 'Outstanding',
      dataIndex: 'outstanding_balance',
      key: 'outstanding_balance',
      render: (balance) => (
        <span className={balance > 0 ? 'balance-negative' : 'balance-positive'}>
          PKR {balance.toLocaleString()}
        </span>
      ),
      align: 'right',
    },
    {
      title: 'Status',
      dataIndex: 'active',
      key: 'active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <Space>
          <Button type="text" icon={<EditOutlined />} size="small" />
        </Space>
      ),
    },
  ];

  useEffect(() => {
    setVendors(mockVendors);
  }, []);

  return (
    <div>
      <div className="page-header">
        <Title level={2}>Vendors</Title>
        <p>Manage your vendor database</p>
      </div>

      <div className="action-buttons">
        <Button type="primary" icon={<PlusOutlined />}>
          Add Vendor
        </Button>
      </div>

      <div className="table-container">
        <Table
          columns={columns}
          dataSource={vendors}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} vendors`,
          }}
        />
      </div>
    </div>
  );
};

export default Vendors;
