const fs = require('fs');
const path = require('path');

const setupComponents = [
  { name: 'TaxesSetup', title: 'Taxes Configuration', icon: 'DollarOutlined' },
  { name: 'CurrenciesSetup', title: 'Currencies Setup', icon: 'GlobalOutlined' },
  { name: 'PaymentTermsSetup', title: 'Payment Terms Setup', icon: 'CreditCardOutlined' },
  { name: 'JournalsSetup', title: 'Journals Setup', icon: 'BookOutlined' },
  { name: 'BankAccountsSetup', title: 'Bank Accounts Setup', icon: 'BankOutlined' },
  { name: 'ProductCategoriesSetup', title: 'Product Categories Setup', icon: 'TagsOutlined' },
  { name: 'WarehousesSetup', title: 'Warehouses Setup', icon: 'HomeOutlined' },
  { name: 'DepartmentsSetup', title: 'Departments Setup', icon: 'TeamOutlined' },
  { name: 'PositionsSetup', title: 'Employee Positions Setup', icon: 'UserOutlined' },
  { name: 'ProjectStagesSetup', title: 'Project Stages Setup', icon: 'ProjectOutlined' },
  { name: 'ManufacturingSetup', title: 'Manufacturing Setup', icon: 'ToolOutlined' }
];

const componentTemplate = (name, title, icon) => `/**
 * ${title} Component
 */
import React from 'react';
import { Card, Typography, Alert } from 'antd';
import { ${icon} } from '@ant-design/icons';

const { Title } = Typography;

const ${name} = () => {
  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <${icon} style={{ marginRight: 8 }} />
        ${title}
      </Title>
      
      <Card>
        <Alert
          message="Coming Soon"
          description="${title} will be available soon."
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
};

export default ${name};
`;

// Create setup components directory if it doesn't exist
const setupDir = path.join(__dirname, 'src', 'components', 'setup');
if (!fs.existsSync(setupDir)) {
  fs.mkdirSync(setupDir, { recursive: true });
}

// Generate each component
setupComponents.forEach(({ name, title, icon }) => {
  const filePath = path.join(setupDir, `${name}.jsx`);
  const content = componentTemplate(name, title, icon);
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ Created ${name}.jsx`);
});

console.log('🎉 All setup components created successfully!');
