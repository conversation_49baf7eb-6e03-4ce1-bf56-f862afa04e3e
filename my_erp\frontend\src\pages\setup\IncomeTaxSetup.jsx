/**
 * Withholding Tax Setup Page - Manage withholding tax (TDS) configurations
 */
import React, { useState, useEffect } from 'react';
import { Table, Space, Modal, message, Popconfirm, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { <PERSON>Header, PageContent, QuickActions } from '../../components/shared/layout/PageLayout';
import { setupAPI } from '../../services/api';
import IncomeTaxForm from './components/IncomeTaxForm';

const IncomeTaxSetup = () => {
  const [incomeTaxes, setIncomeTaxes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTax, setEditingTax] = useState(null);
  const [formLoading, setFormLoading] = useState(false);

  const [selectedCompany] = useState(1); // TODO: Get from context

  // Fetch withholding tax configurations
  const fetchIncomeTaxes = async () => {
    setLoading(true);
    try {
      const response = await setupAPI.getWithholdingTaxConfigurations(selectedCompany);
      setIncomeTaxes(response.data?.results || response.data || []);
    } catch (error) {
      message.error('Failed to fetch withholding tax configurations');
      console.error('Fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchIncomeTaxes();
  }, [selectedCompany]);

  // Handle create
  const handleCreate = () => {
    setEditingTax(null);
    setModalVisible(true);
  };

  // Handle edit
  const handleEdit = (tax) => {
    setEditingTax(tax);
    setModalVisible(true);
  };

  // Handle delete
  const handleDelete = async (id) => {
    try {
      await setupAPI.deleteWithholdingTaxConfiguration(id);
      message.success('Withholding tax configuration deleted successfully');
      fetchIncomeTaxes();
    } catch (error) {
      message.error('Failed to delete withholding tax configuration');
      console.error('Delete error:', error);
    }
  };

  // Handle form submit
  const handleFormSubmit = async (values) => {
    setFormLoading(true);
    try {
      const taxData = {
        ...values,
        company: selectedCompany
      };

      console.log('=== FORM DATA BEING SENT ===');
      console.log(JSON.stringify(taxData, null, 2));

      if (editingTax) {
        await setupAPI.updateWithholdingTaxConfiguration(editingTax.id, taxData);
        message.success('Withholding tax configuration updated successfully');
      } else {
        await setupAPI.createWithholdingTaxConfiguration(taxData);
        message.success('Withholding tax configuration created successfully');
      }
      setModalVisible(false);
      setEditingTax(null);
      fetchIncomeTaxes();
    } catch (error) {
      message.error(`Failed to ${editingTax ? 'update' : 'create'} income tax configuration`);
      console.error('Form submit error:', error);
    } finally {
      setFormLoading(false);
    }
  };

  // Table columns
  const columns = [
    {
      title: 'Tax Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Tax Code',
      dataIndex: 'code',
      key: 'code',
      render: (code) => <Tag color="blue">{code}</Tag>,
    },
    {
      title: 'Tax Type',
      dataIndex: 'tax_type',
      key: 'tax_type',
      render: (type) => (
        <Tag color={type === 'federal' ? 'red' : type === 'state' ? 'orange' : 'green'}>
          {type?.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Rate (%)',
      dataIndex: 'rate',
      key: 'rate',
      render: (rate) => `${rate}%`,
      sorter: (a, b) => a.rate - b.rate,
    },
    {
      title: 'Min Income',
      dataIndex: 'min_income',
      key: 'min_income',
      render: (amount) => amount ? `$${amount.toLocaleString()}` : '-',
    },
    {
      title: 'Max Income',
      dataIndex: 'max_income',
      key: 'max_income',
      render: (amount) => amount ? `$${amount.toLocaleString()}` : 'No Limit',
    },
    {
      title: 'Status',
      dataIndex: 'active',
      key: 'active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <QuickActions.Edit
            onClick={() => handleEdit(record)}
            disabled={loading}
          />
          <Popconfirm
            title="Are you sure you want to delete this income tax?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <QuickActions.Delete disabled={loading} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const breadcrumbs = [
    { title: 'Setup', href: '/setup' },
    { title: 'Income Tax Setup' }
  ];

  const actions = [
    {
      label: 'New Income Tax',
      type: 'primary',
      icon: <PlusOutlined />,
      onClick: handleCreate
    }
  ];

  return (
    <>
      <PageHeader
        title="Income Tax Setup"
        subtitle="Configure income tax rates, brackets, and deductions"
        breadcrumbs={breadcrumbs}
        actions={actions}
      />
      
      <PageContent>
        <Table
          columns={columns}
          dataSource={incomeTaxes}
          loading={loading}
          rowKey="id"
          pagination={{
            total: incomeTaxes.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} income taxes`,
          }}
          scroll={{ x: 1000 }}
        />
      </PageContent>

      <Modal
        title={editingTax ? 'Edit Income Tax' : 'Create New Income Tax'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
        destroyOnClose
      >
        <IncomeTaxForm
          initialValues={editingTax}
          onSubmit={handleFormSubmit}
          onCancel={() => setModalVisible(false)}
          loading={formLoading}
        />
      </Modal>
    </>
  );
};

export default IncomeTaxSetup;
