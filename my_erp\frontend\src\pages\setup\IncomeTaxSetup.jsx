/**
 * Income Tax Setup Page - Manage income tax rates and brackets
 */
import React, { useState, useEffect } from 'react';
import { Table, Space, Modal, message, Popconfirm, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { PageHeader, PageContent, QuickActions } from '../../components/shared/layout/PageLayout';
import { setupAPI } from '../../services/api';
import IncomeTaxForm from './components/IncomeTaxForm';

const IncomeTaxSetup = () => {
  const [incomeTaxes, setIncomeTaxes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTax, setEditingTax] = useState(null);
  const [formLoading, setFormLoading] = useState(false);

  // Fetch income taxes
  const fetchIncomeTaxes = async () => {
    setLoading(true);
    try {
      const response = await setupAPI.getIncomeTaxes();
      setIncomeTaxes(response.data || []);
    } catch (error) {
      message.error('Failed to fetch income taxes');
      console.error('Fetch income taxes error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchIncomeTaxes();
  }, []);

  // Handle create
  const handleCreate = () => {
    setEditingTax(null);
    setModalVisible(true);
  };

  // Handle edit
  const handleEdit = (tax) => {
    setEditingTax(tax);
    setModalVisible(true);
  };

  // Handle delete
  const handleDelete = async (id) => {
    try {
      await setupAPI.deleteIncomeTax(id);
      message.success('Income tax deleted successfully');
      fetchIncomeTaxes();
    } catch (error) {
      message.error('Failed to delete income tax');
      console.error('Delete income tax error:', error);
    }
  };

  // Handle form submit
  const handleFormSubmit = async (values) => {
    setFormLoading(true);
    try {
      if (editingTax) {
        await setupAPI.updateIncomeTax(editingTax.id, values);
        message.success('Income tax updated successfully');
      } else {
        await setupAPI.createIncomeTax(values);
        message.success('Income tax created successfully');
      }
      setModalVisible(false);
      fetchIncomeTaxes();
    } catch (error) {
      message.error(`Failed to ${editingTax ? 'update' : 'create'} income tax`);
      console.error('Form submit error:', error);
    } finally {
      setFormLoading(false);
    }
  };

  // Table columns
  const columns = [
    {
      title: 'Tax Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Tax Code',
      dataIndex: 'code',
      key: 'code',
      render: (code) => <Tag color="blue">{code}</Tag>,
    },
    {
      title: 'Tax Type',
      dataIndex: 'tax_type',
      key: 'tax_type',
      render: (type) => (
        <Tag color={type === 'federal' ? 'red' : type === 'state' ? 'orange' : 'green'}>
          {type?.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Rate (%)',
      dataIndex: 'rate',
      key: 'rate',
      render: (rate) => `${rate}%`,
      sorter: (a, b) => a.rate - b.rate,
    },
    {
      title: 'Min Income',
      dataIndex: 'min_income',
      key: 'min_income',
      render: (amount) => amount ? `$${amount.toLocaleString()}` : '-',
    },
    {
      title: 'Max Income',
      dataIndex: 'max_income',
      key: 'max_income',
      render: (amount) => amount ? `$${amount.toLocaleString()}` : 'No Limit',
    },
    {
      title: 'Status',
      dataIndex: 'active',
      key: 'active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <QuickActions.Edit
            onClick={() => handleEdit(record)}
            disabled={loading}
          />
          <Popconfirm
            title="Are you sure you want to delete this income tax?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <QuickActions.Delete disabled={loading} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const breadcrumbs = [
    { title: 'Setup', href: '/setup' },
    { title: 'Income Tax Setup' }
  ];

  const actions = [
    {
      label: 'New Income Tax',
      type: 'primary',
      icon: <PlusOutlined />,
      onClick: handleCreate
    }
  ];

  return (
    <>
      <PageHeader
        title="Income Tax Setup"
        subtitle="Configure income tax rates, brackets, and deductions"
        breadcrumbs={breadcrumbs}
        actions={actions}
      />
      
      <PageContent>
        <Table
          columns={columns}
          dataSource={incomeTaxes}
          loading={loading}
          rowKey="id"
          pagination={{
            total: incomeTaxes.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} income taxes`,
          }}
          scroll={{ x: 1000 }}
        />
      </PageContent>

      <Modal
        title={editingTax ? 'Edit Income Tax' : 'Create New Income Tax'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
        destroyOnClose
      >
        <IncomeTaxForm
          initialValues={editingTax}
          onSubmit={handleFormSubmit}
          onCancel={() => setModalVisible(false)}
          loading={formLoading}
        />
      </Modal>
    </>
  );
};

export default IncomeTaxSetup;
