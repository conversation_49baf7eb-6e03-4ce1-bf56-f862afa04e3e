#!/usr/bin/env python
"""
Create initial setup data for the ERP system
Including countries, currencies, timezones, and setup templates
"""
import os
import sys
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from setup.models import Country, Currency, Timezone, SetupTemplate

def create_countries():
    """Create initial country data"""
    countries_data = [
        {'name': 'Pakistan', 'code': 'PK', 'phone_code': '+92', 'currency_code': 'PKR'},
        {'name': 'United States', 'code': 'US', 'phone_code': '+1', 'currency_code': 'USD'},
        {'name': 'United Kingdom', 'code': 'GB', 'phone_code': '+44', 'currency_code': 'GBP'},
        {'name': 'Canada', 'code': 'CA', 'phone_code': '+1', 'currency_code': 'CAD'},
        {'name': 'Australia', 'code': 'AU', 'phone_code': '+61', 'currency_code': 'AUD'},
        {'name': 'Germany', 'code': 'DE', 'phone_code': '+49', 'currency_code': 'EUR'},
        {'name': 'France', 'code': 'FR', 'phone_code': '+33', 'currency_code': 'EUR'},
        {'name': 'India', 'code': 'IN', 'phone_code': '+91', 'currency_code': 'INR'},
        {'name': 'China', 'code': 'CN', 'phone_code': '+86', 'currency_code': 'CNY'},
        {'name': 'Japan', 'code': 'JP', 'phone_code': '+81', 'currency_code': 'JPY'},
        {'name': 'United Arab Emirates', 'code': 'AE', 'phone_code': '+971', 'currency_code': 'AED'},
        {'name': 'Saudi Arabia', 'code': 'SA', 'phone_code': '+966', 'currency_code': 'SAR'},
    ]
    
    for country_data in countries_data:
        country, created = Country.objects.get_or_create(
            code=country_data['code'],
            defaults=country_data
        )
        if created:
            print(f"✅ Created country: {country.name}")
        else:
            print(f"⚠️ Country already exists: {country.name}")

def create_currencies():
    """Create initial currency data"""
    currencies_data = [
        {'name': 'Pakistani Rupee', 'code': 'PKR', 'symbol': 'Rs', 'decimal_places': 2},
        {'name': 'US Dollar', 'code': 'USD', 'symbol': '$', 'decimal_places': 2},
        {'name': 'British Pound', 'code': 'GBP', 'symbol': '£', 'decimal_places': 2},
        {'name': 'Euro', 'code': 'EUR', 'symbol': '€', 'decimal_places': 2},
        {'name': 'Canadian Dollar', 'code': 'CAD', 'symbol': 'C$', 'decimal_places': 2},
        {'name': 'Australian Dollar', 'code': 'AUD', 'symbol': 'A$', 'decimal_places': 2},
        {'name': 'Indian Rupee', 'code': 'INR', 'symbol': '₹', 'decimal_places': 2},
        {'name': 'Chinese Yuan', 'code': 'CNY', 'symbol': '¥', 'decimal_places': 2},
        {'name': 'Japanese Yen', 'code': 'JPY', 'symbol': '¥', 'decimal_places': 0},
        {'name': 'UAE Dirham', 'code': 'AED', 'symbol': 'د.إ', 'decimal_places': 2},
        {'name': 'Saudi Riyal', 'code': 'SAR', 'symbol': 'ر.س', 'decimal_places': 2},
    ]
    
    for currency_data in currencies_data:
        currency, created = Currency.objects.get_or_create(
            code=currency_data['code'],
            defaults=currency_data
        )
        if created:
            print(f"✅ Created currency: {currency.name}")
        else:
            print(f"⚠️ Currency already exists: {currency.name}")

def create_timezones():
    """Create initial timezone data"""
    timezones_data = [
        {'name': 'Asia/Karachi', 'offset': '+05:00', 'description': 'Pakistan Standard Time'},
        {'name': 'America/New_York', 'offset': '-05:00', 'description': 'Eastern Standard Time'},
        {'name': 'America/Los_Angeles', 'offset': '-08:00', 'description': 'Pacific Standard Time'},
        {'name': 'Europe/London', 'offset': '+00:00', 'description': 'Greenwich Mean Time'},
        {'name': 'Europe/Paris', 'offset': '+01:00', 'description': 'Central European Time'},
        {'name': 'Asia/Dubai', 'offset': '+04:00', 'description': 'Gulf Standard Time'},
        {'name': 'Asia/Riyadh', 'offset': '+03:00', 'description': 'Arabia Standard Time'},
        {'name': 'Asia/Kolkata', 'offset': '+05:30', 'description': 'India Standard Time'},
        {'name': 'Asia/Shanghai', 'offset': '+08:00', 'description': 'China Standard Time'},
        {'name': 'Asia/Tokyo', 'offset': '+09:00', 'description': 'Japan Standard Time'},
        {'name': 'Australia/Sydney', 'offset': '+10:00', 'description': 'Australian Eastern Standard Time'},
    ]
    
    for timezone_data in timezones_data:
        timezone, created = Timezone.objects.get_or_create(
            name=timezone_data['name'],
            defaults=timezone_data
        )
        if created:
            print(f"✅ Created timezone: {timezone.name}")
        else:
            print(f"⚠️ Timezone already exists: {timezone.name}")

def create_setup_templates():
    """Create setup templates for different business types"""
    templates_data = [
        {
            'name': 'Small Business',
            'description': 'Basic setup for small businesses',
            'business_type': 'small_business',
            'template_data': json.dumps({
                'company': {
                    'decimal_precision': 2,
                    'thousands_separator': ',',
                    'decimal_separator': '.',
                    'date_format': 'DD/MM/YYYY',
                    'time_format': '24'
                },
                'taxes': [
                    {'name': 'Sales Tax', 'rate': 17.0, 'tax_type': 'sales'},
                    {'name': 'Service Tax', 'rate': 16.0, 'tax_type': 'sales'}
                ],
                'payment_terms': [
                    {'name': 'Immediate Payment', 'days': 0, 'is_default': True},
                    {'name': 'Net 15', 'days': 15},
                    {'name': 'Net 30', 'days': 30}
                ]
            })
        },
        {
            'name': 'Trading Company',
            'description': 'Setup for import/export and trading businesses',
            'business_type': 'trading',
            'template_data': json.dumps({
                'company': {
                    'decimal_precision': 2,
                    'thousands_separator': ',',
                    'decimal_separator': '.',
                    'date_format': 'DD/MM/YYYY',
                    'time_format': '24'
                },
                'taxes': [
                    {'name': 'Sales Tax', 'rate': 17.0, 'tax_type': 'sales'},
                    {'name': 'Import Duty', 'rate': 25.0, 'tax_type': 'purchase'},
                    {'name': 'Withholding Tax', 'rate': 1.0, 'tax_type': 'purchase'}
                ],
                'payment_terms': [
                    {'name': 'Cash on Delivery', 'days': 0, 'is_default': True},
                    {'name': 'Net 30', 'days': 30},
                    {'name': 'Net 60', 'days': 60},
                    {'name': 'Net 90', 'days': 90}
                ]
            })
        },
        {
            'name': 'Manufacturing',
            'description': 'Setup for manufacturing businesses',
            'business_type': 'manufacturing',
            'template_data': json.dumps({
                'company': {
                    'decimal_precision': 3,
                    'thousands_separator': ',',
                    'decimal_separator': '.',
                    'date_format': 'DD/MM/YYYY',
                    'time_format': '24'
                },
                'taxes': [
                    {'name': 'Sales Tax', 'rate': 17.0, 'tax_type': 'sales'},
                    {'name': 'Input Tax', 'rate': 17.0, 'tax_type': 'purchase'},
                    {'name': 'Excise Duty', 'rate': 10.0, 'tax_type': 'sales'}
                ],
                'payment_terms': [
                    {'name': 'Net 15', 'days': 15, 'is_default': True},
                    {'name': 'Net 30', 'days': 30},
                    {'name': 'Net 45', 'days': 45}
                ]
            })
        },
        {
            'name': 'Service Company',
            'description': 'Setup for service-based businesses',
            'business_type': 'services',
            'template_data': json.dumps({
                'company': {
                    'decimal_precision': 2,
                    'thousands_separator': ',',
                    'decimal_separator': '.',
                    'date_format': 'DD/MM/YYYY',
                    'time_format': '24'
                },
                'taxes': [
                    {'name': 'Service Tax', 'rate': 16.0, 'tax_type': 'sales'},
                    {'name': 'Withholding Tax', 'rate': 8.0, 'tax_type': 'purchase'}
                ],
                'payment_terms': [
                    {'name': 'Net 15', 'days': 15, 'is_default': True},
                    {'name': 'Net 30', 'days': 30}
                ]
            })
        }
    ]
    
    for template_data in templates_data:
        template, created = SetupTemplate.objects.get_or_create(
            name=template_data['name'],
            business_type=template_data['business_type'],
            defaults=template_data
        )
        if created:
            print(f"✅ Created setup template: {template.name}")
        else:
            print(f"⚠️ Setup template already exists: {template.name}")

def main():
    """Main function to create all setup data"""
    print("🚀 Creating initial setup data for ERP system...")
    print()
    
    print("📍 Creating countries...")
    create_countries()
    print()
    
    print("💰 Creating currencies...")
    create_currencies()
    print()
    
    print("🌍 Creating timezones...")
    create_timezones()
    print()
    
    print("📋 Creating setup templates...")
    create_setup_templates()
    print()
    
    print("✅ Setup data creation completed successfully!")
    print()
    print("📊 Summary:")
    print(f"   Countries: {Country.objects.count()}")
    print(f"   Currencies: {Currency.objects.count()}")
    print(f"   Timezones: {Timezone.objects.count()}")
    print(f"   Setup Templates: {SetupTemplate.objects.count()}")

if __name__ == '__main__':
    main()
