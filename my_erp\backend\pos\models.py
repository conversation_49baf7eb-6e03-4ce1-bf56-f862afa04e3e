"""
Point of Sale (POS) Module Models - Complete Odoo POS System
Based on Odoo's Point of Sale Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime, timedelta
import json

# Import models from other modules for integration
from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountTax, ProductProduct, ProductCategory
)
from inventory.models import StockLocation, StockPicking, StockMove


class PosConfig(models.Model):
    """POS Configuration - Based on Odoo pos.config"""

    PICKING_TYPES = [
        ('incoming', 'Receipt'),
        ('outgoing', 'Delivery'),
        ('internal', 'Internal Transfer'),
    ]

    # Basic Configuration
    name = models.CharField(max_length=64)
    active = models.BooleanField(default=True)

    # Journal Configuration
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, help_text="Sales Journal")
    invoice_journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, related_name='pos_invoice_journal', help_text="Invoice Journal")

    # Currency
    currency_id = models.CharField(max_length=3, default='PKR', help_text="Currency")

    # Stock Configuration
    picking_type_id = models.CharField(max_length=16, choices=PICKING_TYPES, default='outgoing', help_text="Operation Type")
    stock_location_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, help_text="Stock Location")

    # Product Configuration (simplified - will be enhanced later)
    # available_pricelist_ids = models.ManyToManyField('accounting.ProductPricelist', blank=True, help_text="Available Pricelists")
    # pricelist_id = models.ForeignKey('accounting.ProductPricelist', null=True, blank=True, on_delete=models.SET_NULL, help_text="Default Pricelist")

    # Interface Configuration
    iface_cashdrawer = models.BooleanField(default=False, help_text="Cashdrawer")
    iface_electronic_scale = models.BooleanField(default=False, help_text="Electronic Scale")
    iface_customer_facing_display = models.BooleanField(default=False, help_text="Customer Facing Display")
    iface_print_via_proxy = models.BooleanField(default=False, help_text="Print via Proxy")

    # Receipt Configuration
    receipt_header = models.TextField(blank=True, help_text="Receipt Header")
    receipt_footer = models.TextField(blank=True, help_text="Receipt Footer")

    # Session Configuration
    cash_control = models.BooleanField(default=False, help_text="Cash Control")

    # Sequence
    sequence_id = models.IntegerField(null=True, blank=True, help_text="Order IDs Sequence")
    sequence_line_id = models.IntegerField(null=True, blank=True, help_text="Order Line IDs Sequence")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'pos_config'

    def __str__(self):
        return self.name

    def open_session_cb(self, user):
        """Open new POS session"""
        session = PosSession.objects.create(
            config_id=self,
            user_id=user,
            start_at=datetime.now(),
            state='opening_control',
            company_id=self.company_id,
        )
        return session


class PosSession(models.Model):
    """POS Session - Based on Odoo pos.session"""

    STATES = [
        ('opening_control', 'Opening Control'),
        ('opened', 'In Progress'),
        ('closing_control', 'Closing Control'),
        ('closed', 'Closed & Posted'),
    ]

    # Session Configuration
    config_id = models.ForeignKey(PosConfig, on_delete=models.CASCADE, help_text="Point of Sale")
    name = models.CharField(max_length=64, help_text="Session ID")
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, help_text="Responsible")

    # State
    state = models.CharField(max_length=16, choices=STATES, default='opening_control')

    # Dates
    start_at = models.DateTimeField(help_text="Opening Date")
    stop_at = models.DateTimeField(null=True, blank=True, help_text="Closing Date")

    # Cash Control
    cash_register_balance_start = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Starting Balance")
    cash_register_balance_end_real = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Ending Balance")
    cash_register_balance_end = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Theoretical Closing Balance")
    cash_register_difference = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Difference")

    # Journal Entry
    move_id = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.SET_NULL, help_text="Journal Entry")

    # Sequence
    sequence_number = models.IntegerField(default=1, help_text="Order Sequence Number")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'pos_session'

    def __str__(self):
        return f"{self.config_id.name} - {self.name}"

    def save(self, *args, **kwargs):
        if not self.name:
            self.name = f"{self.config_id.name}/{datetime.now().strftime('%Y%m%d')}/{self.sequence_number:04d}"
        super().save(*args, **kwargs)

    def action_pos_session_open(self):
        """Open POS session"""
        if self.state == 'opening_control':
            self.state = 'opened'
            self.save()
            return True
        return False

    def action_pos_session_closing_control(self):
        """Start closing control"""
        if self.state == 'opened':
            self.state = 'closing_control'
            self.stop_at = datetime.now()
            self._compute_cash_balance()
            self.save()
            return True
        return False

    def action_pos_session_close(self):
        """Close POS session"""
        if self.state == 'closing_control':
            self._create_account_move()
            self.state = 'closed'
            self.save()
            return True
        return False

    def _compute_cash_balance(self):
        """Compute theoretical cash balance"""
        orders = self.order_ids.filter(state__in=['paid', 'done', 'invoiced'])
        cash_payments = 0

        for order in orders:
            for payment in order.payment_ids.all():
                if payment.payment_method_id.is_cash_count:
                    cash_payments += float(payment.amount)

        self.cash_register_balance_end = self.cash_register_balance_start + cash_payments
        self.cash_register_difference = self.cash_register_balance_end_real - self.cash_register_balance_end

    def _create_account_move(self):
        """Create accounting move for session"""
        if not self.move_id:
            move = AccountMove.objects.create(
                move_type='entry',
                date=self.stop_at.date() if self.stop_at else date.today(),
                ref=f"POS Session: {self.name}",
                journal_id=self.config_id.journal_id,
                company_id=self.company_id,
            )

            # Create move lines for session totals
            self._create_session_move_lines(move)

            # Post the move
            move.action_post()
            self.move_id = move


class PosPaymentMethod(models.Model):
    """POS Payment Method - Based on Odoo pos.payment.method"""

    name = models.CharField(max_length=64)
    active = models.BooleanField(default=True)

    # Payment Configuration
    is_cash_count = models.BooleanField(default=False, help_text="Cash")
    use_payment_terminal = models.CharField(max_length=32, blank=True, help_text="Use a Payment Terminal")

    # Journal
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, help_text="Journal")

    # Outstanding Account
    outstanding_account_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, help_text="Outstanding Account")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'pos_payment_method'

    def __str__(self):
        return self.name


class PosOrder(models.Model):
    """POS Order - Based on Odoo pos.order"""

    STATES = [
        ('draft', 'New'),
        ('cancel', 'Cancelled'),
        ('paid', 'Paid'),
        ('done', 'Posted'),
        ('invoiced', 'Invoiced'),
    ]

    # Order Information
    name = models.CharField(max_length=64, help_text="Order Ref")
    date_order = models.DateTimeField(default=datetime.now, help_text="Order Date")

    # Session and Config
    session_id = models.ForeignKey(PosSession, on_delete=models.CASCADE, related_name='order_ids', help_text="Session")
    config_id = models.ForeignKey(PosConfig, on_delete=models.CASCADE, help_text="Point of Sale")

    # Customer
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL, help_text="Customer")

    # Amounts
    amount_total = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Total")
    amount_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Taxes")
    amount_paid = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Paid")
    amount_return = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Returned")

    # State
    state = models.CharField(max_length=16, choices=STATES, default='draft')

    # Pricelist (simplified)
    # pricelist_id = models.ForeignKey('accounting.ProductPricelist', null=True, blank=True, on_delete=models.SET_NULL, help_text="Pricelist")

    # Fiscal Position
    fiscal_position_id = models.IntegerField(null=True, blank=True, help_text="Fiscal Position")

    # User
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, help_text="Salesperson")

    # Sequence
    sequence_number = models.IntegerField(help_text="Sequence Number")
    pos_reference = models.CharField(max_length=64, help_text="Receipt Number")

    # Accounting
    account_move = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.SET_NULL, help_text="Journal Entry")
    picking_id = models.ForeignKey(StockPicking, null=True, blank=True, on_delete=models.SET_NULL, help_text="Picking")

    # Note
    note = models.TextField(blank=True, help_text="Internal Notes")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'pos_order'

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.name:
            self.name = f"{self.config_id.name}/{self.sequence_number:04d}"
        if not self.pos_reference:
            self.pos_reference = self.name
        super().save(*args, **kwargs)

    def action_pos_order_paid(self):
        """Mark order as paid"""
        if self.state == 'draft':
            self._create_picking()
            self.state = 'paid'
            self.save()
            return True
        return False

    def action_pos_order_done(self):
        """Post order to accounting"""
        if self.state == 'paid':
            self._create_account_move()
            self.state = 'done'
            self.save()
            return True
        return False

    def action_pos_order_invoice(self):
        """Create invoice for order"""
        if self.state in ['paid', 'done']:
            invoice = self._create_invoice()
            if invoice:
                self.state = 'invoiced'
                self.save()
                return invoice
        return None

    def _create_picking(self):
        """Create stock picking for order"""
        if not self.picking_id and self.lines.exists():
            picking = StockPicking.objects.create(
                picking_type_id=1,  # Delivery
                partner_id=self.partner_id,
                location_id=self.config_id.stock_location_id,
                location_dest_id=StockLocation.objects.filter(usage='customer').first(),
                origin=self.name,
                company_id=self.company_id,
            )

            # Create stock moves for order lines
            for line in self.lines.all():
                if line.product_id.type == 'product':
                    StockMove.objects.create(
                        name=line.product_id.name,
                        product_id=line.product_id,
                        product_uom_qty=line.qty,
                        product_uom=1,  # Units
                        picking_id=picking,
                        location_id=self.config_id.stock_location_id,
                        location_dest_id=StockLocation.objects.filter(usage='customer').first(),
                        company_id=self.company_id,
                    )

            self.picking_id = picking

    def _create_account_move(self):
        """Create accounting move for order"""
        if not self.account_move:
            move = AccountMove.objects.create(
                move_type='out_invoice',
                partner_id=self.partner_id,
                invoice_date=self.date_order.date(),
                date=self.date_order.date(),
                journal_id=self.config_id.journal_id,
                ref=f"POS Order: {self.name}",
                company_id=self.company_id,
            )

            # Create move lines for order lines
            for line in self.lines.all():
                # Product line
                AccountMoveLine.objects.create(
                    move_id=move,
                    product_id=line.product_id,
                    name=line.product_id.name,
                    quantity=line.qty,
                    price_unit=line.price_unit,
                    account_id=line.product_id.product_tmpl_id.categ_id.property_account_income_categ_id or
                              AccountAccount.objects.filter(account_type='income').first(),
                    tax_ids=line.tax_ids.all(),
                )

            # Post the move
            move.action_post()
            self.account_move = move

    def _create_invoice(self):
        """Create customer invoice"""
        if self.partner_id:
            invoice = AccountMove.objects.create(
                move_type='out_invoice',
                partner_id=self.partner_id,
                invoice_date=self.date_order.date(),
                date=self.date_order.date(),
                journal_id=self.config_id.invoice_journal_id,
                ref=f"POS Invoice: {self.name}",
                company_id=self.company_id,
            )

            # Create invoice lines
            for line in self.lines.all():
                AccountMoveLine.objects.create(
                    move_id=invoice,
                    product_id=line.product_id,
                    name=line.product_id.name,
                    quantity=line.qty,
                    price_unit=line.price_unit,
                    account_id=line.product_id.product_tmpl_id.categ_id.property_account_income_categ_id or
                              AccountAccount.objects.filter(account_type='income').first(),
                    tax_ids=line.tax_ids.all(),
                )

            return invoice
        return None


class PosOrderLine(models.Model):
    """POS Order Line - Based on Odoo pos.order.line"""

    # Order Reference
    order_id = models.ForeignKey(PosOrder, on_delete=models.CASCADE, related_name='lines', help_text="Order Ref")

    # Product
    product_id = models.ForeignKey(ProductProduct, on_delete=models.CASCADE, help_text="Product")

    # Quantity and Price
    qty = models.DecimalField(max_digits=16, decimal_places=4, default=1, help_text="Quantity")
    price_unit = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Unit Price")
    price_subtotal = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Subtotal w/o Tax")
    price_subtotal_incl = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Subtotal")

    # Discount
    discount = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text="Discount (%)")

    # Tax
    tax_ids = models.ManyToManyField(AccountTax, blank=True, help_text="Taxes")

    # Name
    full_product_name = models.CharField(max_length=256, help_text="Full Product Name")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'pos_order_line'

    def __str__(self):
        return f"{self.order_id.name} - {self.product_id.name}"

    def save(self, *args, **kwargs):
        # Set full product name
        if not self.full_product_name:
            self.full_product_name = self.product_id.name

        # Calculate subtotals
        subtotal = self.qty * self.price_unit
        if self.discount:
            subtotal = subtotal * (1 - self.discount / 100)

        self.price_subtotal = subtotal

        # Save first to get an ID
        super().save(*args, **kwargs)

        # Calculate tax amount (simplified) - only if object has been saved
        if self.pk:
            tax_amount = 0
            for tax in self.tax_ids.all():
                if tax.amount_type == 'percent':
                    tax_amount += (subtotal * tax.amount) / 100

            self.price_subtotal_incl = subtotal + tax_amount

            # Save again if tax amount changed
            if tax_amount > 0:
                super().save(update_fields=['price_subtotal_incl'])


class PosPayment(models.Model):
    """POS Payment - Based on Odoo pos.payment"""

    # Order Reference
    pos_order_id = models.ForeignKey(PosOrder, on_delete=models.CASCADE, related_name='payment_ids', help_text="Order")

    # Payment Details
    amount = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Amount")
    payment_date = models.DateTimeField(default=datetime.now, help_text="Payment Date")

    # Payment Method
    payment_method_id = models.ForeignKey(PosPaymentMethod, on_delete=models.CASCADE, help_text="Payment Method")

    # Card Details (for card payments)
    card_type = models.CharField(max_length=32, blank=True, help_text="Card Type")
    cardholder_name = models.CharField(max_length=64, blank=True, help_text="Cardholder Name")
    transaction_id = models.CharField(max_length=64, blank=True, help_text="Transaction ID")

    # Session
    session_id = models.ForeignKey(PosSession, on_delete=models.CASCADE, help_text="Session")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'pos_payment'

    def __str__(self):
        return f"{self.pos_order_id.name} - {self.payment_method_id.name} - {self.amount}"


class PosMakePayment(models.Model):
    """POS Make Payment Wizard - Based on Odoo pos.make.payment"""

    # Payment Configuration
    amount = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Payment Amount")
    payment_method_id = models.ForeignKey(PosPaymentMethod, on_delete=models.CASCADE, help_text="Payment Method")
    payment_name = models.CharField(max_length=64, blank=True, help_text="Payment Reference")
    payment_date = models.DateTimeField(default=datetime.now, help_text="Payment Date")

    # Session
    session_id = models.ForeignKey(PosSession, on_delete=models.CASCADE, help_text="Session")

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'pos_make_payment'

    def __str__(self):
        return f"Payment - {self.amount}"

    def process_payment(self):
        """Process payment"""
        # Create payment record
        payment = PosPayment.objects.create(
            amount=self.amount,
            payment_method_id=self.payment_method_id,
            payment_date=self.payment_date,
            session_id=self.session_id,
            company_id=self.session_id.company_id,
        )
        return payment


class PosCloseSessionWizard(models.Model):
    """POS Close Session Wizard - Based on Odoo pos.close.session.wizard"""

    # Session
    session_id = models.ForeignKey(PosSession, on_delete=models.CASCADE, help_text="Session")

    # Cash Control
    cash_register_balance_end_real = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Ending Balance")

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'pos_close_session_wizard'

    def __str__(self):
        return f"Close Session - {self.session_id.name}"

    def close_session(self):
        """Close POS session"""
        self.session_id.cash_register_balance_end_real = self.cash_register_balance_end_real
        return self.session_id.action_pos_session_close()


class PosDetails(models.Model):
    """POS Details Report - Based on Odoo pos.details"""

    # Date Range
    date_start = models.DateField(help_text="Start Date")
    date_stop = models.DateField(help_text="End Date")

    # POS Configuration
    pos_config_ids = models.ManyToManyField(PosConfig, help_text="Point of Sale")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'pos_details'

    def __str__(self):
        return f"POS Details - {self.date_start} to {self.date_stop}"

    def generate_report(self):
        """Generate POS details report"""
        sessions = PosSession.objects.filter(
            config_id__in=self.pos_config_ids.all(),
            start_at__date__gte=self.date_start,
            start_at__date__lte=self.date_stop,
            state='closed'
        )

        report_data = {
            'date_start': self.date_start,
            'date_stop': self.date_stop,
            'sessions': [],
            'total_sales': 0,
            'total_tax': 0,
            'total_discount': 0,
        }

        for session in sessions:
            session_data = {
                'session': session,
                'orders': session.order_ids.filter(state__in=['paid', 'done', 'invoiced']),
                'total_sales': 0,
                'total_tax': 0,
                'payment_methods': {},
            }

            for order in session_data['orders']:
                session_data['total_sales'] += float(order.amount_total)
                session_data['total_tax'] += float(order.amount_tax)

                # Group by payment methods
                for payment in order.payment_ids.all():
                    method_name = payment.payment_method_id.name
                    if method_name not in session_data['payment_methods']:
                        session_data['payment_methods'][method_name] = 0
                    session_data['payment_methods'][method_name] += float(payment.amount)

            report_data['sessions'].append(session_data)
            report_data['total_sales'] += session_data['total_sales']
            report_data['total_tax'] += session_data['total_tax']

        return report_data


class PosDailyReport(models.Model):
    """POS Daily Report - Based on Odoo pos.daily.report"""

    # Date
    date = models.DateField(default=date.today, help_text="Date")

    # POS Configuration
    pos_config_id = models.ForeignKey(PosConfig, on_delete=models.CASCADE, help_text="Point of Sale")

    # Totals
    total_sales = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Total Sales")
    total_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Total Tax")
    total_discount = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Total Discount")
    total_orders = models.IntegerField(default=0, help_text="Total Orders")

    # Payment Methods
    cash_total = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Cash Total")
    card_total = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Card Total")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'pos_daily_report'
        unique_together = ['date', 'pos_config_id']

    def __str__(self):
        return f"Daily Report - {self.pos_config_id.name} - {self.date}"

    def compute_totals(self):
        """Compute daily totals"""
        sessions = PosSession.objects.filter(
            config_id=self.pos_config_id,
            start_at__date=self.date,
            state='closed'
        )

        total_sales = 0
        total_tax = 0
        total_orders = 0
        cash_total = 0
        card_total = 0

        for session in sessions:
            orders = session.order_ids.filter(state__in=['paid', 'done', 'invoiced'])
            total_orders += orders.count()

            for order in orders:
                total_sales += float(order.amount_total)
                total_tax += float(order.amount_tax)

                for payment in order.payment_ids.all():
                    if payment.payment_method_id.is_cash_count:
                        cash_total += float(payment.amount)
                    else:
                        card_total += float(payment.amount)

        self.total_sales = total_sales
        self.total_tax = total_tax
        self.total_orders = total_orders
        self.cash_total = cash_total
        self.card_total = card_total
        self.save()

        return {
            'total_sales': total_sales,
            'total_tax': total_tax,
            'total_orders': total_orders,
            'cash_total': cash_total,
            'card_total': card_total,
        }
