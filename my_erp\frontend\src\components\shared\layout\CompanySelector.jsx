/**
 * Company Selector Component - Multi-Company Support
 * Following Odoo's company switching functionality
 */
import React, { useState } from 'react';
import { Select, Avatar, Space, Typography, Tooltip, Button } from 'antd';
import { 
  GlobalOutlined, 
  BuildingOutlined, 
  PlusOutlined,
  SettingOutlined 
} from '@ant-design/icons';
import { useAuth } from '../auth/AuthProvider';
import { useNavigate } from 'react-router-dom';

const { Text } = Typography;
const { Option } = Select;

const CompanySelector = () => {
  const { 
    companies, 
    currentCompany, 
    switchCompany, 
    isSuperUser, 
    isAdmin 
  } = useAuth();
  
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleCompanyChange = async (companyId) => {
    if (companyId === currentCompany?.id) return;
    
    setLoading(true);
    try {
      await switchCompany(companyId);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCompany = () => {
    navigate('/settings/companies/create');
  };

  const handleManageCompanies = () => {
    navigate('/settings/companies');
  };

  if (!companies || companies.length === 0) {
    return (
      <Space>
        <GlobalOutlined />
        <Text type="secondary">No Company</Text>
      </Space>
    );
  }

  // Single company - just display it
  if (companies.length === 1) {
    return (
      <Space>
        <Avatar 
          size="small" 
          icon={<BuildingOutlined />}
          src={currentCompany?.logo}
          style={{ backgroundColor: '#1890ff' }}
        />
        <Text strong>{currentCompany?.name}</Text>
        {(isSuperUser() || isAdmin()) && (
          <Tooltip title="Manage Companies">
            <Button 
              type="text" 
              size="small" 
              icon={<SettingOutlined />}
              onClick={handleManageCompanies}
            />
          </Tooltip>
        )}
      </Space>
    );
  }

  return (
    <Space>
      <GlobalOutlined />
      <Select
        value={currentCompany?.id}
        onChange={handleCompanyChange}
        loading={loading}
        className="company-selector"
        style={{ minWidth: 200 }}
        placeholder="Select Company"
        optionLabelProp="label"
        dropdownRender={(menu) => (
          <>
            {menu}
            {(isSuperUser() || isAdmin()) && (
              <>
                <div style={{ padding: '8px 0', borderTop: '1px solid #f0f0f0' }}>
                  <Button
                    type="text"
                    icon={<PlusOutlined />}
                    onClick={handleCreateCompany}
                    style={{ width: '100%', textAlign: 'left' }}
                  >
                    Create Company
                  </Button>
                  <Button
                    type="text"
                    icon={<SettingOutlined />}
                    onClick={handleManageCompanies}
                    style={{ width: '100%', textAlign: 'left' }}
                  >
                    Manage Companies
                  </Button>
                </div>
              </>
            )}
          </>
        )}
      >
        {companies.map(company => (
          <Option 
            key={company.id} 
            value={company.id}
            label={
              <Space>
                <Avatar 
                  size="small" 
                  icon={<BuildingOutlined />}
                  src={company.logo}
                  style={{ backgroundColor: '#1890ff' }}
                />
                <Text>{company.name}</Text>
              </Space>
            }
          >
            <Space>
              <Avatar 
                size="small" 
                icon={<BuildingOutlined />}
                src={company.logo}
                style={{ backgroundColor: '#1890ff' }}
              />
              <div>
                <Text strong>{company.name}</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {company.currency_id} • {company.country}
                </Text>
              </div>
            </Space>
          </Option>
        ))}
      </Select>
    </Space>
  );
};

export default CompanySelector;
