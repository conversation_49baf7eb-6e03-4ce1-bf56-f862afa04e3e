# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_balance
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2021-11-08 06:35+0000\n"
"Last-Translator: <PERSON> <ibu<PERSON><EMAIL>>\n"
"Language-Team: none\n"
"Language: es_AR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/wizard/open_tax_balances.py:0
msgid "%(name)s: %(target)s from %(from)s to %(to)s"
msgstr "%(name)s: %(target)s desde %(from)s hasta %(to)s"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_search_balance
msgid "Account Tax"
msgstr "Cuenta de Impuesto"

#. module: account_tax_balance
#: model:ir.model.fields.selection,name:account_tax_balance.selection__wizard_open_tax_balances__target_move__all
msgid "All Entries"
msgstr "Todos los Asientos"

#. module: account_tax_balance
#: model:ir.model.fields.selection,name:account_tax_balance.selection__wizard_open_tax_balances__target_move__posted
msgid "All Posted Entries"
msgstr "Todos los Asientos Asentados"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__balance_regular
msgid "Balance"
msgstr "Saldo"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__balance_refund
msgid "Balance Refund"
msgstr "Cuota Devoluciones"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__base_balance_regular
msgid "Base Balance"
msgstr "Base Imponible"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__base_balance_refund
msgid "Base Balance Refund"
msgstr "Base Devoluciones"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "Base Total"
msgstr "Base Total"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.wizard_open_tax_balances
msgid "Cancel"
msgstr "Cancelar"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__company_ids
msgid "Companies"
msgstr "Compañías"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__create_date
msgid "Created on"
msgstr "Creado en"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__date_range_id
msgid "Date Range"
msgstr "Rango de Fecha"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__display_name
msgid "Display Name"
msgstr "Mostrar Nombre"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_bank_statement_line__financial_type
#: model:ir.model.fields,field_description:account_tax_balance.field_account_move__financial_type
msgid "Financial Type"
msgstr "Tipo Financiero"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__from_date
msgid "From Date"
msgstr "Desde la Fecha"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_search_balance
msgid "Group By"
msgstr "Agrupar por"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__has_moves
msgid "Has balance in period"
msgstr "Tiene saldo en periodo"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__id
msgid "ID"
msgstr "ID"

#. module: account_tax_balance
#: model:ir.model,name:account_tax_balance.model_account_move
msgid "Journal Entry"
msgstr "Apunte Contable"

#. module: account_tax_balance
#: model:ir.model,name:account_tax_balance.model_account_move_line
msgid "Journal Item"
msgstr "Asiento Contable"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__write_uid
msgid "Last Updated by"
msgstr "Última modificación por"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Liquidity"
msgstr "Liquidez"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_account_move_filter
msgid "Move type"
msgstr "Tipo de Operación"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.wizard_open_tax_balances
msgid "Open Taxes"
msgstr "Ver Impuestos"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Other"
msgstr "Otro"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Payable"
msgstr "Pagadero"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Payable refund"
msgstr "Reembolso a Pagar"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Receivable"
msgstr "A Cobrar"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Receivable refund"
msgstr "Reembolso a Cobrar"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_search_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "Short Name"
msgstr "Nombre Corto"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__target_move
msgid "Target Moves"
msgstr "Movimientos destino"

#. module: account_tax_balance
#: model:ir.model,name:account_tax_balance.model_account_tax
msgid "Tax"
msgstr "Impuesto"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_search_balance
msgid "Tax Group"
msgstr "Grupo del impuesto"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_search_balance
msgid "Tax Scope"
msgstr "Uso del impuesto"

#. module: account_tax_balance
#: model:ir.actions.act_window,name:account_tax_balance.action_open_tax_balances
#: model:ir.actions.act_window,name:account_tax_balance.action_tax_balances_tree
#: model:ir.ui.menu,name:account_tax_balance.menu_action_open_tax_balances
#: model:ir.ui.menu,name:account_tax_balance.menu_tax_balances
#: model_terms:ir.ui.view,arch_db:account_tax_balance.wizard_open_tax_balances
msgid "Taxes Balance"
msgstr "Tabla de impuestos"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__to_date
msgid "To Date"
msgstr "Hasta la Fecha"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "Total"
msgstr "Total"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__balance
msgid "Total Balance"
msgstr "Total Cuota"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__base_balance
msgid "Total Base Balance"
msgstr "Total Base Imponible"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_tax.py:0
msgid "Unsupported search operator"
msgstr "Operador de búsqueda no soportado"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View base lines"
msgstr "Ver líneas de base imponible"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View base refund lines"
msgstr "Ver líneas de base imponible de devoluciones"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View base regular lines"
msgstr "Ver líneas de base imponible de operaciones corrientes"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View tax lines"
msgstr "Ver líneas de cuota"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View tax refund lines"
msgstr "Ver líneas de cuota de devoluciones"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View tax regular lines"
msgstr "Ver líneas de cuota de operaciones corrientes"

#. module: account_tax_balance
#: model:ir.model,name:account_tax_balance.model_wizard_open_tax_balances
msgid "Wizard Open Tax Balances"
msgstr "Asistente apertura balance de impuestos"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.wizard_open_tax_balances
msgid "or"
msgstr "o"

#~ msgid "Last Modified on"
#~ msgstr "Última modificación en"

#~ msgid "Journal Entries"
#~ msgstr "Asientos Contables"

#~ msgid "Move Type"
#~ msgstr "Tipo de Movimiento"
