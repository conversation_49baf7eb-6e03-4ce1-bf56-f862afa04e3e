"""
CRM Module Models - Complete Odoo CRM Management
Based on Odoo's CRM Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime, timedelta

# Import models from other modules for integration
from accounting.models import (
    ResCompany, ResPartner, ResCountry, ResCountryState, ResCurrency
)


class CrmTeam(models.Model):
    """CRM Team - Based on Odoo crm.team (Enhanced from sales.models.SalesTeam)"""

    name = models.CharField(max_length=64)
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10)
    color = models.IntegerField(default=0, help_text="Color Index")

    # Team Leader and Members
    user_id = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, help_text="Team Leader")
    member_ids = models.ManyToManyField(User, blank=True, related_name='crm_teams', help_text="Team Members")

    # CRM Configuration
    use_leads = models.BooleanField(default=True, help_text="Use Leads")
    use_opportunities = models.BooleanField(default=True, help_text="Use Opportunities")

    # Targets and Goals
    invoiced_target = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Monthly Revenue Target")
    quotation_count = models.IntegerField(default=0, help_text="Number of Quotations")
    sale_order_count = models.IntegerField(default=0, help_text="Number of Sales Orders")

    # Lead Assignment
    assignment_enabled = models.BooleanField(default=False, help_text="Lead Assignment")
    assignment_auto_enabled = models.BooleanField(default=False, help_text="Automatic Assignment")
    assignment_optout = models.BooleanField(default=False, help_text="Skip Auto Assignment")

    # Domain and Filters
    domain = models.TextField(blank=True, help_text="Domain Filter")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'crm_team'

    def __str__(self):
        return self.name


class CrmStage(models.Model):
    """CRM Stage - Based on Odoo crm.stage"""

    name = models.CharField(max_length=64)
    sequence = models.IntegerField(default=1)

    # Stage Properties
    is_won = models.BooleanField(default=False, help_text="Is Won Stage")
    fold = models.BooleanField(default=False, help_text="Folded in Kanban")

    # Requirements
    requirements = models.TextField(blank=True, help_text="Requirements")

    # Team Filter
    team_ids = models.ManyToManyField(CrmTeam, blank=True, help_text="Teams")

    class Meta:
        db_table = 'crm_stage'
        ordering = ['sequence', 'name']

    def __str__(self):
        return self.name


class CrmLostReason(models.Model):
    """Lost Reason - Based on Odoo crm.lost.reason"""

    name = models.CharField(max_length=64)
    active = models.BooleanField(default=True)

    class Meta:
        db_table = 'crm_lost_reason'

    def __str__(self):
        return self.name


class CrmLead(models.Model):
    """CRM Lead - Based on Odoo crm.lead"""

    TYPES = [
        ('lead', 'Lead'),
        ('opportunity', 'Opportunity'),
    ]

    PRIORITIES = [
        ('0', 'Low'),
        ('1', 'Normal'),
        ('2', 'High'),
        ('3', 'Very High'),
    ]

    # Basic Information
    name = models.CharField(max_length=128, help_text="Subject")
    active = models.BooleanField(default=True)
    type = models.CharField(max_length=12, choices=TYPES, default='lead')
    priority = models.CharField(max_length=1, choices=PRIORITIES, default='1')

    # Contact Information
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL, help_text="Customer")
    partner_name = models.CharField(max_length=128, blank=True, help_text="Customer Name")
    contact_name = models.CharField(max_length=128, blank=True, help_text="Contact Name")
    email_from = models.EmailField(blank=True, help_text="Email")
    phone = models.CharField(max_length=32, blank=True, help_text="Phone")
    mobile = models.CharField(max_length=32, blank=True, help_text="Mobile")
    website = models.URLField(blank=True, help_text="Website")

    # Address Information
    street = models.CharField(max_length=128, blank=True)
    street2 = models.CharField(max_length=128, blank=True)
    city = models.CharField(max_length=64, blank=True)
    state_id = models.ForeignKey(ResCountryState, null=True, blank=True, on_delete=models.SET_NULL)
    zip = models.CharField(max_length=16, blank=True)
    country_id = models.ForeignKey(ResCountry, null=True, blank=True, on_delete=models.SET_NULL)

    # Sales Information
    user_id = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, help_text="Salesperson")
    team_id = models.ForeignKey(CrmTeam, null=True, blank=True, on_delete=models.SET_NULL, help_text="Sales Team")
    stage_id = models.ForeignKey(CrmStage, null=True, blank=True, on_delete=models.SET_NULL, help_text="Stage")

    # Opportunity Information
    expected_revenue = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Expected Revenue")
    prorated_revenue = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Prorated Revenue")
    recurring_revenue = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Recurring Revenue")
    recurring_plan = models.CharField(max_length=16, choices=[('monthly', 'Monthly'), ('yearly', 'Yearly')], blank=True)

    # Probability and Dates
    probability = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text="Success Rate (%)")
    automated_probability = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text="Automated Probability")
    date_deadline = models.DateField(null=True, blank=True, help_text="Expected Closing")
    date_closed = models.DateTimeField(null=True, blank=True, help_text="Closed Date")
    date_conversion = models.DateTimeField(null=True, blank=True, help_text="Conversion Date")
    date_last_stage_update = models.DateTimeField(auto_now=True, help_text="Last Stage Update")

    # Source and Campaign
    source_id = models.IntegerField(null=True, blank=True, help_text="Source")
    medium_id = models.IntegerField(null=True, blank=True, help_text="Medium")
    campaign_id = models.IntegerField(null=True, blank=True, help_text="Campaign")
    referred = models.CharField(max_length=128, blank=True, help_text="Referred By")

    # Lost Information
    lost_reason_id = models.ForeignKey(CrmLostReason, null=True, blank=True, on_delete=models.SET_NULL)

    # Description
    description = models.TextField(blank=True, help_text="Notes")

    # Tags
    tag_ids = models.CharField(max_length=256, blank=True, help_text="Tags")

    # Company and Currency
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)
    currency_id = models.CharField(max_length=3, default='PKR')

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'crm_lead'
        ordering = ['-create_date', '-write_date']

    def __str__(self):
        return self.name

    def action_set_won(self):
        """Mark opportunity as won - Exact Odoo logic"""
        if self.type == 'opportunity':
            won_stage = CrmStage.objects.filter(is_won=True).first()
            if won_stage:
                self.stage_id = won_stage
                self.probability = 100
                self.date_closed = datetime.now()
                self.save()
                return True
        return False

    def action_set_lost(self, lost_reason_id=None):
        """Mark opportunity as lost - Exact Odoo logic"""
        if self.type == 'opportunity':
            self.probability = 0
            self.date_closed = datetime.now()
            if lost_reason_id:
                self.lost_reason_id_id = lost_reason_id
            self.active = False
            self.save()
            return True
        return False

    def convert_to_opportunity(self):
        """Convert lead to opportunity - Exact Odoo logic"""
        if self.type == 'lead':
            self.type = 'opportunity'
            self.date_conversion = datetime.now()

            # Set default stage for opportunities
            if not self.stage_id:
                first_stage = CrmStage.objects.filter(
                    models.Q(team_ids=self.team_id) | models.Q(team_ids__isnull=True)
                ).first()
                if first_stage:
                    self.stage_id = first_stage

            # Set default probability
            if self.probability == 0:
                self.probability = 20  # Default opportunity probability

            self.save()
            return True
        return False

    def create_partner(self):
        """Create partner from lead - Exact Odoo logic"""
        if not self.partner_id and (self.partner_name or self.contact_name):
            partner = ResPartner.objects.create(
                name=self.partner_name or self.contact_name,
                email=self.email_from,
                phone=self.phone,
                mobile=self.mobile,
                website=self.website,
                street=self.street,
                street2=self.street2,
                city=self.city,
                state_id=self.state_id,
                zip=self.zip,
                country_id=self.country_id,
                is_company=bool(self.partner_name),
                customer_rank=1,
                company_id=self.company_id,
            )
            self.partner_id = partner
            self.save()
            return partner
        return self.partner_id

    def create_sale_order(self):
        """Create sale order from opportunity - Integration with sales module"""
        if self.type == 'opportunity' and self.partner_id:
            # Import here to avoid circular imports
            from sales.models import SaleOrder

            order = SaleOrder.objects.create(
                partner_id=self.partner_id,
                partner_invoice_id=self.partner_id,
                partner_shipping_id=self.partner_id,
                user_id=self.user_id,
                team_id_id=self.team_id.id if self.team_id else None,
                origin=f"Opportunity: {self.name}",
                company_id=self.company_id,
            )

            # Mark opportunity as won
            self.action_set_won()

            return order
        return None

    def _compute_prorated_revenue(self):
        """Compute prorated revenue based on probability"""
        self.prorated_revenue = (self.expected_revenue * self.probability) / 100
        self.save()


class CrmActivity(models.Model):
    """CRM Activity - Based on Odoo mail.activity for CRM"""

    ACTIVITY_TYPES = [
        ('call', 'Call'),
        ('meeting', 'Meeting'),
        ('email', 'Email'),
        ('todo', 'To Do'),
        ('upload_file', 'Upload File'),
    ]

    STATES = [
        ('overdue', 'Overdue'),
        ('today', 'Today'),
        ('planned', 'Planned'),
        ('done', 'Done'),
    ]

    # Activity Information
    activity_type = models.CharField(max_length=16, choices=ACTIVITY_TYPES, default='call')
    summary = models.CharField(max_length=128, help_text="Summary")
    note = models.TextField(blank=True, help_text="Note")

    # Dates
    date_deadline = models.DateField(help_text="Due Date")

    # Assignment
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, help_text="Assigned to")
    create_uid = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_activities', help_text="Created by")

    # Related Record
    lead_id = models.ForeignKey(CrmLead, on_delete=models.CASCADE, related_name='activity_ids')

    # State
    state = models.CharField(max_length=16, choices=STATES, default='planned')

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'crm_activity'
        ordering = ['date_deadline', 'create_date']

    def __str__(self):
        return f"{self.summary} - {self.lead_id.name}"

    def action_done(self):
        """Mark activity as done"""
        self.state = 'done'
        self.save()
        return True

    def save(self, *args, **kwargs):
        # Compute state based on deadline
        today = date.today()
        if self.state != 'done':
            if self.date_deadline < today:
                self.state = 'overdue'
            elif self.date_deadline == today:
                self.state = 'today'
            else:
                self.state = 'planned'
        super().save(*args, **kwargs)
