import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Switch, 
  Typography, 
  Space,
  Tag,
  message,
  Popconfirm
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  SearchOutlined 
} from '@ant-design/icons';
import { accountingAPI } from '../services/api';

const { Title } = Typography;
const { Option } = Select;

const ChartOfAccounts = () => {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');

  // Account types based on Odoo
  const accountTypes = [
    { value: 'asset_receivable', label: 'Receivable', color: 'blue' },
    { value: 'asset_cash', label: 'Bank and Cash', color: 'green' },
    { value: 'asset_current', label: 'Current Assets', color: 'cyan' },
    { value: 'asset_non_current', label: 'Non-current Assets', color: 'geekblue' },
    { value: 'asset_fixed', label: 'Fixed Assets', color: 'purple' },
    { value: 'liability_payable', label: 'Payable', color: 'red' },
    { value: 'liability_current', label: 'Current Liabilities', color: 'volcano' },
    { value: 'liability_non_current', label: 'Non-current Liabilities', color: 'magenta' },
    { value: 'equity', label: 'Equity', color: 'gold' },
    { value: 'income', label: 'Income', color: 'lime' },
    { value: 'expense', label: 'Expenses', color: 'orange' },
  ];

  const columns = [
    {
      title: 'Code',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      sorter: (a, b) => a.code.localeCompare(b.code),
    },
    {
      title: 'Account Name',
      dataIndex: 'name',
      key: 'name',
      filterable: true,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search account name"
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button onClick={() => clearFilters()} size="small" style={{ width: 90 }}>
              Reset
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
      onFilter: (value, record) =>
        record.name.toString().toLowerCase().includes(value.toLowerCase()),
    },
    {
      title: 'Type',
      dataIndex: 'account_type',
      key: 'account_type',
      render: (type) => {
        const accountType = accountTypes.find(t => t.value === type);
        return accountType ? (
          <Tag color={accountType.color}>{accountType.label}</Tag>
        ) : type;
      },
      filters: accountTypes.map(type => ({ text: type.label, value: type.value })),
      onFilter: (value, record) => record.account_type === value,
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      align: 'right',
      render: (balance) => (
        <span className={balance >= 0 ? 'balance-positive' : 'balance-negative'}>
          PKR {Math.abs(balance || 0).toLocaleString()}
        </span>
      ),
      sorter: (a, b) => (a.balance || 0) - (b.balance || 0),
    },
    {
      title: 'Reconcile',
      dataIndex: 'reconcile',
      key: 'reconcile',
      render: (reconcile) => (
        <Tag color={reconcile ? 'green' : 'default'}>
          {reconcile ? 'Yes' : 'No'}
        </Tag>
      ),
      filters: [
        { text: 'Reconcilable', value: true },
        { text: 'Non-reconcilable', value: false },
      ],
      onFilter: (value, record) => record.reconcile === value,
    },
    {
      title: 'Status',
      dataIndex: 'deprecated',
      key: 'deprecated',
      render: (deprecated) => (
        <Tag color={deprecated ? 'red' : 'green'}>
          {deprecated ? 'Deprecated' : 'Active'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
          />
          <Popconfirm
            title="Are you sure you want to delete this account?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              icon={<DeleteOutlined />}
              danger
              size="small"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    fetchAccounts();
  }, []);

  const fetchAccounts = async () => {
    setLoading(true);
    try {
      const response = await accountingAPI.getAccounts();
      setAccounts(response.data.results || response.data);
    } catch (error) {
      message.error('Failed to fetch accounts');
      console.error('Error fetching accounts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingAccount(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (account) => {
    setEditingAccount(account);
    form.setFieldsValue(account);
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await accountingAPI.deleteAccount(id);
      message.success('Account deleted successfully');
      fetchAccounts();
    } catch (error) {
      message.error('Failed to delete account');
      console.error('Error deleting account:', error);
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingAccount) {
        await accountingAPI.updateAccount(editingAccount.id, values);
        message.success('Account updated successfully');
      } else {
        await accountingAPI.createAccount(values);
        message.success('Account created successfully');
      }
      setModalVisible(false);
      fetchAccounts();
    } catch (error) {
      message.error('Failed to save account');
      console.error('Error saving account:', error);
    }
  };

  return (
    <div>
      <div className="page-header">
        <Title level={2}>Chart of Accounts</Title>
        <p>Manage your chart of accounts structure</p>
      </div>

      <div className="action-buttons">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
        >
          Add Account
        </Button>
      </div>

      <div className="table-container">
        <Table
          columns={columns}
          dataSource={accounts}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 50,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} accounts`,
          }}
          scroll={{ x: 1200 }}
        />
      </div>

      <Modal
        title={editingAccount ? 'Edit Account' : 'Add Account'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="code"
            label="Account Code"
            rules={[{ required: true, message: 'Please enter account code' }]}
          >
            <Input placeholder="e.g., 1001" />
          </Form.Item>

          <Form.Item
            name="name"
            label="Account Name"
            rules={[{ required: true, message: 'Please enter account name' }]}
          >
            <Input placeholder="e.g., Cash in Hand" />
          </Form.Item>

          <Form.Item
            name="account_type"
            label="Account Type"
            rules={[{ required: true, message: 'Please select account type' }]}
          >
            <Select placeholder="Select account type">
              {accountTypes.map(type => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="reconcile"
            label="Allow Reconciliation"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="deprecated"
            label="Deprecated"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="note"
            label="Internal Notes"
          >
            <Input.TextArea rows={3} placeholder="Internal notes about this account" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ChartOfAccounts;
