"""
Authentication Models - User Management & Authentication
Following Odoo's user hierarchy and security model
"""
from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone
import json


class UserProfile(models.Model):
    """
    User Profile Extension - Following Odoo's res.users structure
    """
    USER_TYPES = [
        ('system_super_user', 'System Super User'),      # Complete system access
        ('company_super_user', 'Company Super User'),    # Full company access
        ('company_admin', 'Company Administrator'),       # Company admin access
        ('user', 'Regular User'),                        # Group-based access
        ('portal', 'Portal User')                        # External limited access
    ]

    # Link to Django User
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')

    # Basic Information
    name = models.CharField(max_length=255, blank=True)
    phone = models.CharField(max_length=50, blank=True)
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)

    # User Type & Status (Odoo-style hierarchy)
    user_type = models.CharField(max_length=20, choices=USER_TYPES, default='user')
    is_company_super_user = models.BooleanField(default=False)  # Company-level super user
    is_portal_user = models.BooleanField(default=False)         # External portal user

    # Profile Information
    bio = models.TextField(blank=True)
    department = models.CharField(max_length=100, blank=True)
    position = models.CharField(max_length=100, blank=True)

    # Authentication
    last_login_ip = models.GenericIPAddressField(blank=True, null=True)
    login_count = models.IntegerField(default=0)

    # Preferences (Text field storing JSON for SQLite compatibility)
    preferences = models.TextField(default='{}', blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_profiles'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'

    def __str__(self):
        return self.name or self.user.get_full_name() or self.user.username

    def get_full_name(self):
        return self.name or self.user.get_full_name()

    def get_short_name(self):
        return self.name or self.user.first_name

    def update_login_info(self, ip_address=None):
        """Update login information"""
        self.login_count += 1
        if ip_address:
            self.last_login_ip = ip_address
        self.save(update_fields=['login_count', 'last_login_ip'])

    def get_preferences(self):
        """Get user preferences with defaults"""
        default_preferences = {
            'theme': 'light',
            'language': 'en',
            'timezone': 'UTC',
            'dateFormat': 'MM/DD/YYYY',
            'timeFormat': '12h',
            'currency': 'USD',
            'notifications': {
                'email': True,
                'browser': True,
                'sound': False
            },
            'dashboard': {
                'autoRefresh': True,
                'refreshInterval': 30,
                'showWelcome': True
            },
            'interface': {
                'sidebarCollapsed': False,
                'compactMode': False,
                'showTooltips': True
            }
        }

        # Parse JSON preferences
        try:
            user_prefs = json.loads(self.preferences) if self.preferences else {}
        except (json.JSONDecodeError, TypeError):
            user_prefs = {}

        # Merge user preferences with defaults
        for key, value in default_preferences.items():
            if key not in user_prefs:
                user_prefs[key] = value
            elif isinstance(value, dict) and isinstance(user_prefs[key], dict):
                for sub_key, sub_value in value.items():
                    if sub_key not in user_prefs[key]:
                        user_prefs[key][sub_key] = sub_value

        return user_prefs

    def update_preferences(self, preferences):
        """Update user preferences"""
        current_prefs = self.get_preferences()
        current_prefs.update(preferences)
        self.preferences = json.dumps(current_prefs)
        self.save(update_fields=['preferences'])

    def get_user_hierarchy_level(self):
        """Get user hierarchy level following Odoo structure"""
        if self.user.is_superuser:
            return 1  # System Super User
        elif self.is_company_super_user:
            return 2  # Company Super User
        elif self.user.is_staff:
            return 3  # Company Admin
        elif self.is_portal_user:
            return 5  # Portal User
        else:
            return 4  # Regular User

    def can_manage_user(self, target_user_profile):
        """Check if this user can manage another user"""
        my_level = self.get_user_hierarchy_level()
        target_level = target_user_profile.get_user_hierarchy_level()

        # Higher hierarchy levels can manage lower levels
        # But company super users can only manage within their companies
        if my_level < target_level:
            if my_level == 1:  # System super user can manage anyone
                return True
            elif my_level == 2:  # Company super user
                # Check if they share companies
                my_companies = set(self.user.company_memberships.values_list('company_id', flat=True))
                target_companies = set(target_user_profile.user.company_memberships.values_list('company_id', flat=True))
                return bool(my_companies.intersection(target_companies))
            else:
                return False
        return False


class UserGroup(models.Model):
    """
    User Groups - Following Odoo's access rights model
    """
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)
    
    # Module-specific groups
    module = models.CharField(max_length=50, blank=True)
    
    # Permissions
    can_read = models.BooleanField(default=True)
    can_write = models.BooleanField(default=False)
    can_create = models.BooleanField(default=False)
    can_delete = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'res_groups'
        verbose_name = 'User Group'
        verbose_name_plural = 'User Groups'
    
    def __str__(self):
        return self.name


class UserGroupMembership(models.Model):
    """
    User Group Membership - Many-to-Many relationship
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='custom_group_memberships')
    group = models.ForeignKey(UserGroup, on_delete=models.CASCADE, related_name='user_memberships')
    
    # Additional membership info
    granted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='granted_memberships')
    granted_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'res_users_groups_rel'
        unique_together = ['user', 'group']
        verbose_name = 'User Group Membership'
        verbose_name_plural = 'User Group Memberships'
    
    def __str__(self):
        return f"{self.user} - {self.group}"


class CompanyUserMembership(models.Model):
    """
    Company-User Membership - Following Odoo's multi-company structure
    """
    COMPANY_ROLES = [
        ('owner', 'Company Owner'),           # Company super user
        ('manager', 'Department Manager'),    # Company admin
        ('employee', 'Employee'),            # Regular user
        ('external', 'External User')        # Portal user
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='company_memberships')
    # We'll reference company by ID to avoid circular imports
    company_id = models.IntegerField()  # Reference to ResCompany

    # Role within the company
    role = models.CharField(max_length=20, choices=COMPANY_ROLES, default='employee')

    # Permissions within this company
    is_active = models.BooleanField(default=True)
    can_manage_users = models.BooleanField(default=False)
    can_access_all_modules = models.BooleanField(default=False)

    # Timestamps
    joined_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Who added this user to the company
    added_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='added_company_users')

    class Meta:
        db_table = 'company_user_memberships'
        unique_together = ['user', 'company_id']
        verbose_name = 'Company User Membership'
        verbose_name_plural = 'Company User Memberships'

    def __str__(self):
        return f"{self.user.username} - Company {self.company_id} ({self.role})"

    def is_company_owner(self):
        """Check if user is company owner"""
        return self.role == 'owner'

    def is_company_manager(self):
        """Check if user is company manager"""
        return self.role in ['owner', 'manager']

    def can_manage_company_users(self):
        """Check if user can manage other users in this company"""
        return self.can_manage_users or self.is_company_manager()


class UserSession(models.Model):
    """
    User Session Management - Track active sessions
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sessions')
    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    
    # Session info
    created_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField()
    
    # Status
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'user_sessions'
        verbose_name = 'User Session'
        verbose_name_plural = 'User Sessions'
    
    def __str__(self):
        return f"{self.user} - {self.session_key[:8]}..."
    
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def deactivate(self):
        self.is_active = False
        self.save(update_fields=['is_active'])


class LoginAttempt(models.Model):
    """
    Login Attempt Tracking - Security monitoring
    """
    email = models.EmailField()
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    
    # Attempt info
    success = models.BooleanField(default=False)
    failure_reason = models.CharField(max_length=100, blank=True)
    attempted_at = models.DateTimeField(auto_now_add=True)
    
    # User (if successful)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='login_attempts')
    
    class Meta:
        db_table = 'login_attempts'
        verbose_name = 'Login Attempt'
        verbose_name_plural = 'Login Attempts'
        ordering = ['-attempted_at']
    
    def __str__(self):
        status = "Success" if self.success else "Failed"
        return f"{self.email} - {status} - {self.attempted_at}"
