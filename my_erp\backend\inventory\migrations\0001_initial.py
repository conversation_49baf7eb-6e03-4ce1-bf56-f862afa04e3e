# Generated by Django 4.2.21 on 2025-07-15 18:49

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0002_remove_stockmove_account_move_ids_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='StockInventory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='/', max_length=64)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('cancel', 'Cancelled'), ('confirm', 'In Progress'), ('done', 'Validated')], default='draft', max_length=16)),
                ('filter', models.CharField(choices=[('partial', 'Partial Inventory'), ('product', 'One product only'), ('lot', 'One Lot/Serial Number'), ('owner', 'One owner only'), ('product_owner', 'One product for a specific owner')], default='partial', max_length=16)),
                ('lot_id', models.IntegerField(blank=True, help_text='Lot/Serial Number', null=True)),
                ('package_id', models.IntegerField(blank=True, help_text='Package', null=True)),
                ('accounting_date', models.DateField(blank=True, null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'stock_inventory',
            },
        ),
        migrations.CreateModel(
            name='StockLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('complete_name', models.CharField(blank=True, max_length=256)),
                ('active', models.BooleanField(default=True)),
                ('usage', models.CharField(choices=[('supplier', 'Vendor Location'), ('view', 'View'), ('internal', 'Internal Location'), ('customer', 'Customer Location'), ('inventory', 'Inventory Loss'), ('procurement', 'Procurement'), ('production', 'Production'), ('transit', 'Transit Location')], default='internal', max_length=12)),
                ('barcode', models.CharField(blank=True, max_length=128)),
                ('removal_strategy_id', models.CharField(blank=True, help_text='Removal Strategy', max_length=32)),
                ('scrap_location', models.BooleanField(default=False)),
                ('return_location', models.BooleanField(default=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('child_ids', models.ManyToManyField(blank=True, related_name='parent_locations', to='inventory.stocklocation')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('location_id', models.ForeignKey(blank=True, help_text='Parent Location', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_locations', to='inventory.stocklocation')),
                ('valuation_in_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_valuation_in_locations', to='accounting.accountaccount')),
                ('valuation_out_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_valuation_out_locations', to='accounting.accountaccount')),
            ],
            options={
                'db_table': 'stock_location',
            },
        ),
        migrations.CreateModel(
            name='StockWarehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('code', models.CharField(max_length=5, unique=True)),
                ('active', models.BooleanField(default=True)),
                ('delivery_steps', models.CharField(choices=[('ship_only', 'Ship Only'), ('pick_ship', 'Pick + Ship'), ('pick_pack_ship', 'Pick + Pack + Ship')], default='ship_only', max_length=16)),
                ('reception_steps', models.CharField(choices=[('one_step', 'Direct'), ('two_steps', 'Input + Stock'), ('three_steps', 'Input + Quality + Stock')], default='one_step', max_length=16)),
                ('sequence', models.IntegerField(default=10)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('lot_stock_id', models.ForeignKey(help_text='Stock Location', on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_lot_stock', to='inventory.stocklocation')),
                ('partner_id', models.ForeignKey(blank=True, help_text='Address', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('resupply_wh_ids', models.ManyToManyField(blank=True, help_text='Resupply Warehouses', to='inventory.stockwarehouse')),
                ('view_location_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_view', to='inventory.stocklocation')),
                ('wh_input_stock_loc_id', models.ForeignKey(help_text='Input Location', on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_input', to='inventory.stocklocation')),
                ('wh_output_stock_loc_id', models.ForeignKey(help_text='Output Location', on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_output', to='inventory.stocklocation')),
                ('wh_pack_stock_loc_id', models.ForeignKey(help_text='Packing Location', on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_pack', to='inventory.stocklocation')),
                ('wh_qc_stock_loc_id', models.ForeignKey(help_text='Quality Control Location', on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_qc', to='inventory.stocklocation')),
            ],
            options={
                'db_table': 'stock_warehouse',
            },
        ),
        migrations.CreateModel(
            name='StockPickingType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('color', models.IntegerField(default=0)),
                ('sequence', models.IntegerField(default=1)),
                ('sequence_code', models.CharField(max_length=5)),
                ('code', models.CharField(choices=[('incoming', 'Receipt'), ('outgoing', 'Delivery'), ('internal', 'Internal Transfer'), ('mrp_operation', 'Manufacturing')], max_length=16)),
                ('active', models.BooleanField(default=True)),
                ('use_create_lots', models.BooleanField(default=True)),
                ('use_existing_lots', models.BooleanField(default=True)),
                ('show_entire_packs', models.BooleanField(default=False)),
                ('show_reserved', models.BooleanField(default=False)),
                ('auto_show_reception_report', models.BooleanField(default=False)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('default_location_dest_id', models.ForeignKey(help_text='Default Destination Location', on_delete=django.db.models.deletion.CASCADE, related_name='dest_picking_types', to='inventory.stocklocation')),
                ('default_location_src_id', models.ForeignKey(help_text='Default Source Location', on_delete=django.db.models.deletion.CASCADE, related_name='src_picking_types', to='inventory.stocklocation')),
                ('warehouse_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='picking_type_ids', to='inventory.stockwarehouse')),
            ],
            options={
                'db_table': 'stock_picking_type',
            },
        ),
        migrations.CreateModel(
            name='StockPicking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='/', max_length=64)),
                ('origin', models.CharField(blank=True, help_text='Source Document', max_length=64)),
                ('note', models.TextField(blank=True)),
                ('date', models.DateTimeField(default=datetime.datetime.now, help_text='Creation Date')),
                ('date_done', models.DateTimeField(blank=True, help_text='Date of Transfer', null=True)),
                ('scheduled_date', models.DateTimeField(default=datetime.datetime.now, help_text='Scheduled Date')),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('waiting', 'Waiting Another Operation'), ('confirmed', 'Waiting'), ('assigned', 'Ready'), ('done', 'Done'), ('cancel', 'Cancelled')], default='draft', max_length=16)),
                ('priority', models.CharField(choices=[('0', 'Normal'), ('1', 'Urgent')], default='0', max_length=1)),
                ('sale_id', models.IntegerField(blank=True, help_text='Sales Order reference', null=True)),
                ('purchase_id', models.IntegerField(blank=True, help_text='Purchase Order reference', null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('backorder_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='backorder_ids', to='inventory.stockpicking')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('location_dest_id', models.ForeignKey(help_text='Destination Location', on_delete=django.db.models.deletion.CASCADE, related_name='picking_location_dest_id', to='inventory.stocklocation')),
                ('location_id', models.ForeignKey(help_text='Source Location', on_delete=django.db.models.deletion.CASCADE, related_name='picking_location_id', to='inventory.stocklocation')),
                ('partner_id', models.ForeignKey(blank=True, help_text='Partner', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('picking_type_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.stockpickingtype')),
                ('user_id', models.ForeignKey(blank=True, help_text='Responsible', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'stock_picking',
            },
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='warehouse_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='location_ids', to='inventory.stockwarehouse'),
        ),
        migrations.CreateModel(
            name='StockInventoryLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('theoretical_qty', models.DecimalField(decimal_places=4, default=0, help_text='Theoretical Quantity', max_digits=16)),
                ('product_qty', models.DecimalField(decimal_places=4, default=0, help_text='Real Quantity', max_digits=16)),
                ('prod_lot_id', models.IntegerField(blank=True, help_text='Lot/Serial Number', null=True)),
                ('package_id', models.IntegerField(blank=True, help_text='Package', null=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('inventory_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='inventory.stockinventory')),
                ('location_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.stocklocation')),
                ('partner_id', models.ForeignKey(blank=True, help_text='Owner', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('product_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
            ],
            options={
                'db_table': 'stock_inventory_line',
            },
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='location_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.stocklocation'),
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='partner_id',
            field=models.ForeignKey(blank=True, help_text='Owner', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner'),
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='product_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct'),
        ),
        migrations.CreateModel(
            name='StockQuant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=4, default=0, help_text='Quantity On Hand', max_digits=16)),
                ('reserved_quantity', models.DecimalField(decimal_places=4, default=0, help_text='Reserved Quantity', max_digits=16)),
                ('lot_id', models.IntegerField(blank=True, help_text='Lot/Serial Number', null=True)),
                ('package_id', models.IntegerField(blank=True, help_text='Package', null=True)),
                ('inventory_quantity', models.DecimalField(decimal_places=4, default=0, help_text='Inventory Quantity', max_digits=16)),
                ('inventory_diff_quantity', models.DecimalField(decimal_places=4, default=0, help_text='Difference', max_digits=16)),
                ('inventory_date', models.DateTimeField(blank=True, help_text='Last Inventory Date', null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('location_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.stocklocation')),
                ('owner_id', models.ForeignKey(blank=True, help_text='Owner', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('product_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
                ('user_id', models.ForeignKey(blank=True, help_text='Responsible User', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'stock_quant',
                'indexes': [models.Index(fields=['product_id', 'location_id'], name='stock_quant_product_abf30f_idx'), models.Index(fields=['location_id', 'quantity'], name='stock_quant_locatio_72fd91_idx')],
                'unique_together': {('product_id', 'location_id', 'lot_id', 'package_id', 'owner_id')},
            },
        ),
        migrations.CreateModel(
            name='StockMove',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Description', max_length=256)),
                ('sequence', models.IntegerField(default=10)),
                ('priority', models.CharField(choices=[('0', 'Normal'), ('1', 'Urgent')], default='0', max_length=1)),
                ('date', models.DateTimeField(default=datetime.datetime.now, help_text='Expected Date')),
                ('date_expected', models.DateTimeField(default=datetime.datetime.now, help_text='Expected Date')),
                ('date_deadline', models.DateTimeField(blank=True, help_text='Deadline', null=True)),
                ('product_uom_qty', models.DecimalField(decimal_places=4, default=1, help_text='Initial Demand', max_digits=16)),
                ('product_uom', models.IntegerField(help_text='Unit of Measure')),
                ('reserved_availability', models.DecimalField(decimal_places=4, default=0, help_text='Quantity Reserved', max_digits=16)),
                ('availability', models.DecimalField(decimal_places=4, default=0, help_text='Forecasted Quantity', max_digits=16)),
                ('quantity_done', models.DecimalField(decimal_places=4, default=0, help_text='Quantity Done', max_digits=16)),
                ('state', models.CharField(choices=[('draft', 'New'), ('cancel', 'Cancelled'), ('waiting', 'Waiting Another Move'), ('confirmed', 'Waiting Availability'), ('partially_available', 'Partially Available'), ('assigned', 'Available'), ('done', 'Done')], default='draft', max_length=20)),
                ('procure_method', models.CharField(choices=[('make_to_stock', 'Take From Stock'), ('make_to_order', 'Trigger Another Rule')], default='make_to_stock', max_length=16)),
                ('origin', models.CharField(blank=True, help_text='Source Document', max_length=64)),
                ('group_id', models.IntegerField(blank=True, help_text='Procurement Group', null=True)),
                ('rule_id', models.IntegerField(blank=True, help_text='Stock Rule', null=True)),
                ('sale_line_id', models.IntegerField(blank=True, help_text='Sales Order Line reference', null=True)),
                ('purchase_line_id', models.IntegerField(blank=True, help_text='Purchase Order Line reference', null=True)),
                ('inventory_id', models.IntegerField(blank=True, help_text='Inventory Adjustment reference', null=True)),
                ('scrapped', models.BooleanField(default=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_move_ids', models.ManyToManyField(blank=True, help_text='Generated accounting entries', to='accounting.accountmove')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('location_dest_id', models.ForeignKey(help_text='Destination Location', on_delete=django.db.models.deletion.CASCADE, related_name='move_location_dest_id', to='inventory.stocklocation')),
                ('location_id', models.ForeignKey(help_text='Source Location', on_delete=django.db.models.deletion.CASCADE, related_name='move_location_id', to='inventory.stocklocation')),
                ('move_orig_ids', models.ManyToManyField(blank=True, help_text='Original Move', related_name='move_dest_ids', to='inventory.stockmove')),
                ('partner_id', models.ForeignKey(blank=True, help_text='Partner', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('picking_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='move_lines', to='inventory.stockpicking')),
                ('picking_type_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.stockpickingtype')),
                ('product_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
            ],
            options={
                'db_table': 'stock_move',
                'indexes': [models.Index(fields=['product_id', 'location_id', 'location_dest_id', 'state'], name='stock_move_product_650a29_idx'), models.Index(fields=['picking_id', 'state'], name='stock_move_picking_912202_idx'), models.Index(fields=['date_expected', 'state'], name='stock_move_date_ex_358b0d_idx')],
            },
        ),
        migrations.AlterUniqueTogether(
            name='stocklocation',
            unique_together={('barcode', 'company_id')},
        ),
    ]
