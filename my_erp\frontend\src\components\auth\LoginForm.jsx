/**
 * Login Form Component - Super User & Admin Authentication
 * Professional ERP Login with Modern UX/UI
 */
import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Space,
  Divider,
  Alert,
  Checkbox,
  Select,
  Row,
  Col,
  Avatar,
  Badge
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  LoginOutlined,
  CrownOutlined,
  SafetyCertificateOutlined,
  GlobalOutlined,
  BuildingOutlined
} from '@ant-design/icons';
import { useAuth } from '../shared/auth/AuthProvider';
import { useNavigate, useLocation } from 'react-router-dom';
import './LoginForm.css';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const LoginForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [selectedUserType, setSelectedUserType] = useState('');
  
  const { login, USER_TYPES } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Redirect path after login
  const from = location.state?.from?.pathname || '/dashboard';

  // Demo users for easy access
  const demoUsers = [
    {
      type: USER_TYPES.SUPER_USER,
      username: 'superuser',
      password: 'superuser123',
      name: 'Super User',
      description: 'Undeletable master/owner of whole ERP',
      icon: <CrownOutlined style={{ color: '#ff4d4f' }} />,
      badge: 'MASTER'
    },
    {
      type: USER_TYPES.ADMIN,
      username: 'admin',
      password: 'admin123',
      name: 'Administrator',
      description: 'Full ERP copy rights with all permissions',
      icon: <SafetyCertificateOutlined style={{ color: '#1890ff' }} />,
      badge: 'ADMIN'
    },
    {
      type: USER_TYPES.USER,
      username: 'user',
      password: 'user123',
      name: 'Regular User',
      description: 'Standard user with group-based permissions',
      icon: <UserOutlined style={{ color: '#52c41a' }} />,
      badge: 'USER'
    }
  ];

  useEffect(() => {
    // Load remembered credentials
    const savedUsername = localStorage.getItem('rememberedUsername');
    const savedUserType = localStorage.getItem('rememberedUserType');
    
    if (savedUsername) {
      form.setFieldsValue({ username: savedUsername });
      setRememberMe(true);
    }
    
    if (savedUserType) {
      setSelectedUserType(savedUserType);
    }
  }, [form]);

  const handleSubmit = async (values) => {
    setLoading(true);
    setError('');

    try {
      const result = await login({
        username: values.username,
        password: values.password,
        user_type: selectedUserType
      });

      if (result.success) {
        // Handle remember me
        if (rememberMe) {
          localStorage.setItem('rememberedUsername', values.username);
          localStorage.setItem('rememberedUserType', selectedUserType);
        } else {
          localStorage.removeItem('rememberedUsername');
          localStorage.removeItem('rememberedUserType');
        }

        navigate(from, { replace: true });
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = (demoUser) => {
    form.setFieldsValue({
      username: demoUser.username,
      password: demoUser.password
    });
    setSelectedUserType(demoUser.type);
  };

  const handleQuickLogin = async (demoUser) => {
    setLoading(true);
    setError('');

    try {
      const result = await login({
        username: demoUser.username,
        password: demoUser.password,
        user_type: demoUser.type
      });

      if (result.success) {
        navigate(from, { replace: true });
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay" />
      </div>
      
      <div className="login-content">
        <Row justify="center" align="middle" style={{ minHeight: '100vh' }}>
          <Col xs={22} sm={20} md={16} lg={12} xl={10} xxl={8}>
            <Card className="login-card">
              {/* Header */}
              <div className="login-header">
                <Space direction="vertical" align="center" size="small">
                  <Avatar 
                    size={64} 
                    icon={<BuildingOutlined />}
                    style={{ 
                      backgroundColor: '#1890ff',
                      fontSize: '28px'
                    }}
                  />
                  <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
                    DataiCraft ERP
                  </Title>
                  <Text type="secondary">
                    Professional Enterprise Resource Planning
                  </Text>
                </Space>
              </div>

              <Divider />

              {/* Error Alert */}
              {error && (
                <Alert
                  message="Login Failed"
                  description={error}
                  type="error"
                  showIcon
                  closable
                  style={{ marginBottom: 24 }}
                  onClose={() => setError('')}
                />
              )}

              {/* Login Form */}
              <Form
                form={form}
                name="login"
                onFinish={handleSubmit}
                layout="vertical"
                size="large"
                className="login-form"
              >
                <Form.Item
                  name="username"
                  label="Username"
                  rules={[
                    { required: true, message: 'Please enter your username!' }
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="Enter username"
                    autoComplete="username"
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  label="Password"
                  rules={[
                    { required: true, message: 'Please enter your password!' }
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="Enter password"
                    autoComplete="current-password"
                    iconRender={(visible) => 
                      visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                    }
                  />
                </Form.Item>

                <Form.Item label="User Type">
                  <Select
                    value={selectedUserType}
                    onChange={setSelectedUserType}
                    placeholder="Select user type (optional)"
                    allowClear
                  >
                    <Option value={USER_TYPES.SUPER_USER}>
                      <Space>
                        <CrownOutlined style={{ color: '#ff4d4f' }} />
                        Super User (Master)
                      </Space>
                    </Option>
                    <Option value={USER_TYPES.ADMIN}>
                      <Space>
                        <SafetyCertificateOutlined style={{ color: '#1890ff' }} />
                        Administrator
                      </Space>
                    </Option>
                    <Option value={USER_TYPES.USER}>
                      <Space>
                        <UserOutlined style={{ color: '#52c41a' }} />
                        Regular User
                      </Space>
                    </Option>
                  </Select>
                </Form.Item>

                <Form.Item>
                  <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                    <Checkbox
                      checked={rememberMe}
                      onChange={(e) => setRememberMe(e.target.checked)}
                    >
                      Remember me
                    </Checkbox>
                    <Button type="link" size="small">
                      Forgot password?
                    </Button>
                  </Space>
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    block
                    size="large"
                    icon={<LoginOutlined />}
                    className="login-button"
                  >
                    {loading ? 'Signing In...' : 'Sign In'}
                  </Button>
                </Form.Item>
              </Form>

              <Divider>
                <Text type="secondary">Quick Demo Access</Text>
              </Divider>

              {/* Demo Users */}
              <div className="demo-users">
                <Row gutter={[8, 8]}>
                  {demoUsers.map((user, index) => (
                    <Col span={24} key={index}>
                      <Card 
                        size="small" 
                        className="demo-user-card"
                        hoverable
                      >
                        <Row align="middle" justify="space-between">
                          <Col flex="auto">
                            <Space>
                              <Badge count={user.badge} size="small">
                                <Avatar size="small" icon={user.icon} />
                              </Badge>
                              <div>
                                <Text strong>{user.name}</Text>
                                <br />
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  {user.description}
                                </Text>
                              </div>
                            </Space>
                          </Col>
                          <Col>
                            <Space>
                              <Button
                                size="small"
                                onClick={() => handleDemoLogin(user)}
                              >
                                Fill Form
                              </Button>
                              <Button
                                type="primary"
                                size="small"
                                loading={loading}
                                onClick={() => handleQuickLogin(user)}
                              >
                                Quick Login
                              </Button>
                            </Space>
                          </Col>
                        </Row>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </div>

              {/* Footer */}
              <div className="login-footer">
                <Divider />
                <Space direction="vertical" align="center" size="small">
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    © 2025 DataiCraft Solutions. All rights reserved.
                  </Text>
                  <Space split={<Divider type="vertical" />}>
                    <Button type="link" size="small">Privacy Policy</Button>
                    <Button type="link" size="small">Terms of Service</Button>
                    <Button type="link" size="small">Support</Button>
                  </Space>
                </Space>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default LoginForm;
