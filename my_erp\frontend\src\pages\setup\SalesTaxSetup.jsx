/**
 * Sales Tax Setup Page - Manage input and output tax rates
 */
import React, { useState, useEffect } from 'react';
import { Table, Space, Modal, message, Popconfirm, Tag, Tabs } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { <PERSON>Header, PageContent, QuickActions } from '../../components/shared/layout/PageLayout';
import { setupAPI } from '../../services/api';
import SalesTaxForm from './components/SalesTaxForm';

const SalesTaxSetup = () => {
  const [salesTaxes, setSalesTaxes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTax, setEditingTax] = useState(null);
  const [formLoading, setFormLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('input');

  // Fetch sales taxes
  const fetchSalesTaxes = async () => {
    setLoading(true);
    try {
      const response = await setupAPI.getSalesTaxes();
      setSalesTaxes(response.data || []);
    } catch (error) {
      message.error('Failed to fetch sales taxes');
      console.error('Fetch sales taxes error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSalesTaxes();
  }, []);

  // Handle create
  const handleCreate = (taxType) => {
    setEditingTax(null);
    setModalVisible(true);
    // Pre-set the tax type based on current tab
    setTimeout(() => {
      // This will be handled in the form component
    }, 100);
  };

  // Handle edit
  const handleEdit = (tax) => {
    setEditingTax(tax);
    setModalVisible(true);
  };

  // Handle delete
  const handleDelete = async (id) => {
    try {
      await setupAPI.deleteSalesTax(id);
      message.success('Sales tax deleted successfully');
      fetchSalesTaxes();
    } catch (error) {
      message.error('Failed to delete sales tax');
      console.error('Delete sales tax error:', error);
    }
  };

  // Handle form submit
  const handleFormSubmit = async (values) => {
    setFormLoading(true);
    try {
      // Add the tax type based on current tab
      const formData = {
        ...values,
        tax_direction: activeTab // 'input' or 'output'
      };

      if (editingTax) {
        await setupAPI.updateSalesTax(editingTax.id, formData);
        message.success('Sales tax updated successfully');
      } else {
        await setupAPI.createSalesTax(formData);
        message.success('Sales tax created successfully');
      }
      setModalVisible(false);
      fetchSalesTaxes();
    } catch (error) {
      message.error(`Failed to ${editingTax ? 'update' : 'create'} sales tax`);
      console.error('Form submit error:', error);
    } finally {
      setFormLoading(false);
    }
  };

  // Filter taxes by type
  const inputTaxes = salesTaxes.filter(tax => tax.tax_direction === 'input');
  const outputTaxes = salesTaxes.filter(tax => tax.tax_direction === 'output');

  // Table columns
  const getColumns = (taxType) => [
    {
      title: 'Tax Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Tax Code',
      dataIndex: 'code',
      key: 'code',
      render: (code) => <Tag color="blue">{code}</Tag>,
    },
    {
      title: 'Rate (%)',
      dataIndex: 'rate',
      key: 'rate',
      render: (rate) => `${rate}%`,
      sorter: (a, b) => a.rate - b.rate,
    },
    {
      title: 'Scope',
      dataIndex: 'scope',
      key: 'scope',
      render: (scope) => (
        <Tag color={scope === 'federal' ? 'red' : scope === 'state' ? 'orange' : 'green'}>
          {scope?.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Account',
      dataIndex: 'account_name',
      key: 'account_name',
      render: (name) => name || '-',
    },
    {
      title: 'Status',
      dataIndex: 'active',
      key: 'active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <QuickActions.Edit
            onClick={() => handleEdit(record)}
            disabled={loading}
          />
          <Popconfirm
            title="Are you sure you want to delete this sales tax?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <QuickActions.Delete disabled={loading} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const breadcrumbs = [
    { title: 'Setup', href: '/setup' },
    { title: 'Sales Tax Setup' }
  ];

  const getActions = (taxType) => [
    {
      label: `New ${taxType === 'input' ? 'Input' : 'Output'} Tax`,
      type: 'primary',
      icon: <PlusOutlined />,
      onClick: () => handleCreate(taxType)
    }
  ];

  return (
    <>
      <PageHeader
        title="Sales Tax Setup"
        subtitle="Configure input tax (VAT paid) and output tax (VAT collected) rates"
        breadcrumbs={breadcrumbs}
      />
      
      <PageContent>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            <QuickActions.Create
              onClick={() => handleCreate(activeTab)}
              loading={loading}
            >
              New {activeTab === 'input' ? 'Input' : 'Output'} Tax
            </QuickActions.Create>
          }
          items={[
            {
              key: 'input',
              label: (
                <span>
                  Input Tax (VAT Paid)
                  <Tag color="orange" style={{ marginLeft: 8 }}>
                    {inputTaxes.length}
                  </Tag>
                </span>
              ),
              children: (
                <Table
                  columns={getColumns('input')}
                  dataSource={inputTaxes}
                  loading={loading}
                  rowKey="id"
                  pagination={{
                    total: inputTaxes.length,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `${range[0]}-${range[1]} of ${total} input taxes`,
                  }}
                  scroll={{ x: 1000 }}
                />
              )
            },
            {
              key: 'output',
              label: (
                <span>
                  Output Tax (VAT Collected)
                  <Tag color="green" style={{ marginLeft: 8 }}>
                    {outputTaxes.length}
                  </Tag>
                </span>
              ),
              children: (
                <Table
                  columns={getColumns('output')}
                  dataSource={outputTaxes}
                  loading={loading}
                  rowKey="id"
                  pagination={{
                    total: outputTaxes.length,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `${range[0]}-${range[1]} of ${total} output taxes`,
                  }}
                  scroll={{ x: 1000 }}
                />
              )
            }
          ]}
        />
      </PageContent>

      <Modal
        title={`${editingTax ? 'Edit' : 'Create New'} ${activeTab === 'input' ? 'Input' : 'Output'} Tax`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
        destroyOnClose
      >
        <SalesTaxForm
          initialValues={editingTax}
          taxDirection={activeTab}
          onSubmit={handleFormSubmit}
          onCancel={() => setModalVisible(false)}
          loading={formLoading}
        />
      </Modal>
    </>
  );
};

export default SalesTaxSetup;
