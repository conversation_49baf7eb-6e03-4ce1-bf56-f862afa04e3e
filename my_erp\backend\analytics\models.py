"""
Analytics/Reporting Module Models - Complete Odoo Business Intelligence
Based on Odoo's Reporting and Analytics Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime, timedelta
import json

# Import models from other modules for integration
from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountAnalyticAccount, AccountAnalyticLine
)


class IrUiView(models.Model):
    """UI View - Based on Odoo ir.ui.view for custom views and dashboards"""

    VIEW_TYPES = [
        ('tree', 'Tree'),
        ('form', 'Form'),
        ('kanban', 'Kanban'),
        ('calendar', 'Calendar'),
        ('pivot', 'Pivot'),
        ('graph', 'Graph'),
        ('dashboard', 'Dashboard'),
        ('search', 'Search'),
    ]

    name = models.Char<PERSON>ield(max_length=128)
    model = models.CharField(max_length=64, help_text="Model")
    type = models.CharField(max_length=16, choices=VIEW_TYPES, default='tree')

    # View Definition
    arch = models.TextField(help_text="View Architecture (XML)")
    arch_db = models.TextField(blank=True, help_text="Arch Stored in DB")

    # Inheritance
    inherit_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, help_text="Inherited View")
    mode = models.CharField(max_length=16, choices=[('primary', 'Base view'), ('extension', 'Extension View')], default='primary')

    # Properties
    active = models.BooleanField(default=True)
    priority = models.IntegerField(default=16)

    # Groups (Security)
    groups_id = models.CharField(max_length=256, blank=True, help_text="Groups")

    class Meta:
        db_table = 'ir_ui_view'

    def __str__(self):
        return f"{self.name} ({self.model})"


class IrFilters(models.Model):
    """Saved Filters - Based on Odoo ir.filters"""

    name = models.CharField(max_length=64)
    model_id = models.CharField(max_length=64, help_text="Model")

    # Filter Definition
    domain = models.TextField(help_text="Filter Domain")
    context = models.TextField(default='{}', help_text="Context")
    sort = models.TextField(default='[]', help_text="Sort Order")

    # User and Sharing
    user_id = models.ForeignKey(User, null=True, blank=True, on_delete=models.CASCADE, help_text="User (if personal)")
    is_default = models.BooleanField(default=False, help_text="Default Filter")

    # Properties
    active = models.BooleanField(default=True)
    action_id = models.IntegerField(null=True, blank=True, help_text="Action")

    class Meta:
        db_table = 'ir_filters'

    def __str__(self):
        return f"{self.name} ({self.model_id})"


class IrActWindow(models.Model):
    """Window Actions - Based on Odoo ir.actions.act_window"""

    VIEW_MODES = [
        ('tree', 'List'),
        ('form', 'Form'),
        ('kanban', 'Kanban'),
        ('calendar', 'Calendar'),
        ('pivot', 'Pivot'),
        ('graph', 'Graph'),
    ]

    name = models.CharField(max_length=128)
    type = models.CharField(max_length=32, default='ir.actions.act_window')

    # Target Model
    res_model = models.CharField(max_length=64, help_text="Model")

    # Views
    view_mode = models.CharField(max_length=256, default='tree,form', help_text="View Mode")
    view_id = models.ForeignKey(IrUiView, null=True, blank=True, on_delete=models.SET_NULL, help_text="View")

    # Domain and Context
    domain = models.TextField(default='[]', help_text="Domain")
    context = models.TextField(default='{}', help_text="Context")

    # Target
    target = models.CharField(max_length=16, choices=[('current', 'Current Window'), ('new', 'New Window'), ('inline', 'Inline')], default='current')

    # Limit
    limit = models.IntegerField(default=80, help_text="Record Limit")

    # Auto Search
    auto_search = models.BooleanField(default=True, help_text="Auto Search")

    class Meta:
        db_table = 'ir_act_window'

    def __str__(self):
        return self.name


class BaseImport(models.Model):
    """Data Import - Based on Odoo base_import.import"""

    STATES = [
        ('draft', 'Draft'),
        ('cancel', 'Cancelled'),
        ('done', 'Done'),
    ]

    # Import Information
    res_model = models.CharField(max_length=64, help_text="Model")
    file = models.FileField(upload_to='imports/', null=True, blank=True, help_text="File")
    file_name = models.CharField(max_length=256, blank=True, help_text="File Name")
    file_type = models.CharField(max_length=16, choices=[('csv', 'CSV'), ('xls', 'XLS'), ('xlsx', 'XLSX')], default='csv')

    # State
    state = models.CharField(max_length=16, choices=STATES, default='draft')

    # Results
    import_messages = models.TextField(blank=True, help_text="Import Messages")

    # User
    create_uid = models.ForeignKey(User, on_delete=models.CASCADE, help_text="Created by")

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'base_import'

    def __str__(self):
        return f"Import {self.res_model} - {self.file_name}"


class IrCron(models.Model):
    """Scheduled Actions - Based on Odoo ir.cron"""

    INTERVAL_TYPES = [
        ('minutes', 'Minutes'),
        ('hours', 'Hours'),
        ('days', 'Days'),
        ('weeks', 'Weeks'),
        ('months', 'Months'),
    ]

    name = models.CharField(max_length=128)
    active = models.BooleanField(default=True)

    # Scheduling
    interval_number = models.IntegerField(default=1, help_text="Repeat Every")
    interval_type = models.CharField(max_length=16, choices=INTERVAL_TYPES, default='months')
    numbercall = models.IntegerField(default=-1, help_text="Number of Calls (-1 = unlimited)")
    doall = models.BooleanField(default=True, help_text="Repeat Missed")

    # Execution
    model_id = models.CharField(max_length=64, help_text="Model")
    function = models.CharField(max_length=64, help_text="Function")
    args = models.TextField(default='()', help_text="Arguments")

    # Next Execution
    nextcall = models.DateTimeField(default=datetime.now, help_text="Next Execution Date")
    lastcall = models.DateTimeField(null=True, blank=True, help_text="Last Execution Date")

    # Priority
    priority = models.IntegerField(default=5, help_text="Priority")

    # User
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, help_text="User")

    class Meta:
        db_table = 'ir_cron'

    def __str__(self):
        return self.name


class AnalyticsDashboard(models.Model):
    """Analytics Dashboard - Custom model for business intelligence"""

    DASHBOARD_TYPES = [
        ('sales', 'Sales Dashboard'),
        ('financial', 'Financial Dashboard'),
        ('hr', 'HR Dashboard'),
        ('inventory', 'Inventory Dashboard'),
        ('project', 'Project Dashboard'),
        ('crm', 'CRM Dashboard'),
        ('executive', 'Executive Dashboard'),
    ]

    name = models.CharField(max_length=128)
    type = models.CharField(max_length=16, choices=DASHBOARD_TYPES, default='executive')
    active = models.BooleanField(default=True)

    # Configuration
    config = models.TextField(default='{}', help_text="Dashboard Configuration (JSON)")

    # Access
    user_ids = models.ManyToManyField(User, blank=True, help_text="Allowed Users")
    public = models.BooleanField(default=False, help_text="Public Dashboard")

    # Layout
    layout = models.TextField(default='{}', help_text="Dashboard Layout (JSON)")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'analytics_dashboard'

    def __str__(self):
        return self.name

    def get_config(self):
        """Get dashboard configuration as dict"""
        try:
            return json.loads(self.config)
        except:
            return {}

    def set_config(self, config_dict):
        """Set dashboard configuration from dict"""
        self.config = json.dumps(config_dict)
        self.save()


class AnalyticsKpi(models.Model):
    """Key Performance Indicators - Custom model for KPI tracking"""

    KPI_TYPES = [
        ('revenue', 'Revenue'),
        ('profit', 'Profit'),
        ('sales_count', 'Sales Count'),
        ('customer_count', 'Customer Count'),
        ('employee_count', 'Employee Count'),
        ('project_count', 'Project Count'),
        ('task_completion', 'Task Completion Rate'),
        ('inventory_turnover', 'Inventory Turnover'),
        ('custom', 'Custom KPI'),
    ]

    PERIODS = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('yearly', 'Yearly'),
    ]

    name = models.CharField(max_length=128)
    type = models.CharField(max_length=32, choices=KPI_TYPES, default='custom')
    active = models.BooleanField(default=True)

    # Value
    current_value = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Current Value")
    target_value = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Target Value")
    previous_value = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Previous Period Value")

    # Period
    period = models.CharField(max_length=16, choices=PERIODS, default='monthly')

    # Calculation
    model_id = models.CharField(max_length=64, blank=True, help_text="Source Model")
    field_name = models.CharField(max_length=64, blank=True, help_text="Field Name")
    domain = models.TextField(default='[]', help_text="Filter Domain")

    # Display
    suffix = models.CharField(max_length=16, blank=True, help_text="Value Suffix (%, $, etc.)")
    color = models.CharField(max_length=16, choices=[('green', 'Green'), ('yellow', 'Yellow'), ('red', 'Red')], default='green')

    # Dashboard
    dashboard_id = models.ForeignKey(AnalyticsDashboard, null=True, blank=True, on_delete=models.CASCADE, related_name='kpi_ids')

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)
    last_update = models.DateTimeField(null=True, blank=True, help_text="Last Update")

    class Meta:
        db_table = 'analytics_kpi'

    def __str__(self):
        return self.name

    @property
    def variance(self):
        """Calculate variance from target"""
        if self.target_value:
            return ((self.current_value - self.target_value) / self.target_value) * 100
        return 0

    @property
    def growth(self):
        """Calculate growth from previous period"""
        if self.previous_value:
            return ((self.current_value - self.previous_value) / self.previous_value) * 100
        return 0

    def update_value(self):
        """Update KPI value based on configuration"""
        # This would contain logic to calculate KPI values
        # from the source model and field
        self.last_update = datetime.now()
        self.save()


class AnalyticsReport(models.Model):
    """Analytics Report - Custom model for business reports"""

    REPORT_TYPES = [
        ('sales_report', 'Sales Report'),
        ('financial_report', 'Financial Report'),
        ('hr_report', 'HR Report'),
        ('inventory_report', 'Inventory Report'),
        ('project_report', 'Project Report'),
        ('crm_report', 'CRM Report'),
        ('custom_report', 'Custom Report'),
    ]

    OUTPUT_FORMATS = [
        ('pdf', 'PDF'),
        ('xlsx', 'Excel'),
        ('csv', 'CSV'),
        ('html', 'HTML'),
    ]

    name = models.CharField(max_length=128)
    type = models.CharField(max_length=32, choices=REPORT_TYPES, default='custom_report')
    active = models.BooleanField(default=True)

    # Configuration
    model_id = models.CharField(max_length=64, help_text="Source Model")
    domain = models.TextField(default='[]', help_text="Filter Domain")
    fields = models.TextField(default='[]', help_text="Report Fields (JSON)")
    groupby = models.TextField(default='[]', help_text="Group By Fields (JSON)")

    # Output
    output_format = models.CharField(max_length=16, choices=OUTPUT_FORMATS, default='pdf')

    # Scheduling
    auto_generate = models.BooleanField(default=False, help_text="Auto Generate")
    schedule_cron_id = models.ForeignKey(IrCron, null=True, blank=True, on_delete=models.SET_NULL, help_text="Scheduled Action")

    # Access
    user_ids = models.ManyToManyField(User, blank=True, help_text="Allowed Users")
    public = models.BooleanField(default=False, help_text="Public Report")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'analytics_report'

    def __str__(self):
        return self.name

    def generate_report(self):
        """Generate the report"""
        # This would contain logic to generate the actual report
        # based on the configuration
        pass
