"""
Setup Serializers - API serializers for setup functionality
Following DRF best practices with comprehensive validation
"""
from rest_framework import serializers
from .models import (
    Country, Currency, Timezone, CompanySetup, BankAccount,
    TaxConfiguration, WithholdingTaxConfiguration, PaymentTerm, SetupWizardStep, SystemConfiguration,
    SetupTemplate
)

class CountrySerializer(serializers.ModelSerializer):
    """Country serializer for localization"""
    
    class Meta:
        model = Country
        fields = [
            'id', 'name', 'code', 'phone_code', 'currency_code',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class CurrencySerializer(serializers.ModelSerializer):
    """Currency serializer for multi-currency support"""
    
    class Meta:
        model = Currency
        fields = [
            'id', 'name', 'code', 'symbol', 'decimal_places', 'rounding',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class TimezoneSerializer(serializers.ModelSerializer):
    """Timezone serializer for localization"""
    
    class Meta:
        model = Timezone
        fields = [
            'id', 'name', 'offset', 'description', 'is_active'
        ]

class BankAccountSerializer(serializers.ModelSerializer):
    """Bank account serializer"""
    
    class Meta:
        model = BankAccount
        fields = [
            'id', 'company', 'bank_name', 'account_number', 'account_holder_name',
            'iban', 'swift_code', 'branch_name', 'branch_code', 'is_default',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_account_number(self, value):
        """Validate account number format"""
        if not value.replace('-', '').replace(' ', '').isdigit():
            raise serializers.ValidationError("Account number should contain only digits, spaces, and hyphens")
        return value

class TaxConfigurationSerializer(serializers.ModelSerializer):
    """Tax configuration serializer"""

    class Meta:
        model = TaxConfiguration
        fields = [
            'id', 'company', 'name', 'rate', 'tax_type', 'is_default',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_rate(self, value):
        """Validate tax rate"""
        if value < 0 or value > 100:
            raise serializers.ValidationError("Tax rate must be between 0 and 100")
        return value


class WithholdingTaxConfigurationSerializer(serializers.ModelSerializer):
    """Withholding tax configuration serializer"""

    class Meta:
        model = WithholdingTaxConfiguration
        fields = [
            'id', 'company', 'name', 'code', 'rate', 'tax_type', 'threshold_amount',
            'exemption_limit', 'applicable_to', 'calculation_base', 'requires_certificate',
            'certificate_series', 'quarterly_return_required', 'challan_required',
            'payment_due_date', 'return_due_date', 'withholding_account',
            'is_default', 'is_active', 'effective_from', 'effective_to',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_rate(self, value):
        """Validate tax rate"""
        if value < 0 or value > 100:
            raise serializers.ValidationError("Tax rate must be between 0 and 100")
        return value

    def validate(self, data):
        """Validate withholding tax configuration"""
        if data.get('effective_from') and data.get('effective_to'):
            if data['effective_from'] >= data['effective_to']:
                raise serializers.ValidationError("Effective from date must be before effective to date")

        return data

class PaymentTermSerializer(serializers.ModelSerializer):
    """Payment term serializer"""
    
    class Meta:
        model = PaymentTerm
        fields = [
            'id', 'company', 'name', 'days', 'description', 'is_default',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_days(self, value):
        """Validate payment term days"""
        if value < 0:
            raise serializers.ValidationError("Payment term days cannot be negative")
        return value

class SetupWizardStepSerializer(serializers.ModelSerializer):
    """Setup wizard step serializer"""
    
    class Meta:
        model = SetupWizardStep
        fields = [
            'id', 'company', 'step_name', 'step_order', 'is_completed',
            'completed_at', 'data', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class CompanySetupSerializer(serializers.ModelSerializer):
    """Main company setup serializer with nested relationships"""
    
    # Nested serializers for related data
    country_details = CountrySerializer(source='country', read_only=True)
    currency_details = CurrencySerializer(source='currency', read_only=True)
    timezone_details = TimezoneSerializer(source='timezone', read_only=True)
    
    # Related objects
    bank_accounts = BankAccountSerializer(many=True, read_only=True)
    tax_configurations = TaxConfigurationSerializer(many=True, read_only=True)
    payment_terms = PaymentTermSerializer(many=True, read_only=True)
    wizard_steps = SetupWizardStepSerializer(many=True, read_only=True)
    
    class Meta:
        model = CompanySetup
        fields = [
            'id', 'name', 'legal_name', 'email', 'phone', 'website',
            'street', 'street2', 'city', 'state', 'zip', 'country', 'country_details',
            'currency', 'currency_details', 'timezone', 'timezone_details',
            'language', 'date_format', 'time_format',
            'decimal_precision', 'thousands_separator', 'decimal_separator',
            'fiscal_year_start', 'fiscal_year_end',
            'logo', 'favicon', 'tax_calculation_rounding_method',
            'setup_completed', 'setup_progress', 'setup_step',
            'bank_accounts', 'tax_configurations', 'payment_terms', 'wizard_steps',
            'created_by', 'created_at', 'updated_at', 'is_active'
        ]
        read_only_fields = ['setup_progress', 'created_at', 'updated_at']

    def validate_email(self, value):
        """Validate email format"""
        if not value:
            raise serializers.ValidationError("Email is required")
        return value

    def validate_phone(self, value):
        """Validate phone number"""
        if value and len(value.replace(' ', '').replace('-', '').replace('+', '')) < 10:
            raise serializers.ValidationError("Phone number must be at least 10 digits")
        return value

    def validate_fiscal_year_start(self, value):
        """Validate fiscal year start date"""
        if value and self.instance and self.instance.fiscal_year_end:
            if value >= self.instance.fiscal_year_end:
                raise serializers.ValidationError("Fiscal year start must be before end date")
        return value

    def validate_fiscal_year_end(self, value):
        """Validate fiscal year end date"""
        if value and self.instance and self.instance.fiscal_year_start:
            if value <= self.instance.fiscal_year_start:
                raise serializers.ValidationError("Fiscal year end must be after start date")
        return value

    def create(self, validated_data):
        """Create company setup with current user"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """Update company setup and recalculate progress"""
        instance = super().update(instance, validated_data)
        instance.calculate_setup_progress()
        instance.save()
        return instance

class CompanySetupSummarySerializer(serializers.ModelSerializer):
    """Simplified company setup serializer for lists"""
    
    country_name = serializers.CharField(source='country.name', read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    
    class Meta:
        model = CompanySetup
        fields = [
            'id', 'name', 'email', 'phone', 'city', 'country_name',
            'currency_code', 'setup_completed', 'setup_progress',
            'is_active', 'created_at', 'updated_at'
        ]

class SystemConfigurationSerializer(serializers.ModelSerializer):
    """System configuration serializer"""
    
    class Meta:
        model = SystemConfiguration
        fields = [
            'id', 'key', 'value', 'description', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class SetupTemplateSerializer(serializers.ModelSerializer):
    """Setup template serializer"""
    
    class Meta:
        model = SetupTemplate
        fields = [
            'id', 'name', 'description', 'business_type', 'template_data',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class SetupProgressSerializer(serializers.Serializer):
    """Serializer for setup progress tracking"""
    
    total_steps = serializers.IntegerField(read_only=True)
    completed_steps = serializers.IntegerField(read_only=True)
    current_step = serializers.CharField(read_only=True)
    progress_percentage = serializers.IntegerField(read_only=True)
    next_step = serializers.CharField(read_only=True)
    is_completed = serializers.BooleanField(read_only=True)
    
    steps = serializers.ListField(
        child=serializers.DictField(),
        read_only=True
    )

class SetupValidationSerializer(serializers.Serializer):
    """Serializer for setup validation"""
    
    is_valid = serializers.BooleanField(read_only=True)
    errors = serializers.ListField(
        child=serializers.CharField(),
        read_only=True
    )
    warnings = serializers.ListField(
        child=serializers.CharField(),
        read_only=True
    )
    missing_fields = serializers.ListField(
        child=serializers.CharField(),
        read_only=True
    )
