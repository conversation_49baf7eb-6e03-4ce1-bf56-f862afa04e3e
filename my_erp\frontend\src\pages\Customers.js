import React, { useState, useEffect } from 'react';
import { Table, Button, Typography, Tag, Space, Avatar } from 'antd';
import { PlusOutlined, EditOutlined, UserOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Customers = () => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(false);

  // Mock data
  const mockCustomers = [
    {
      id: 1,
      name: 'ABC Company Ltd.',
      email: '<EMAIL>',
      phone: '+92-300-1234567',
      city: 'Karachi',
      customer_rank: 1,
      outstanding_balance: 25000,
      active: true,
    },
    {
      id: 2,
      name: 'XYZ Corporation',
      email: '<EMAIL>',
      phone: '+92-321-7654321',
      city: 'Lahore',
      customer_rank: 1,
      outstanding_balance: 0,
      active: true,
    },
  ];

  const columns = [
    {
      title: 'Customer',
      dataIndex: 'name',
      key: 'name',
      render: (name) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          {name}
        </Space>
      ),
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: 'City',
      dataIndex: 'city',
      key: 'city',
    },
    {
      title: 'Outstanding',
      dataIndex: 'outstanding_balance',
      key: 'outstanding_balance',
      render: (balance) => (
        <span className={balance > 0 ? 'balance-negative' : 'balance-positive'}>
          PKR {balance.toLocaleString()}
        </span>
      ),
      align: 'right',
    },
    {
      title: 'Status',
      dataIndex: 'active',
      key: 'active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <Space>
          <Button type="text" icon={<EditOutlined />} size="small" />
        </Space>
      ),
    },
  ];

  useEffect(() => {
    setCustomers(mockCustomers);
  }, []);

  return (
    <div>
      <div className="page-header">
        <Title level={2}>Customers</Title>
        <p>Manage your customer database</p>
      </div>

      <div className="action-buttons">
        <Button type="primary" icon={<PlusOutlined />}>
          Add Customer
        </Button>
      </div>

      <div className="table-container">
        <Table
          columns={columns}
          dataSource={customers}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} customers`,
          }}
        />
      </div>
    </div>
  );
};

export default Customers;
