from django.contrib import admin
from .models import UserProfile, UserGroup, UserGroupMembership, UserSession, LoginAttempt

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'name', 'user_type', 'department', 'created_at']
    list_filter = ['user_type', 'department', 'created_at']
    search_fields = ['user__username', 'user__email', 'name']

@admin.register(UserGroup)
class UserGroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'module', 'can_read', 'can_write', 'can_create', 'can_delete']
    list_filter = ['module', 'can_read', 'can_write', 'can_create', 'can_delete']
    search_fields = ['name', 'code']

@admin.register(UserGroupMembership)
class UserGroupMembershipAdmin(admin.ModelAdmin):
    list_display = ['user', 'group', 'granted_by', 'granted_at']
    list_filter = ['group', 'granted_at']
    search_fields = ['user__username', 'group__name']

@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    list_display = ['user', 'session_key', 'ip_address', 'created_at', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['user__username', 'session_key', 'ip_address']

@admin.register(LoginAttempt)
class LoginAttemptAdmin(admin.ModelAdmin):
    list_display = ['email', 'success', 'ip_address', 'attempted_at']
    list_filter = ['success', 'attempted_at']
    search_fields = ['email', 'ip_address']
