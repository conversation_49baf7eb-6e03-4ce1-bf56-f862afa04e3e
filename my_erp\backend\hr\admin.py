"""
HR Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import (
    HrDepartment, HrJob, HrEmployee, HrContractType, HrContract,
    HrLeaveType, HrLeave, HrAttendance, HrPayslip
)


@admin.register(HrDepartment)
class HrDepartmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'complete_name', 'manager_id', 'active', 'employee_count']
    list_filter = ['active', 'company_id']
    search_fields = ['name', 'complete_name']
    ordering = ['complete_name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'active', 'parent_id')
        }),
        ('Management', {
            'fields': ('manager_id',)
        }),
        ('Settings', {
            'fields': ('color', 'note'),
            'classes': ('collapse',)
        }),
    )

    def employee_count(self, obj):
        count = obj.employee_ids.filter(active=True).count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    employee_count.short_description = 'Employees'


@admin.register(HrJob)
class HrJobAdmin(admin.ModelAdmin):
    list_display = ['name', 'department_id', 'state', 'no_of_employee', 'no_of_recruitment', 'active']
    list_filter = ['state', 'active', 'department_id', 'company_id']
    search_fields = ['name', 'description']
    ordering = ['sequence', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'active', 'sequence', 'department_id')
        }),
        ('Job Details', {
            'fields': ('description', 'requirements')
        }),
        ('Recruitment', {
            'fields': ('state', 'no_of_recruitment', 'no_of_hired_employee')
        }),
        ('Contract', {
            'fields': ('contract_type_id',),
            'classes': ('collapse',)
        }),
    )


class HrContractInline(admin.TabularInline):
    model = HrContract
    extra = 0
    fields = ['name', 'type_id', 'date_start', 'date_end', 'wage', 'state']
    readonly_fields = ['state']


@admin.register(HrEmployee)
class HrEmployeeAdmin(admin.ModelAdmin):
    list_display = ['name', 'department_id', 'job_id', 'parent_id', 'work_email', 'active', 'age_display']
    list_filter = ['active', 'gender', 'marital', 'department_id', 'job_id', 'employee_type', 'company_id']
    search_fields = ['name', 'work_email', 'private_email', 'identification_id']
    inlines = [HrContractInline]
    ordering = ['name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'active', 'user_id')
        }),
        ('Personal Information', {
            'fields': ('private_name', 'gender', 'birthday', 'marital', 'spouse_complete_name', 'spouse_birthdate', 'children')
        }),
        ('Private Contact', {
            'fields': ('private_street', 'private_street2', 'private_city', 'private_state_id', 'private_zip', 'private_country_id', 'private_phone', 'private_email'),
            'classes': ('collapse',)
        }),
        ('Work Information', {
            'fields': ('department_id', 'job_id', 'parent_id', 'coach_id', 'employee_type')
        }),
        ('Work Contact', {
            'fields': ('work_phone', 'work_email', 'work_location')
        }),
        ('Identification', {
            'fields': ('identification_id', 'passport_id', 'bank_account_id', 'pin', 'barcode'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('address_home_id', 'color', 'notes'),
            'classes': ('collapse',)
        }),
    )

    def age_display(self, obj):
        age = obj.age
        if age:
            return format_html('<span>{} years</span>', age)
        return '-'
    age_display.short_description = 'Age'


@admin.register(HrContractType)
class HrContractTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'sequence']
    ordering = ['sequence', 'name']


@admin.register(HrContract)
class HrContractAdmin(admin.ModelAdmin):
    list_display = ['name', 'employee_id', 'type_id', 'date_start', 'date_end', 'wage_display', 'state']
    list_filter = ['state', 'type_id', 'company_id']
    search_fields = ['name', 'employee_id__name']
    ordering = ['-date_start', 'employee_id__name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'employee_id', 'state', 'sequence')
        }),
        ('Contract Details', {
            'fields': ('type_id', 'date_start', 'date_end', 'trial_date_end')
        }),
        ('Work Details', {
            'fields': ('department_id', 'job_id', 'working_hours')
        }),
        ('Salary', {
            'fields': ('wage', 'currency_id')
        }),
        ('Other', {
            'fields': ('notes', 'analytic_account_id'),
            'classes': ('collapse',)
        }),
    )

    def wage_display(self, obj):
        return format_html(
            '<span style="font-weight: bold; color: green;">{} {:,.2f}</span>',
            obj.currency_id, obj.wage
        )
    wage_display.short_description = 'Wage'

    actions = ['action_start_contracts', 'action_close_contracts']

    def action_start_contracts(self, request, queryset):
        started = 0
        for contract in queryset:
            if contract.action_start():
                started += 1
        self.message_user(request, f'{started} contracts started successfully.')
    action_start_contracts.short_description = "Start selected contracts"

    def action_close_contracts(self, request, queryset):
        closed = 0
        for contract in queryset:
            if contract.action_close():
                closed += 1
        self.message_user(request, f'{closed} contracts closed successfully.')
    action_close_contracts.short_description = "Close selected contracts"


@admin.register(HrLeaveType)
class HrLeaveTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'allocation_type', 'max_leaves', 'leaves_taken', 'double_validation', 'active']
    list_filter = ['allocation_type', 'double_validation', 'active', 'company_id']
    search_fields = ['name']
    ordering = ['sequence', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'sequence', 'active')
        }),
        ('Configuration', {
            'fields': ('allocation_type', 'validity_start', 'validity_stop')
        }),
        ('Limits', {
            'fields': ('max_leaves', 'leaves_taken')
        }),
        ('Settings', {
            'fields': ('double_validation', 'color_name', 'color'),
            'classes': ('collapse',)
        }),
    )


@admin.register(HrLeave)
class HrLeaveAdmin(admin.ModelAdmin):
    list_display = ['employee_id', 'holiday_status_id', 'request_date_from', 'request_date_to', 'number_of_days', 'state', 'manager_id']
    list_filter = ['state', 'request_type', 'holiday_status_id', 'department_id', 'company_id']
    search_fields = ['employee_id__name', 'notes']
    ordering = ['-request_date_from', 'employee_id__name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'employee_id', 'holiday_status_id', 'request_type', 'state')
        }),
        ('Dates', {
            'fields': ('date_from', 'date_to', 'request_date_from', 'request_date_to')
        }),
        ('Duration', {
            'fields': ('number_of_days', 'number_of_hours_display')
        }),
        ('Approval', {
            'fields': ('manager_id', 'user_id')
        }),
        ('Details', {
            'fields': ('notes',)
        }),
    )

    actions = ['action_approve_leaves', 'action_refuse_leaves']

    def action_approve_leaves(self, request, queryset):
        approved = 0
        for leave in queryset:
            if leave.action_approve():
                approved += 1
        self.message_user(request, f'{approved} leaves approved successfully.')
    action_approve_leaves.short_description = "Approve selected leaves"

    def action_refuse_leaves(self, request, queryset):
        refused = 0
        for leave in queryset:
            if leave.action_refuse():
                refused += 1
        self.message_user(request, f'{refused} leaves refused successfully.')
    action_refuse_leaves.short_description = "Refuse selected leaves"


@admin.register(HrAttendance)
class HrAttendanceAdmin(admin.ModelAdmin):
    list_display = ['employee_id', 'check_in', 'check_out', 'worked_hours_display']
    list_filter = ['check_in', 'company_id']
    search_fields = ['employee_id__name']
    ordering = ['-check_in']

    fieldsets = (
        ('Attendance', {
            'fields': ('employee_id', 'check_in', 'check_out', 'worked_hours')
        }),
    )

    readonly_fields = ['worked_hours']

    def worked_hours_display(self, obj):
        if obj.worked_hours:
            hours = int(obj.worked_hours)
            minutes = int((obj.worked_hours - hours) * 60)
            return format_html('<span style="font-weight: bold;">{}h {}m</span>', hours, minutes)
        return '-'
    worked_hours_display.short_description = 'Worked Hours'


@admin.register(HrPayslip)
class HrPayslipAdmin(admin.ModelAdmin):
    list_display = ['name', 'employee_id', 'date_from', 'date_to', 'basic_wage_display', 'net_wage_display', 'state']
    list_filter = ['state', 'date_from', 'company_id']
    search_fields = ['name', 'employee_id__name', 'number']
    ordering = ['-date_from', 'employee_id__name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'number', 'employee_id', 'state')
        }),
        ('Period', {
            'fields': ('date_from', 'date_to')
        }),
        ('Contract', {
            'fields': ('contract_id',)
        }),
        ('Amounts', {
            'fields': ('basic_wage', 'net_wage')
        }),
        ('Accounting', {
            'fields': ('move_id',),
            'classes': ('collapse',)
        }),
        ('Notes', {
            'fields': ('note',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['move_id']

    def basic_wage_display(self, obj):
        return format_html('<span style="color: blue;">{:,.2f}</span>', obj.basic_wage)
    basic_wage_display.short_description = 'Basic Wage'

    def net_wage_display(self, obj):
        return format_html('<span style="color: green; font-weight: bold;">{:,.2f}</span>', obj.net_wage)
    net_wage_display.short_description = 'Net Wage'

    actions = ['action_compute_payslips', 'action_confirm_payslips']

    def action_compute_payslips(self, request, queryset):
        computed = 0
        for payslip in queryset:
            payslip.compute_sheet()
            computed += 1
        self.message_user(request, f'{computed} payslips computed successfully.')
    action_compute_payslips.short_description = "Compute selected payslips"

    def action_confirm_payslips(self, request, queryset):
        confirmed = 0
        for payslip in queryset:
            if payslip.action_payslip_done():
                confirmed += 1
        self.message_user(request, f'{confirmed} payslips confirmed successfully.')
    action_confirm_payslips.short_description = "Confirm selected payslips"


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - HR Module"
