# Generated by Django 4.2.21 on 2025-07-16 05:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('setup', '0002_incometaxconfiguration'),
    ]

    operations = [
        migrations.CreateModel(
            name='WithholdingTaxConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Withholding tax name (e.g., Professional Services TDS)', max_length=100)),
                ('code', models.Char<PERSON>ield(help_text='Tax code (e.g., TDS194J)', max_length=20)),
                ('rate', models.DecimalField(decimal_places=2, help_text='Withholding tax rate percentage', max_digits=5)),
                ('tax_type', models.CharField(choices=[('professional_services', 'Professional Services'), ('contractor_payments', 'Contractor Payments'), ('rent_payments', 'Rent Payments'), ('commission_payments', 'Commission Payments'), ('interest_payments', 'Interest Payments'), ('salary_payments', 'Salary Payments'), ('dividend_payments', 'Dividend Payments'), ('royalty_payments', 'Royalty Payments'), ('other', 'Other')], default='professional_services', max_length=30)),
                ('threshold_amount', models.DecimalField(decimal_places=2, default=0, help_text='Minimum amount above which tax should be deducted', max_digits=15)),
                ('exemption_limit', models.DecimalField(decimal_places=2, default=0, help_text='Annual exemption limit for this tax type', max_digits=15)),
                ('applicable_to', models.CharField(choices=[('vendors', 'Vendors/Suppliers'), ('employees', 'Employees'), ('customers', 'Customers'), ('all', 'All Parties')], default='vendors', max_length=20)),
                ('calculation_base', models.CharField(choices=[('gross_amount', 'Gross Amount'), ('net_amount', 'Net Amount'), ('taxable_amount', 'Taxable Amount')], default='gross_amount', max_length=20)),
                ('requires_certificate', models.BooleanField(default=False, help_text='Whether TDS certificate is required')),
                ('certificate_series', models.CharField(blank=True, help_text='TDS certificate series (e.g., A, B, C)', max_length=10)),
                ('quarterly_return_required', models.BooleanField(default=True, help_text='Whether quarterly TDS return filing is required')),
                ('challan_required', models.BooleanField(default=True, help_text='Whether tax payment through challan is required')),
                ('payment_due_date', models.IntegerField(default=7, help_text='Payment due date (days from month end)')),
                ('return_due_date', models.IntegerField(default=31, help_text='Return filing due date (days from quarter end)')),
                ('withholding_account', models.CharField(blank=True, help_text='Account code for withholding tax liability', max_length=100)),
                ('is_default', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('effective_from', models.DateField(help_text='Effective from date')),
                ('effective_to', models.DateField(blank=True, help_text='Effective to date (null for ongoing)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withholding_tax_configurations', to='setup.companysetup')),
            ],
            options={
                'ordering': ['tax_type', 'name'],
                'unique_together': {('company', 'code')},
            },
        ),
        migrations.DeleteModel(
            name='IncomeTaxConfiguration',
        ),
    ]
