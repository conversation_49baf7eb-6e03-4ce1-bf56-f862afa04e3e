<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2023 <PERSON> <<EMAIL>>
     Copyright 2023 <PERSON> Fernandez <<EMAIL>>
     License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl). -->
<odoo>
    <record id="aged_partner_report_configuration_form" model="ir.ui.view">
        <field name="name">Age partner report configuration form</field>
        <field name="model">account.age.report.configuration</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name" />
                        <field name="company_id" />
                    </group>
                    <field name="line_ids">
                        <list editable="bottom">
                            <field name="name" />
                            <field name="inferior_limit" />
                        </list>
                    </field>
                </sheet>
            </form>
        </field>
    </record>
    <record id="aged_partner_report_configuration_tree" model="ir.ui.view">
        <field name="name">Age partner report configuration list</field>
        <field name="model">account.age.report.configuration</field>
        <field name="arch" type="xml">
            <list>
                <field name="name" />
                <field name="company_id" />
            </list>
        </field>
    </record>
    <record id="action_aged_partner_report_configuration" model="ir.actions.act_window">
        <field name="name">Age Partner Report Configuration</field>
        <field name="res_model">account.age.report.configuration</field>
        <field name="view_mode">list,form</field>
    </record>
</odoo>
