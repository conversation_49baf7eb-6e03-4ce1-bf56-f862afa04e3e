/* Main Layout Styles - Professional ERP Interface */

.main-layout {
  min-height: 100vh;
  background: #f0f2f5;
}

/* Sidebar Styles */
.layout-sider {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  background: #fff !important;
  border-right: 1px solid #f0f0f0;
}

.logo-container {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  margin-bottom: 8px;
}

.logo h4 {
  color: #fff !important;
  margin: 0;
  font-weight: 600;
  text-align: center;
}

/* Menu Styles */
.main-menu {
  border-right: none;
  background: transparent;
  width: 100%;
}

.main-menu .ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.main-menu .ant-menu-item:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.main-menu .ant-menu-item-selected {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: #fff;
  font-weight: 500;
}

.main-menu .ant-menu-item-selected::after {
  display: none;
}

.main-menu .ant-menu-submenu {
  position: relative;
}

.main-menu .ant-menu-submenu-title {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.main-menu .ant-menu-submenu-title:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.main-menu .ant-menu-submenu-open > .ant-menu-submenu-title {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.main-menu .ant-menu-sub {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #f0f0f0;
  min-width: 280px;
  width: auto;
  overflow: visible;
  position: absolute !important;
  left: 100% !important;
  top: 0 !important;
  margin-left: 8px !important;
  z-index: 1050;
}

.main-menu .ant-menu-sub .ant-menu-item {
  margin: 4px 8px;
  padding: 8px 16px !important;
  height: 40px;
  line-height: 24px;
  white-space: nowrap;
  overflow: visible;
  text-overflow: ellipsis;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.main-menu .ant-menu-sub .ant-menu-item:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  transform: translateX(2px);
}

/* Header Styles */
.layout-header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-center {
  flex: 1;
  max-width: 600px;
  margin: 0 24px;
}

.header-right {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 16px;
  cursor: pointer;
  transition: color 0.3s;
  border-radius: 6px;
}

.trigger:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

/* User Info Styles */
.user-info {
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: rgba(24, 144, 255, 0.1);
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/* Content Styles */
.layout-content {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 200px);
}

/* Footer Styles */
.layout-footer {
  background: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 12px 24px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .layout-header {
    padding: 0 16px;
  }
  
  .header-center {
    margin: 0 16px;
    max-width: none;
  }
  
  .layout-content {
    margin: 16px;
    padding: 16px;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .user-details {
    display: none;
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 12px;
  }
  
  .header-center {
    margin: 0 12px;
  }
  
  .layout-content {
    margin: 12px;
    padding: 12px;
  }
  
  .header-right .ant-space {
    gap: 8px !important;
  }
}

/* Dark Mode Styles */
.dark-mode .main-layout {
  background: #141414;
}

.dark-mode .layout-sider {
  background: #001529 !important;
  border-right-color: #303030;
}

.dark-mode .layout-header {
  background: #001529;
  border-bottom-color: #303030;
}

.dark-mode .layout-content {
  background: #1f1f1f;
  color: #fff;
}

.dark-mode .layout-footer {
  background: #001529;
  border-top-color: #303030;
  color: #fff;
}

/* Animation Styles */
.main-menu .ant-menu-item,
.main-menu .ant-menu-submenu-title {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.layout-sider {
  transition: all 0.2s ease;
}

/* Custom Scrollbar */
.layout-sider .ant-layout-sider-children {
  overflow-y: auto;
  overflow-x: hidden;
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar {
  width: 6px;
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.layout-sider .ant-layout-sider-children::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Badge Styles for Notifications */
.notification-badge .ant-badge-count {
  background: #ff4d4f;
  box-shadow: 0 0 0 1px #fff;
}

/* Two-Panel Sidebar Styles */
.two-panel-sidebar {
  display: flex;
  height: 100vh;
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

.main-menu-panel {
  width: 200px;
  background: #fafafa;
  border-right: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
}

.main-menu-panel .logo-container {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.main-menu-items {
  flex: 1;
  padding: 8px 0;
}

.main-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0;
  margin: 2px 8px;
  border-radius: 6px;
}

.main-menu-item:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.main-menu-item.active {
  background: #1890ff;
  color: #fff;
}

.main-menu-item .menu-icon {
  margin-right: 12px;
  font-size: 16px;
}

.main-menu-item .menu-label {
  flex: 1;
  font-weight: 500;
}

.main-menu-item .menu-arrow {
  font-size: 14px;
  opacity: 0.6;
}

.submenu-panel {
  width: 280px;
  background: #fff;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.submenu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.submenu-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #1890ff;
}

.submenu-title span {
  margin-left: 8px;
}

.submenu-items {
  flex: 1;
  padding: 8px 0;
}

.submenu-item {
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0;
  margin: 2px 12px;
  border-radius: 6px;
}

.submenu-item:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

/* Company Selector Styles */
.company-selector {
  min-width: 300px;
}

.company-selector .ant-select-selector {
  border-radius: 6px;
  border-color: #d9d9d9;
}

.company-selector .ant-select-selector:hover {
  border-color: #1890ff;
}

/* Global Search Styles */
.global-search {
  width: 100%;
  max-width: 400px;
}

.global-search .ant-input {
  border-radius: 20px;
  background: #f5f5f5;
  border: none;
  padding: 8px 16px;
}

.global-search .ant-input:focus {
  background: #fff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Loading Styles */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Error Styles */
.error-container {
  text-align: center;
  padding: 40px;
}

.error-container .ant-result-title {
  color: #ff4d4f;
}

/* Success Styles */
.success-container {
  text-align: center;
  padding: 40px;
}

.success-container .ant-result-title {
  color: #52c41a;
}

/* Professional Card Styles */
.professional-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.professional-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Status Indicators */
.status-active {
  color: #52c41a;
  font-weight: 500;
}

.status-inactive {
  color: #ff4d4f;
  font-weight: 500;
}

.status-pending {
  color: #faad14;
  font-weight: 500;
}

.status-draft {
  color: #8c8c8c;
  font-weight: 500;
}

/* Priority Indicators */
.priority-high {
  color: #ff4d4f;
  font-weight: 600;
}

.priority-medium {
  color: #faad14;
  font-weight: 500;
}

.priority-low {
  color: #52c41a;
  font-weight: 400;
}

/* Module Icons */
.module-icon {
  font-size: 18px;
  margin-right: 8px;
}

/* Breadcrumb Styles */
.page-breadcrumb {
  margin-bottom: 24px;
}

.page-breadcrumb .ant-breadcrumb-link {
  color: #8c8c8c;
}

.page-breadcrumb .ant-breadcrumb-link:hover {
  color: #1890ff;
}

/* Page Header Styles */
.page-header {
  background: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 24px;
  border-radius: 8px;
}

.page-header .ant-page-header-heading-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.page-header .ant-page-header-heading-sub-title {
  color: #8c8c8c;
}

/* Action Button Styles */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.action-buttons .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.action-buttons .ant-btn-primary:hover {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

/* Table Styles */
.professional-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
}

.professional-table .ant-table-tbody > tr:hover > td {
  background: rgba(24, 144, 255, 0.05);
}

.professional-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f5f5f5;
}

/* Form Styles */
.professional-form .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.professional-form .ant-input,
.professional-form .ant-select-selector {
  border-radius: 6px;
  border-color: #d9d9d9;
}

.professional-form .ant-input:focus,
.professional-form .ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Modal Styles */
.professional-modal .ant-modal-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-bottom: none;
  border-radius: 8px 8px 0 0;
}

.professional-modal .ant-modal-title {
  color: #fff;
  font-weight: 600;
}

.professional-modal .ant-modal-close {
  color: #fff;
}

.professional-modal .ant-modal-close:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}

/* Drawer Styles */
.professional-drawer .ant-drawer-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-bottom: none;
}

.professional-drawer .ant-drawer-title {
  color: #fff;
  font-weight: 600;
}

.professional-drawer .ant-drawer-close {
  color: #fff;
}

.professional-drawer .ant-drawer-close:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}
