/**
 * Taxes Configuration Component
 */
import React from 'react';
import { Card, Typography, Alert } from 'antd';
import { DollarOutlined } from '@ant-design/icons';

const { Title } = Typography;

const TaxesSetup = () => {
  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <DollarOutlined style={{ marginRight: 8 }} />
        Taxes Configuration
      </Title>
      
      <Card>
        <Alert
          message="Coming Soon"
          description="Taxes Configuration will be available soon."
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
};

export default TaxesSetup;
