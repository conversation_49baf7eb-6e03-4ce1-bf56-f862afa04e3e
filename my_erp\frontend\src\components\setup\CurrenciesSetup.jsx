/**
 * Currencies Setup Component
 */
import React from 'react';
import { Card, Typography, Alert } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';

const { Title } = Typography;

const CurrenciesSetup = () => {
  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <GlobalOutlined style={{ marginRight: 8 }} />
        Currencies Setup
      </Title>
      
      <Card>
        <Alert
          message="Coming Soon"
          description="Currencies Setup will be available soon."
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
};

export default CurrenciesSetup;
