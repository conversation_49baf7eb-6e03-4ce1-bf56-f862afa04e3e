import React, { useState } from 'react';
import { Layout as AntLay<PERSON>, Menu, Button, Typography, Avatar } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  AccountBookOutlined,
  FileTextOutlined,
  UserOutlined,
  ShopOutlined,
  CreditCardOutlined,
  ReceiptOutlined,
  Bar<PERSON>hartOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BankOutlined,
  SettingOutlined,
  CalendarOutlined,
  DollarOutlined,
  GlobalOutlined,
  BookOutlined,
  TagsOutlined,
  HomeOutlined,
  TeamOutlined,
  ProjectOutlined,
  ToolOutlined,
} from '@ant-design/icons';

const { Header, Sider, Content } = AntLayout;
const { Title } = Typography;

const Layout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
    },
    {
      key: '/chart-of-accounts',
      icon: <AccountBookOutlined />,
      label: 'Chart of Accounts',
    },
    {
      key: '/journal-entries',
      icon: <FileTextOutlined />,
      label: 'Journal Entries',
    },
    {
      key: 'partners',
      icon: <UserOutlined />,
      label: 'Partners',
      children: [
        {
          key: '/customers',
          label: 'Customers',
        },
        {
          key: '/vendors',
          label: 'Vendors',
        },
      ],
    },
    {
      key: 'transactions',
      icon: <BankOutlined />,
      label: 'Transactions',
      children: [
        {
          key: '/invoices',
          label: 'Customer Invoices',
        },
        {
          key: '/bills',
          label: 'Vendor Bills',
        },
        {
          key: '/payments',
          label: 'Payments',
        },
      ],
    },
    {
      key: '/reports',
      icon: <BarChartOutlined />,
      label: 'Reports',
    },
    {
      key: 'setup',
      icon: <SettingOutlined />,
      label: 'Setup',
      children: [
        {
          key: '/setup/company',
          icon: <ShopOutlined />,
          label: 'Company Setup',
        },
        {
          key: '/setup/chart-of-accounts',
          icon: <AccountBookOutlined />,
          label: 'Chart of Accounts',
        },
        {
          key: '/setup/fiscal-year',
          icon: <CalendarOutlined />,
          label: 'Fiscal Year',
        },
        {
          key: '/setup/taxes',
          icon: <DollarOutlined />,
          label: 'Taxes Configuration',
        },
        {
          key: '/setup/currencies',
          icon: <GlobalOutlined />,
          label: 'Currencies',
        },
        {
          key: '/setup/payment-terms',
          icon: <CreditCardOutlined />,
          label: 'Payment Terms',
        },
        {
          key: '/setup/journals',
          icon: <BookOutlined />,
          label: 'Journals',
        },
        {
          key: '/setup/bank-accounts',
          icon: <BankOutlined />,
          label: 'Bank Accounts',
        },
        {
          key: '/setup/product-categories',
          icon: <TagsOutlined />,
          label: 'Product Categories',
        },
        {
          key: '/setup/warehouses',
          icon: <HomeOutlined />,
          label: 'Warehouses',
        },
        {
          key: '/setup/departments',
          icon: <TeamOutlined />,
          label: 'Departments',
        },
        {
          key: '/setup/positions',
          icon: <UserOutlined />,
          label: 'Employee Positions',
        },
        {
          key: '/setup/project-stages',
          icon: <ProjectOutlined />,
          label: 'Project Stages',
        },
        {
          key: '/setup/manufacturing',
          icon: <ToolOutlined />,
          label: 'Manufacturing Setup',
        },
      ],
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  return (
    <AntLayout className="main-layout">
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Title level={4} style={{ color: 'white', margin: 0 }}>
            {collapsed ? 'ERP' : 'My ERP System'}
          </Title>
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ borderRight: 0 }}
        />
      </Sider>
      
      <AntLayout style={{ marginLeft: collapsed ? 80 : 200, transition: 'margin-left 0.2s' }}>
        <Header style={{ 
          padding: '0 24px', 
          background: '#fff', 
          display: 'flex', 
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 1px 4px rgba(0,21,41,.08)'
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{ fontSize: '16px', width: 64, height: 64 }}
          />
          
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <span>Welcome, Admin</span>
            <Avatar icon={<UserOutlined />} />
          </div>
        </Header>
        
        <Content className="content-area">
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
