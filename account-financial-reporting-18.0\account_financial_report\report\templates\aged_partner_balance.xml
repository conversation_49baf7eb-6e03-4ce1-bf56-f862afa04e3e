<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <template id="aged_partner_balance">
        <t t-call="account_financial_report.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-call="account_financial_report.internal_layout">
                    <t
                        t-call="account_financial_report.report_aged_partner_balance_base"
                    />
                </t>
            </t>
        </t>
    </template>
    <template id="report_aged_partner_balance_base">
        <!-- Saved flag fields into variables, used to define columns display -->
        <t t-set="show_move_line_details" t-value="show_move_line_details" />
        <!-- Defines global variables used by internal layout -->
        <t t-set="title">
            Aged Partner Balance -
            <t t-out="company_name" />
            -
            <t t-out="currency_name" />
        </t>
        <div class="page">
            <div class="row">
                <h4
                    class="mt0"
                    t-esc="title or 'Odoo Report'"
                    style="text-align: center;"
                />
            </div>
            <!-- Display filters -->
            <t t-call="account_financial_report.report_aged_partner_balance_filters" />
            <t t-foreach="aged_partner_balance" t-as="account">
                <div class="page_break">
                    <!-- Display account header -->
                    <div class="act_as_table list_table" style="margin-top: 10px;" />
                    <div class="act_as_caption account_title" style="width: 100%;">
                        <span t-esc="account['code']" />
                        -
                        <span t-esc="account['name']" />
                    </div>
                    <!-- Display account lines -->
                    <t t-if="not show_move_line_details">
                        <div class="act_as_table data_table" style="width: 100%;">
                            <!-- Display account header -->
                            <t
                                t-call="account_financial_report.report_aged_partner_balance_lines_header"
                            />
                            <t t-foreach="account['partners']" t-as="partner">
                                <!-- Display one line per partner -->
                                <t
                                    t-call="account_financial_report.report_aged_partner_balance_lines"
                                />
                            </t>
                        </div>
                        <!-- Display account footer -->
                        <t
                            t-call="account_financial_report.report_aged_partner_balance_account_ending_cumul"
                        />
                    </t>
                    <!-- Display account move lines -->
                    <t t-if="show_move_line_details">
                        <!-- Display account partners -->
                        <t t-foreach="account['partners']" t-as="partner">
                            <div class="page_break">
                                <!-- Display partner header -->
                                <div class="act_as_caption account_title">
                                    <span t-esc="partner['name']" />
                                </div>
                                <!-- Display partner move lines -->
                                <t
                                    t-call="account_financial_report.report_aged_partner_balance_move_lines"
                                />
                                <!-- Display partner footer -->
                                <t
                                    t-call="account_financial_report.report_aged_partner_balance_partner_ending_cumul"
                                >
                                    <t t-set="partner_cumul_line" t-value="partner" />
                                </t>
                            </div>
                        </t>
                        <!-- Display account footer -->
                        <t
                            t-call="account_financial_report.report_aged_partner_balance_account_ending_cumul"
                        />
                    </t>
                </div>
            </t>
        </div>
    </template>
    <template id="report_aged_partner_balance_filters">
        <div class="act_as_table data_table" style="width: 100%;">
            <div class="act_as_row labels">
                <div class="act_as_cell">Date at filter</div>
                <div class="act_as_cell">Target moves filter</div>
            </div>
            <div class="act_as_row">
                <div class="act_as_cell">
                    <span t-esc="date_at" />
                </div>
                <div class="act_as_cell">
                    <t t-if="only_posted_moves">All posted entries</t>
                    <t t-if="not only_posted_moves">All entries</t>
                </div>
            </div>
        </div>
    </template>
    <template id="report_aged_partner_balance_lines_header">
        <!-- Display table headers for lines -->
        <div class="act_as_thead">
            <div class="act_as_row labels">
                <!--## partner-->
                <div class="act_as_cell" style="width: 32.52%;">Partner</div>
                <!--## amount_residual-->
                <div class="act_as_cell" style="width: 9.64%;">Residual</div>
                <!--## current-->
                <div class="act_as_cell" style="width: 9.64%;">Not due</div>
                <t t-if="not age_partner_config">
                    <!--## age_30_days-->
                    <div class="act_as_cell" style="width: 9.64%;">1 - 30 d.</div>
                    <!--## age_60_days-->
                    <div class="act_as_cell" style="width: 9.64%;">31 - 60 d.</div>
                    <!--## age_90_days-->
                    <div class="act_as_cell" style="width: 9.64%;">61 - 90 d.</div>
                    <!--## age_120_days-->
                    <div class="act_as_cell" style="width: 9.64%;">91 - 120 d.</div>
                    <!--## older-->
                    <div class="act_as_cell" style="width: 9.64%;">> 120 d.</div>
                </t>
                <!--## dynamic columns-->
                <t t-foreach="age_partner_config.line_ids" t-as="column_dynamic">
                    <div class="act_as_cell">
                        <span t-out="column_dynamic.name" />
                    </div>
                </t>
            </div>
        </div>
    </template>
    <template id="report_aged_partner_balance_lines">
        <!-- Display each partner lines -->
        <div class="act_as_row lines">
            <!--## partner-->
            <div class="act_as_cell left">
                <span t-esc="partner['name']" />
            </div>
            <!--## amount_residual-->
            <div class="act_as_cell amount">
                <span
                    t-esc="partner['residual']"
                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                />
            </div>
            <!--## current-->
            <div class="act_as_cell amount">
                <span
                    t-esc="partner['current']"
                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                />
            </div>
            <t t-if="not age_partner_config">
                <!--## age_30_days-->
                <div class="act_as_cell amount">
                    <span
                        t-esc="partner['30_days']"
                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                    />
                </div>
                <!--## age_60_days-->
                <div class="act_as_cell amount">
                    <span
                        t-esc="partner['60_days']"
                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                    />
                </div>
                <!--## age_90_days-->
                <div class="act_as_cell amount">
                    <span
                        t-esc="partner['90_days']"
                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                    />
                </div>
                <!--## age_120_days-->
                <div class="act_as_cell amount">
                    <span
                        t-esc="partner['120_days']"
                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                    />
                </div>
                <!--## older-->
                <div class="act_as_cell amount">
                    <span
                        t-esc="partner['older']"
                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                    />
                </div>
            </t>
            <t t-foreach="age_partner_config.line_ids" t-as="column_dynamic">
                <div class="act_as_cell amount">
                    <span
                        t-out="partner[column_dynamic]"
                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                    />
                </div>
            </t>
        </div>
    </template>
    <template id="report_aged_partner_balance_move_lines">
        <div class="act_as_table data_table" style="width: 100%;">
            <!-- Display table headers for move lines -->
            <div class="act_as_thead">
                <div class="act_as_row labels">
                    <!--## date-->
                    <div class="act_as_cell first_column" style="width: 6.00%;">
                        Date
                    </div>
                    <!--## move-->
                    <div class="act_as_cell" style="width: 7.00%;">Entry</div>
                    <!--## journal-->
                    <div class="act_as_cell" style="width: 5.00%;">Journal</div>
                    <!--## account code-->
                    <div class="act_as_cell" style="width: 6.00%;">Account</div>
                    <!--## partner-->
                    <div class="act_as_cell" style="width: 10.50%;">Partner</div>
                    <!--## ref - label-->
                    <div class="act_as_cell" style="width: 18.00%;">
                        Ref -
                        Label
                    </div>
                    <!--## date_due-->
                    <div class="act_as_cell" style="width: 6.00%;">
                        Due
                        date
                    </div>
                    <!--## amount_residual-->
                    <div class="act_as_cell" style="width: 6.00%;">Residual</div>
                    <!--## current-->
                    <div class="act_as_cell" style="width: 6.00%;">Current</div>
                    <t t-if="not age_partner_config">
                        <!--## age_30_days-->
                        <div class="act_as_cell" style="width: 6.00%;">
                            Age ≤ 30
                            d.
                        </div>
                        <!--## age_60_days-->
                        <div class="act_as_cell" style="width: 6.00%;">
                            Age ≤ 60
                            d.
                        </div>
                        <!--## age_90_days-->
                        <div class="act_as_cell" style="width: 6.00%;">
                            Age ≤ 90
                            d.
                        </div>
                        <!--## age_120_days-->
                        <div class="act_as_cell" style="width: 6.00%;">
                            Age ≤ 120
                            d.
                        </div>
                        <!--## older-->
                        <div class="act_as_cell" style="width: 6.00%;">Older</div>
                    </t>
                    <!--## current-->
                    <t t-foreach="age_partner_config.line_ids" t-as="column_dynamic">
                        <div class="act_as_cell">
                            <span t-esc="column_dynamic.name" />
                        </div>
                    </t>
                </div>
            </div>
            <!-- Display each move lines -->
            <t t-foreach="partner['move_lines']" t-as="line">
                <!-- # lines or centralized lines -->
                <div class="act_as_row lines">
                    <!--## date-->
                    <div class="act_as_cell left">
                        <span
                            t-att-res-id="line['line_rec'].id"
                            res-model="account.move.line"
                            view-type="form"
                        >
                            <!--## We don't use t-field because it throws an error on click -->
                            <t t-esc="line['date']" t-options="{'widget': 'date'}" />
                        </span>
                    </div>
                    <!--## move-->
                    <div class="act_as_cell left">
                        <span
                            t-att-res-id="line['line_rec'].move_id.id"
                            res-model="account.move"
                            view-type="form"
                        >
                            <t t-out="line['entry']" />
                        </span>
                    </div>
                    <!--## journal-->
                    <div class="act_as_cell left">
                        <span
                            t-att-res-id="line['line_rec'].move_id.journal_id.id"
                            res-model="account.journal"
                            view-type="form"
                        >
                            <t t-out="line['journal']" />
                        </span>
                    </div>
                    <!--## account code-->
                    <div class="act_as_cell left">
                        <span
                            t-att-res-id="line['line_rec'].account_id.id"
                            res-model="account.account"
                            view-type="form"
                        >
                            <t t-out="line['account']" />
                        </span>
                    </div>
                    <!--## partner-->
                    <div class="act_as_cell left">
                        <span
                            t-att-res-id="line['line_rec'].partner_id.id"
                            res-model="res.partner"
                            view-type="form"
                        >
                            <t t-out="line['partner']" />
                        </span>
                    </div>
                    <!--## ref - label-->
                    <div class="act_as_cell left">
                        <span
                            t-att-res-id="line['line_rec'].id"
                            res-model="account.move.line"
                            view-type="form"
                        >
                            <t t-out="line['ref_label']" />
                        </span>
                    </div>
                    <!--## date_due-->
                    <div class="act_as_cell left">
                        <span
                            t-att-res-id="line['line_rec'].id"
                            res-model="account.move.line"
                            view-type="form"
                        >
                            <!--## We don't use t-field because it throws an error on click -->
                            <t
                                t-esc="line['due_date']"
                                t-options="{'widget': 'date'}"
                            />
                        </span>
                    </div>
                    <!--## amount_residual-->
                    <div class="act_as_cell amount">
                        <span
                            domain="[('id', 'in', (line['line_rec'] | line['line_rec'].matched_debit_ids.mapped('debit_move_id') | line['line_rec'].matched_credit_ids.mapped('credit_move_id')).ids)]"
                            res-model="account.move.line"
                        >
                            <t
                                t-out="line['residual']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </span>
                    </div>
                    <!--## current-->
                    <div class="act_as_cell amount">
                        <t t-if="line['current'] == 0">
                            <span
                                t-esc="line['current']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </t>
                        <t t-else="">
                            <span
                                domain="[('id', 'in', (line['line_rec'] | line['line_rec'].matched_debit_ids.mapped('debit_move_id') | line['line_rec'].matched_credit_ids.mapped('credit_move_id')).ids)]"
                                res-model="account.move.line"
                            >
                                <t
                                    t-out="line['current']"
                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                />
                            </span>
                        </t>
                    </div>
                    <t t-if="not age_partner_config">
                        <!--## age_30_days-->
                        <div class="act_as_cell amount">
                            <t t-if="line['30_days'] == 0">
                                <span
                                    t-esc="line['30_days']"
                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                />
                            </t>
                            <t t-else="">
                                <span
                                    domain="[('id', 'in', (line['line_rec'] | line['line_rec'].matched_debit_ids.mapped('debit_move_id') | line['line_rec'].matched_credit_ids.mapped('credit_move_id')).ids)]"
                                    res-model="account.move.line"
                                >
                                    <t
                                        t-out="line['30_days']"
                                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                    />
                                </span>
                            </t>
                        </div>
                        <!--## age_60_days-->
                        <div class="act_as_cell amount">
                            <t t-if="line['60_days'] == 0">
                                <span
                                    t-esc="line['60_days']"
                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                />
                            </t>
                            <t t-else="">
                                <span
                                    domain="[('id', 'in', (line['line_rec'] | line['line_rec'].matched_debit_ids.mapped('debit_move_id') | line['line_rec'].matched_credit_ids.mapped('credit_move_id')).ids)]"
                                    res-model="account.move.line"
                                >
                                    <t
                                        t-out="line['60_days']"
                                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                    />
                                </span>
                            </t>
                        </div>
                        <!--## age_90_days-->
                        <div class="act_as_cell amount">
                            <t t-if="line['90_days'] == 0">
                                <span
                                    t-esc="line['90_days']"
                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                />
                            </t>
                            <t t-else="">
                                <span
                                    domain="[('id', 'in', (line['line_rec'] | line['line_rec'].matched_debit_ids.mapped('debit_move_id') | line['line_rec'].matched_credit_ids.mapped('credit_move_id')).ids)]"
                                    res-model="account.move.line"
                                >
                                    <t
                                        t-out="line['90_days']"
                                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                    />
                                </span>
                            </t>
                        </div>
                        <!--## age_120_days-->
                        <div class="act_as_cell amount">
                            <t t-if="line['120_days'] == 0">
                                <span
                                    t-esc="line['120_days']"
                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                />
                            </t>
                            <t t-else="">
                                <span
                                    domain="[('id', 'in', (line['line_rec'] | line['line_rec'].matched_debit_ids.mapped('debit_move_id') | line['line_rec'].matched_credit_ids.mapped('credit_move_id')).ids)]"
                                    res-model="account.move.line"
                                >
                                    <t
                                        t-out="line['120_days']"
                                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                    />
                                </span>
                            </t>
                        </div>
                        <!--## older-->
                        <div class="act_as_cell amount">
                            <t t-if="line['older'] == 0">
                                <span
                                    t-esc="line['older']"
                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                />
                            </t>
                            <t t-else="">
                                <span
                                    domain="[('id', 'in', (line['line_rec'] | line['line_rec'].matched_debit_ids.mapped('debit_move_id') | line['line_rec'].matched_credit_ids.mapped('credit_move_id')).ids)]"
                                    res-model="account.move.line"
                                >
                                    <t
                                        t-out="line['older']"
                                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                    />
                                </span>
                            </t>
                        </div>
                    </t>
                    <t t-foreach="age_partner_config.line_ids" t-as="column_dynamic">
                        <div class="act_as_cell amount">
                            <t t-if="line[column_dynamic] == 0">
                                <span
                                    t-esc="line[column_dynamic]"
                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                />
                            </t>
                            <t t-else="">
                                <span
                                    domain="[('id', 'in', (line['line_rec'] | line['line_rec'].matched_debit_ids.mapped('debit_move_id') | line['line_rec'].matched_credit_ids.mapped('credit_move_id')).ids)]"
                                    res-model="account.move.line"
                                >
                                    <t
                                        t-out="line[column_dynamic]"
                                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                                    />
                                </span>
                            </t>
                        </div>
                    </t>
                </div>
            </t>
        </div>
    </template>
    <template id="report_aged_partner_balance_partner_ending_cumul">
        <!-- Display ending balance line for partner -->
        <div class="act_as_table list_table" style="width: 100%;">
            <div class="act_as_row lines" style="font-weight: bold;">
                <!--## date-->
                <div class="act_as_cell right" style="width: 52.00%;">
                    Partner
                    cumul aged balance
                </div>
                <!--## date_due-->
                <div class="act_as_cell" style="width: 6.00%;" />
                <!--## amount_residual-->
                <div class="act_as_cell amount" style="width: 6.00%;">
                    <span
                        t-esc="partner_cumul_line['residual']"
                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                    />
                </div>
                <!--## current-->
                <div class="act_as_cell amount" style="width: 6.00%;">
                    <span
                        t-esc="partner_cumul_line['current']"
                        t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                    />
                </div>
                <t t-if="not age_partner_config">
                    <!--## age_30_days-->
                    <div class="act_as_cell amount" style="width: 6.00%;">
                        <span
                            t-esc="partner_cumul_line['30_days']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </div>
                    <!--## age_60_days-->
                    <div class="act_as_cell amount" style="width: 6.00%;">
                        <span
                            t-esc="partner_cumul_line['60_days']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </div>
                    <!--## age_90_days-->
                    <div class="act_as_cell amount" style="width: 6.00%;">
                        <span
                            t-esc="partner_cumul_line['90_days']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </div>
                    <!--## age_120_days-->
                    <div class="act_as_cell amount" style="width: 6.00%;">
                        <span
                            t-esc="partner_cumul_line['120_days']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </div>
                    <!--## older-->
                    <div class="act_as_cell amount" style="width: 6.00%;">
                        <span
                            t-esc="partner_cumul_line['older']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </div>
                </t>
                <t t-foreach="age_partner_config.line_ids" t-as="column_dynamic">
                    <div class="act_as_cell amount">
                        <span
                            t-esc="partner_cumul_line[column_dynamic]"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </div>
                </t>
            </div>
        </div>
    </template>
    <template id="report_aged_partner_balance_account_ending_cumul">
        <!-- Display ending balance line for account -->
        <div class="act_as_table list_table" style="width: 100%;">
            <div class="act_as_row lines" style="font-weight: bold;">
                <t t-if="not show_move_line_details">
                    <!--## total-->
                    <div class="act_as_cell right" style="width: 32.52%;">Total</div>
                    <!--## amount_residual-->
                    <div class="act_as_cell amount" style="width: 9.64%;">
                        <span
                            t-esc="account['residual']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </div>
                    <!--## current-->
                    <div class="act_as_cell amount" style="width: 9.64%;">
                        <span
                            t-esc="account['current']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </div>
                    <t t-if="not age_partner_config">
                        <!--## age_30_days-->
                        <div class="act_as_cell amount" style="width: 9.64%;">
                            <span
                                t-esc="account['30_days']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                        <!--## age_60_days-->
                        <div class="act_as_cell amount" style="width: 9.64%;">
                            <span
                                t-esc="account['60_days']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                        <!--## age_90_days-->
                        <div class="act_as_cell amount" style="width: 9.64%;">
                            <span
                                t-esc="account['90_days']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                        <!--## age_120_days-->
                        <div class="act_as_cell amount" style="width: 9.64%;">
                            <span
                                t-esc="account['120_days']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                        <!--## older-->
                        <div class="act_as_cell amount" style="width: 9.64%;">
                            <span
                                t-esc="account['older']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                    </t>
                    <t t-foreach="age_partner_config.line_ids" t-as="column_dynamic">
                        <div class="act_as_cell amount">
                            <span
                                t-esc="account[column_dynamic]"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                    </t>
                </t>
                <t t-if="show_move_line_details">
                    <!--## total-->
                    <div class="act_as_cell right" style="width: 52.00%;">Total</div>
                    <!--## date_due-->
                    <div class="act_as_cell" style="width: 6.00%;" />
                    <!--## amount_residual-->
                    <div class="act_as_cell amount" style="width: 6.00%">
                        <span
                            t-esc="account['residual']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </div>
                    <!--## current-->
                    <div class="act_as_cell amount" style="width: 6.00%">
                        <span
                            t-esc="account['current']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </div>
                    <t t-if="not age_partner_config">
                        <!--## age_30_days-->
                        <div class="act_as_cell amount" style="width: 6.00%">
                            <span
                                t-esc="account['30_days']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                        <!--## age_60_days-->
                        <div class="act_as_cell amount" style="width: 6.00%">
                            <span
                                t-esc="account['60_days']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                        <!--## age_90_days-->
                        <div class="act_as_cell amount" style="width: 6.00%">
                            <span
                                t-esc="account['90_days']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                        <!--## age_120_days-->
                        <div class="act_as_cell amount" style="width: 6.00%">
                            <span
                                t-esc="account['120_days']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                        <!--## older-->
                        <div class="act_as_cell amount" style="width: 6.00%">
                            <span
                                t-esc="account['older']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                    </t>
                    <t t-foreach="age_partner_config.line_ids" t-as="column_dynamic">
                        <div class="act_as_cell amount">
                            <span
                                t-esc="account[column_dynamic]"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </div>
                    </t>
                </t>
            </div>
            <div class="act_as_row" style="font-weight: bold; font-style: italic;">
                <t t-if="not show_move_line_details">
                    <!--## total-->
                    <div class="act_as_cell right" style="width: 32.52%;">Percents</div>
                    <!--## amount_residual-->
                    <div class="act_as_cell amount" style="width: 9.64%;" />
                    <!--## current-->
                    <div class="act_as_cell amount" style="width: 9.64%;">
                        <span t-esc="account['percent_current']" />
                            %
                        </div>
                    <t t-if="not age_partner_config">
                        <!--## age_30_days-->
                        <div class="act_as_cell amount" style="width: 9.64%;">
                            <span t-esc="account['percent_30_days']" />
                            %
                        </div>
                        <!--## age_60_days-->
                        <div class="act_as_cell amount" style="width: 9.64%;">
                            <span t-esc="account['percent_60_days']" />
                            %
                        </div>
                        <!--## age_90_days-->
                        <div class="act_as_cell amount" style="width: 9.64%;">
                            <span t-esc="account['percent_90_days']" />
                            %
                        </div>
                        <!--## age_120_days-->
                        <div class="act_as_cell amount" style="width: 9.64%;">
                            <span t-esc="account['percent_120_days']" />
                            %
                        </div>
                        <!--## older-->
                        <div class="act_as_cell amount" style="width: 9.64%;">
                            <span t-esc="account['percent_older']" />
                            %
                        </div>
                    </t>
                    <!--## current-->
                    <t t-foreach="age_partner_config.line_ids" t-as="column_dynamic">
                        <div class="act_as_cell amount">
                            <span t-esc="account['percent_'+str(column_dynamic.id)]" />
                            %
                        </div>
                    </t>
                </t>
                <t t-if="show_move_line_details">
                    <!--## total-->
                    <div class="act_as_cell right" style="width: 52.00%;">Percents</div>
                    <!--## date_due-->
                    <div class="act_as_cell" style="width: 6.00%;" />
                    <!--## amount_residual-->
                    <div class="act_as_cell amount" style="width: 6.00%" />
                    <!--## current-->
                    <div class="act_as_cell amount" style="width: 6.00%">
                        <span t-esc="account['percent_current']" />
                            %
                        </div>
                    <t t-if="not age_partner_config">
                        <!--## age_30_days-->
                        <div class="act_as_cell amount" style="width: 6.00%">
                            <span t-esc="account['percent_30_days']" />
                            %
                        </div>
                        <!--## age_60_days-->
                        <div class="act_as_cell amount" style="width: 6.00%">
                            <span t-esc="account['percent_60_days']" />
                            %
                        </div>
                        <!--## age_90_days-->
                        <div class="act_as_cell amount" style="width: 6.00%">
                            <span t-esc="account['percent_90_days']" />
                            %
                        </div>
                        <!--## age_120_days-->
                        <div class="act_as_cell amount" style="width: 6.00%">
                            <span t-esc="account['percent_120_days']" />
                            %
                        </div>
                        <!--## older-->
                        <div class="act_as_cell amount" style="width: 6.00%">
                            <span t-esc="account['percent_older']" />
                            %
                        </div>
                    </t>
                    <!--## current-->
                    <t t-foreach="age_partner_config.line_ids" t-as="column_dynamic">
                        <div class="act_as_cell amount">
                            <span t-esc="account['percent_'+str(column_dynamic.id)]" />
                            %
                        </div>
                    </t>
                </t>
            </div>
        </div>
    </template>
</odoo>
