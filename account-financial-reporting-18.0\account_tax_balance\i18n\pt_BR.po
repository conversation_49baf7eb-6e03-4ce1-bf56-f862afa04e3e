# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_balance
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2023-10-29 00:25+0000\n"
"Last-Translator: <PERSON><PERSON> <adriano<PERSON><PERSON><PERSON>@gmail.com>\n"
"Language-Team: none\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/wizard/open_tax_balances.py:0
msgid "%(name)s: %(target)s from %(from)s to %(to)s"
msgstr "%(name)s: %(target)s de %(from)s para %(to)s"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_search_balance
msgid "Account Tax"
msgstr "Imposto da Conta"

#. module: account_tax_balance
#: model:ir.model.fields.selection,name:account_tax_balance.selection__wizard_open_tax_balances__target_move__all
msgid "All Entries"
msgstr "Todas as Entradas"

#. module: account_tax_balance
#: model:ir.model.fields.selection,name:account_tax_balance.selection__wizard_open_tax_balances__target_move__posted
msgid "All Posted Entries"
msgstr "Todas as Entradas Lançadas"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__balance_regular
msgid "Balance"
msgstr "Saldo"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__balance_refund
msgid "Balance Refund"
msgstr "Reembolso do Saldo"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__base_balance_regular
msgid "Base Balance"
msgstr "Saldo Base"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__base_balance_refund
msgid "Base Balance Refund"
msgstr "Reembolso de Saldo Base"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "Base Total"
msgstr "Base Total"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.wizard_open_tax_balances
msgid "Cancel"
msgstr "Cancelar"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__company_ids
msgid "Companies"
msgstr "Empresas"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__create_date
msgid "Created on"
msgstr "Criado em"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__date_range_id
msgid "Date Range"
msgstr "Período"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__display_name
msgid "Display Name"
msgstr "Nome Exibição"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_bank_statement_line__financial_type
#: model:ir.model.fields,field_description:account_tax_balance.field_account_move__financial_type
msgid "Financial Type"
msgstr "Tipo Financeiro"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__from_date
msgid "From Date"
msgstr "Data Inicial"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_search_balance
msgid "Group By"
msgstr "Agrupar por"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__has_moves
msgid "Has balance in period"
msgstr "Tem saldo no período"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__id
msgid "ID"
msgstr "ID"

#. module: account_tax_balance
#: model:ir.model,name:account_tax_balance.model_account_move
msgid "Journal Entry"
msgstr "Entrada Diário"

#. module: account_tax_balance
#: model:ir.model,name:account_tax_balance.model_account_move_line
msgid "Journal Item"
msgstr "Item Diário"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Liquidity"
msgstr "Liquidez"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_account_move_filter
msgid "Move type"
msgstr "Tipo Movimento"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.wizard_open_tax_balances
msgid "Open Taxes"
msgstr "Impostos Abertos"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Other"
msgstr "Outro"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Payable"
msgstr "A pagar"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Payable refund"
msgstr "Reembolso a pagar"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Receivable"
msgstr "A receber"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_move.py:0
msgid "Receivable refund"
msgstr "Reembolso A Receber"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_search_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "Short Name"
msgstr "Nome Curto"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__target_move
msgid "Target Moves"
msgstr "Movimentos Alvo"

#. module: account_tax_balance
#: model:ir.model,name:account_tax_balance.model_account_tax
msgid "Tax"
msgstr "Imposto"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_search_balance
msgid "Tax Group"
msgstr "Grupo de Imposto"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_search_balance
msgid "Tax Scope"
msgstr "Escopo Fiscal"

#. module: account_tax_balance
#: model:ir.actions.act_window,name:account_tax_balance.action_open_tax_balances
#: model:ir.actions.act_window,name:account_tax_balance.action_tax_balances_tree
#: model:ir.ui.menu,name:account_tax_balance.menu_action_open_tax_balances
#: model:ir.ui.menu,name:account_tax_balance.menu_tax_balances
#: model_terms:ir.ui.view,arch_db:account_tax_balance.wizard_open_tax_balances
msgid "Taxes Balance"
msgstr "Saldo Impostos"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_wizard_open_tax_balances__to_date
msgid "To Date"
msgstr "Data Final"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "Total"
msgstr "Total"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__balance
msgid "Total Balance"
msgstr "Saldo Total"

#. module: account_tax_balance
#: model:ir.model.fields,field_description:account_tax_balance.field_account_tax__base_balance
msgid "Total Base Balance"
msgstr "Saldo Base Total"

#. module: account_tax_balance
#. odoo-python
#: code:addons/account_tax_balance/models/account_tax.py:0
msgid "Unsupported search operator"
msgstr "Operador de pesquisa não suportado"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View base lines"
msgstr "Ver linhas base"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View base refund lines"
msgstr "Ver linhas bases de reembolso"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View base regular lines"
msgstr "Ver linhas bases regulares"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View tax lines"
msgstr "Ver linhas de imposto"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View tax refund lines"
msgstr "Ver linhas de reembolso de imposto"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.view_tax_tree_balance
msgid "View tax regular lines"
msgstr "Ver linhas de imposto regulares"

#. module: account_tax_balance
#: model:ir.model,name:account_tax_balance.model_wizard_open_tax_balances
msgid "Wizard Open Tax Balances"
msgstr "Assistente de Saldos Fiscais Abertos"

#. module: account_tax_balance
#: model_terms:ir.ui.view,arch_db:account_tax_balance.wizard_open_tax_balances
msgid "or"
msgstr "ou"

#~ msgid "Last Modified on"
#~ msgstr "Última Modificação em"
