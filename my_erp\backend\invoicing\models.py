"""
Invoicing Module Models - Complete Odoo Invoicing
Based on Odoo's Invoicing/Billing Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime, timedelta

# Import models from other modules for integration
from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountTax, AccountPaymentTerm, ResCurrency, ProductProduct
)


class AccountMoveReversal(models.Model):
    """Account Move Reversal - Based on Odoo account.move.reversal"""

    REVERSAL_TYPES = [
        ('refund', 'Partial Refund'),
        ('cancel', 'Full Refund'),
        ('modify', 'Cancel: create refund and new draft invoice'),
    ]

    move_ids = models.ManyToManyField(AccountMove, help_text="Journal Entries")
    new_move_ids = models.ManyToManyField(AccountMove, blank=True, related_name='reversal_new_moves', help_text="Reversal Moves")

    # Reversal Configuration
    refund_method = models.CharField(max_length=16, choices=REVERSAL_TYPES, default='refund')
    reason = models.CharField(max_length=128, blank=True, help_text="Reason")

    # Dates
    date = models.DateField(default=date.today, help_text="Reversal Date")

    # Journal
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, help_text="Use Specific Journal")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_move_reversal'

    def __str__(self):
        return f"Reversal - {self.reason}"

    def reverse_moves(self):
        """Reverse the selected moves"""
        reversed_moves = []
        for move in self.move_ids.all():
            reversed_move = self._reverse_move(move)
            if reversed_move:
                reversed_moves.append(reversed_move)
                self.new_move_ids.add(reversed_move)
        return reversed_moves

    def _reverse_move(self, move):
        """Reverse a single move"""
        # Create reversal move
        reversal_move = AccountMove.objects.create(
            move_type=self._get_reversal_type(move.move_type),
            partner_id=move.partner_id,
            invoice_date=self.date,
            date=self.date,
            journal_id=self.journal_id,
            ref=f"Reversal of {move.name}: {self.reason}",
            company_id=self.company_id,
        )

        # Create reversal lines
        for line in move.line_ids.all():
            AccountMoveLine.objects.create(
                move_id=reversal_move,
                account_id=line.account_id,
                partner_id=line.partner_id,
                name=line.name,
                debit=line.credit,  # Reverse debit/credit
                credit=line.debit,
                tax_ids=line.tax_ids.all(),
                product_id=line.product_id,
                quantity=-line.quantity if line.quantity else 0,
                price_unit=line.price_unit,
            )

        return reversal_move

    def _get_reversal_type(self, original_type):
        """Get the reversal move type"""
        reversal_map = {
            'out_invoice': 'out_refund',
            'in_invoice': 'in_refund',
            'out_refund': 'out_invoice',
            'in_refund': 'in_invoice',
        }
        return reversal_map.get(original_type, 'entry')


class AccountPaymentRegister(models.Model):
    """Payment Register - Based on Odoo account.payment.register"""

    PAYMENT_TYPES = [
        ('outbound', 'Send Money'),
        ('inbound', 'Receive Money'),
    ]

    PARTNER_TYPES = [
        ('customer', 'Customer'),
        ('supplier', 'Vendor'),
    ]

    # Payment Configuration
    payment_type = models.CharField(max_length=16, choices=PAYMENT_TYPES, default='inbound')
    partner_type = models.CharField(max_length=16, choices=PARTNER_TYPES, default='customer')
    source_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Amount to Pay")
    source_amount_currency = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Amount in Currency")

    # Payment Details
    amount = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Payment Amount")
    payment_date = models.DateField(default=date.today, help_text="Payment Date")
    communication = models.CharField(max_length=256, blank=True, help_text="Memo")

    # Journal and Currency
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, help_text="Payment Journal")
    currency_id = models.CharField(max_length=3, default='PKR', help_text="Currency")

    # Group Payment
    group_payment = models.BooleanField(default=False, help_text="Group Payments")

    # Related Moves
    line_ids = models.ManyToManyField(AccountMoveLine, help_text="Journal Items")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_payment_register'

    def __str__(self):
        return f"Payment Register - {self.amount}"

    def action_create_payments(self):
        """Create payments from register"""
        from accounting.models import AccountPayment

        payments = []
        if self.group_payment:
            # Create single grouped payment
            payment = self._create_payment()
            payments.append(payment)
        else:
            # Create individual payments
            for line in self.line_ids.all():
                payment = self._create_payment(line)
                payments.append(payment)

        return payments

    def _create_payment(self, line=None):
        """Create individual payment"""
        from accounting.models import AccountPayment

        payment_amount = line.amount_residual if line else self.amount
        partner = line.partner_id if line else self.line_ids.first().partner_id

        payment = AccountPayment.objects.create(
            payment_type=self.payment_type,
            partner_type=self.partner_type,
            partner_id=partner,
            amount=abs(payment_amount),
            payment_date=self.payment_date,
            communication=self.communication,
            journal_id=self.journal_id,
            company_id=self.company_id,
        )

        # Reconcile with move lines
        if line:
            payment.reconcile_with_moves([line.move_id])
        else:
            moves = [line.move_id for line in self.line_ids.all()]
            payment.reconcile_with_moves(moves)

        return payment


class AccountInvoiceSend(models.Model):
    """Invoice Send - Based on Odoo account.invoice.send"""

    TEMPLATE_TYPES = [
        ('invoice', 'Invoice'),
        ('invoice_reminder', 'Invoice Reminder'),
    ]

    # Email Configuration
    template_id = models.CharField(max_length=32, choices=TEMPLATE_TYPES, default='invoice')
    email_from = models.EmailField(help_text="From")
    email_to = models.TextField(help_text="To")
    email_cc = models.TextField(blank=True, help_text="Cc")
    subject = models.CharField(max_length=256, help_text="Subject")
    body = models.TextField(help_text="Contents")

    # Attachments
    attachment_ids = models.TextField(blank=True, help_text="Attachments")

    # Options
    is_email = models.BooleanField(default=True, help_text="Email")
    is_print = models.BooleanField(default=False, help_text="Print")
    printed = models.BooleanField(default=False, help_text="Is Printed")

    # Invoice
    invoice_ids = models.ManyToManyField(AccountMove, help_text="Invoices")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_invoice_send'

    def __str__(self):
        return f"Send Invoice - {self.subject}"

    def send_and_print_action(self):
        """Send email and/or print invoice"""
        results = []

        if self.is_email:
            email_result = self._send_email()
            results.append(email_result)

        if self.is_print:
            print_result = self._print_invoice()
            results.append(print_result)

        return results

    def _send_email(self):
        """Send invoice email"""
        # Email sending logic would go here
        # In a real implementation, this would integrate with email backend
        for invoice in self.invoice_ids.all():
            # Mark invoice as sent
            invoice.is_move_sent = True
            invoice.save()

        return {'type': 'email', 'status': 'sent', 'count': self.invoice_ids.count()}

    def _print_invoice(self):
        """Print invoice"""
        # Print logic would go here
        # In a real implementation, this would generate PDF
        self.printed = True
        self.save()

        return {'type': 'print', 'status': 'printed', 'count': self.invoice_ids.count()}


class AccountDebitNote(models.Model):
    """Debit Note - Based on Odoo account.debit.note"""

    COPY_LINES_OPTIONS = [
        ('copy_lines', 'Copy lines'),
        ('refund_lines', 'Refund lines'),
    ]

    # Debit Note Configuration
    move_ids = models.ManyToManyField(AccountMove, help_text="Journal Entries")
    copy_lines = models.CharField(max_length=16, choices=COPY_LINES_OPTIONS, default='copy_lines')
    reason = models.CharField(max_length=128, help_text="Reason")

    # Dates
    date = models.DateField(default=date.today, help_text="Debit Note Date")

    # Journal
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, help_text="Journal")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_debit_note'

    def __str__(self):
        return f"Debit Note - {self.reason}"

    def create_debit(self):
        """Create debit note"""
        debit_moves = []

        for move in self.move_ids.all():
            debit_move = self._create_debit_move(move)
            if debit_move:
                debit_moves.append(debit_move)

        return debit_moves

    def _create_debit_move(self, original_move):
        """Create debit move from original"""
        debit_move = AccountMove.objects.create(
            move_type=self._get_debit_type(original_move.move_type),
            partner_id=original_move.partner_id,
            invoice_date=self.date,
            date=self.date,
            journal_id=self.journal_id,
            ref=f"Debit Note for {original_move.name}: {self.reason}",
            company_id=self.company_id,
        )

        # Create debit lines based on copy_lines option
        if self.copy_lines == 'copy_lines':
            self._copy_lines(original_move, debit_move)
        else:
            self._refund_lines(original_move, debit_move)

        return debit_move

    def _get_debit_type(self, original_type):
        """Get debit note type"""
        if original_type in ['out_invoice', 'out_refund']:
            return 'out_invoice'  # Customer debit note
        elif original_type in ['in_invoice', 'in_refund']:
            return 'in_invoice'   # Vendor debit note
        return 'entry'

    def _copy_lines(self, original_move, debit_move):
        """Copy lines as-is"""
        for line in original_move.line_ids.all():
            if line.account_id.account_type not in ['asset_receivable', 'liability_payable']:
                AccountMoveLine.objects.create(
                    move_id=debit_move,
                    account_id=line.account_id,
                    partner_id=line.partner_id,
                    name=line.name,
                    debit=line.debit,
                    credit=line.credit,
                    tax_ids=line.tax_ids.all(),
                    product_id=line.product_id,
                    quantity=line.quantity,
                    price_unit=line.price_unit,
                )

    def _refund_lines(self, original_move, debit_move):
        """Create refund lines (reversed)"""
        for line in original_move.line_ids.all():
            if line.account_id.account_type not in ['asset_receivable', 'liability_payable']:
                AccountMoveLine.objects.create(
                    move_id=debit_move,
                    account_id=line.account_id,
                    partner_id=line.partner_id,
                    name=line.name,
                    debit=line.credit,  # Reverse
                    credit=line.debit,  # Reverse
                    tax_ids=line.tax_ids.all(),
                    product_id=line.product_id,
                    quantity=-line.quantity if line.quantity else 0,
                    price_unit=line.price_unit,
                )
