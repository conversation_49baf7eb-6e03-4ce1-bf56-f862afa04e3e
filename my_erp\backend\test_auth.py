#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from django.contrib.auth import authenticate
from django.contrib.auth.models import User

# Test authentication
users = User.objects.filter(email='<EMAIL>')
print(f"Found {users.count()} users <NAME_EMAIL>")

for user in users:
    print(f"\nUser: {user.username}")
    print(f"Email: {user.email}")
    print(f"Is active: {user.is_active}")
    print(f"Check password 'admin123': {user.check_password('admin123')}")
    
    # Test authentication
    auth_result = authenticate(username=user.username, password='admin123')
    print(f"Authentication result: {auth_result}")
    
    if auth_result:
        print("✅ Authentication successful!")
        break
else:
    print("❌ No user authenticated successfully")
    
    # Let's try to set the password for admin2
    admin2 = User.objects.filter(username='admin2').first()
    if admin2:
        print(f"\nSetting password for {admin2.username}")
        admin2.set_password('admin123')
        admin2.save()
        print("Password set successfully")
        
        # Test again
        auth_result = authenticate(username=admin2.username, password='admin123')
        print(f"Authentication result after password reset: {auth_result}")
