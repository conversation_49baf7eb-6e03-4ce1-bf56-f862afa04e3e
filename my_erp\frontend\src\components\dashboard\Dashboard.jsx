/**
 * Dashboard Component - Professional ERP Dashboard
 * Following Odoo's dashboard structure with modern UX/UI
 */
import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Space,
  Button,
  Avatar,
  Progress,
  List,
  Badge,
  Divider,
  Alert,
  Spin
} from 'antd';
import {
  DollarOutlined,
  ShoppingCartOutlined,
  ShopOutlined,
  InboxOutlined,
  TeamOutlined,
  PhoneOutlined,
  ProjectOutlined,
  BarChartOutlined,
  ToolOutlined,
  FileTextOutlined,
  CreditCardOutlined,
  WalletOutlined,
  ShoppingOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  UserOutlined,
  CalendarOutlined,
  BellOutlined
} from '@ant-design/icons';
import { useAuth } from '../shared/auth/AuthProvider';
import { useNavigate } from 'react-router-dom';
import './Dashboard.css';

const { Title, Text, Paragraph } = Typography;

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({});
  const { user, currentCompany, isSuperUser, isAdmin, canAccessModule } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Simulate loading dashboard data
    setTimeout(() => {
      setDashboardData({
        totalRevenue: 125000,
        totalExpenses: 85000,
        netProfit: 40000,
        totalCustomers: 156,
        totalVendors: 89,
        totalProducts: 234,
        totalEmployees: 45,
        totalProjects: 12,
        recentActivities: [
          { id: 1, type: 'invoice', description: 'Invoice INV/2025/001 created', time: '2 hours ago' },
          { id: 2, type: 'payment', description: 'Payment received from ABC Corp', time: '4 hours ago' },
          { id: 3, type: 'order', description: 'Sales order SO/2025/001 confirmed', time: '6 hours ago' },
          { id: 4, type: 'expense', description: 'Expense report submitted by John Doe', time: '1 day ago' },
        ],
        upcomingTasks: [
          { id: 1, title: 'Review expense reports', priority: 'high', dueDate: 'Today' },
          { id: 2, title: 'Approve purchase orders', priority: 'medium', dueDate: 'Tomorrow' },
          { id: 3, title: 'Monthly financial review', priority: 'high', dueDate: 'This week' },
        ]
      });
      setLoading(false);
    }, 1000);
  }, []);

  const moduleCards = [
    {
      key: 'accounting',
      title: 'Accounting',
      icon: <DollarOutlined />,
      color: '#1890ff',
      path: '/accounting',
      description: 'Financial management and reporting',
      stats: { value: dashboardData.totalRevenue, label: 'Total Revenue' }
    },
    {
      key: 'sales',
      title: 'Sales',
      icon: <ShoppingCartOutlined />,
      color: '#52c41a',
      path: '/sales',
      description: 'Sales orders and quotations',
      stats: { value: dashboardData.totalCustomers, label: 'Customers' }
    },
    {
      key: 'purchase',
      title: 'Purchase',
      icon: <ShopOutlined />,
      color: '#fa8c16',
      path: '/purchase',
      description: 'Purchase orders and vendor management',
      stats: { value: dashboardData.totalVendors, label: 'Vendors' }
    },
    {
      key: 'inventory',
      title: 'Inventory',
      icon: <InboxOutlined />,
      color: '#722ed1',
      path: '/inventory',
      description: 'Stock management and warehousing',
      stats: { value: dashboardData.totalProducts, label: 'Products' }
    },
    {
      key: 'hr',
      title: 'Human Resources',
      icon: <TeamOutlined />,
      color: '#eb2f96',
      path: '/hr',
      description: 'Employee management and payroll',
      stats: { value: dashboardData.totalEmployees, label: 'Employees' }
    },
    {
      key: 'crm',
      title: 'CRM',
      icon: <PhoneOutlined />,
      color: '#13c2c2',
      path: '/crm',
      description: 'Customer relationship management',
      stats: { value: '85%', label: 'Lead Conversion' }
    },
    {
      key: 'project',
      title: 'Project',
      icon: <ProjectOutlined />,
      color: '#faad14',
      path: '/project',
      description: 'Project management and tracking',
      stats: { value: dashboardData.totalProjects, label: 'Active Projects' }
    },
    {
      key: 'manufacturing',
      title: 'Manufacturing',
      icon: <ToolOutlined />,
      color: '#f5222d',
      path: '/manufacturing',
      description: 'Production planning and control',
      stats: { value: '92%', label: 'Efficiency' }
    },
    {
      key: 'pos',
      title: 'Point of Sale',
      icon: <ShoppingOutlined />,
      color: '#a0d911',
      path: '/pos',
      description: 'Retail point of sale system',
      stats: { value: '$12,500', label: 'Daily Sales' }
    },
    {
      key: 'expenses',
      title: 'Expenses',
      icon: <WalletOutlined />,
      color: '#ff7a45',
      path: '/expenses',
      description: 'Employee expense management',
      stats: { value: '$8,500', label: 'Monthly Expenses' }
    },
    {
      key: 'invoicing',
      title: 'Invoicing',
      icon: <CreditCardOutlined />,
      color: '#9254de',
      path: '/invoicing',
      description: 'Invoice management and billing',
      stats: { value: '24', label: 'Pending Invoices' }
    },
    {
      key: 'financial-reports',
      title: 'Financial Reports',
      icon: <FileTextOutlined />,
      color: '#36cfc9',
      path: '/financial-reports',
      description: 'Advanced financial reporting',
      stats: { value: '15', label: 'Reports Generated' }
    }
  ];

  const handleModuleClick = (module) => {
    if (canAccessModule(module.key)) {
      navigate(module.path);
    }
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <Spin size="large" />
        <div style={{ marginTop: 16, color: '#8c8c8c' }}>Loading dashboard...</div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      {/* Welcome Header */}
      <div className="dashboard-header">
        <Row justify="space-between" align="middle">
          <Col>
            <Space direction="vertical" size="small">
              <Title level={2} style={{ margin: 0 }}>
                Welcome back, {user?.name}! 👋
              </Title>
              <Text type="secondary">
                {isSuperUser() ? 'Super User' : isAdmin() ? 'Administrator' : 'User'} • {currentCompany?.name}
              </Text>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button type="primary" icon={<CalendarOutlined />}>
                Schedule Meeting
              </Button>
              <Button icon={<BellOutlined />}>
                <Badge count={5} size="small" />
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Key Metrics */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="metric-card">
            <Statistic
              title="Total Revenue"
              value={dashboardData.totalRevenue}
              precision={0}
              valueStyle={{ color: '#3f8600' }}
              prefix={<DollarOutlined />}
              suffix={
                <span style={{ fontSize: '14px', color: '#52c41a' }}>
                  <ArrowUpOutlined /> 12.5%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="metric-card">
            <Statistic
              title="Total Expenses"
              value={dashboardData.totalExpenses}
              precision={0}
              valueStyle={{ color: '#cf1322' }}
              prefix={<WalletOutlined />}
              suffix={
                <span style={{ fontSize: '14px', color: '#ff4d4f' }}>
                  <ArrowUpOutlined /> 8.2%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="metric-card">
            <Statistic
              title="Net Profit"
              value={dashboardData.netProfit}
              precision={0}
              valueStyle={{ color: '#1890ff' }}
              prefix={<BarChartOutlined />}
              suffix={
                <span style={{ fontSize: '14px', color: '#52c41a' }}>
                  <ArrowUpOutlined /> 15.3%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="metric-card">
            <Statistic
              title="Profit Margin"
              value={32.5}
              precision={1}
              valueStyle={{ color: '#722ed1' }}
              suffix="%"
              prefix={<ArrowUpOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Module Cards */}
      <div style={{ marginBottom: 24 }}>
        <Title level={3}>ERP Modules</Title>
        <Row gutter={[16, 16]}>
          {moduleCards.map((module) => (
            <Col xs={24} sm={12} lg={8} xl={6} key={module.key}>
              <Card
                className={`module-card ${!canAccessModule(module.key) ? 'disabled' : ''}`}
                hoverable={canAccessModule(module.key)}
                onClick={() => handleModuleClick(module)}
                style={{ 
                  borderLeft: `4px solid ${module.color}`,
                  opacity: canAccessModule(module.key) ? 1 : 0.6
                }}
              >
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Space>
                    <Avatar 
                      icon={module.icon} 
                      style={{ backgroundColor: module.color }}
                      size="large"
                    />
                    <div>
                      <Title level={5} style={{ margin: 0 }}>{module.title}</Title>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {module.description}
                      </Text>
                    </div>
                  </Space>
                  <Divider style={{ margin: '8px 0' }} />
                  <div style={{ textAlign: 'center' }}>
                    <Text strong style={{ fontSize: '18px', color: module.color }}>
                      {module.stats.value}
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {module.stats.label}
                    </Text>
                  </div>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* Recent Activities and Tasks */}
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="Recent Activities" className="activity-card">
            <List
              dataSource={dashboardData.recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} size="small" />}
                    title={item.description}
                    description={item.time}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Upcoming Tasks" className="tasks-card">
            <List
              dataSource={dashboardData.upcomingTasks}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Badge 
                        color={item.priority === 'high' ? '#ff4d4f' : item.priority === 'medium' ? '#faad14' : '#52c41a'} 
                      />
                    }
                    title={item.title}
                    description={`Due: ${item.dueDate}`}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Card title="Quick Actions" style={{ marginTop: 24 }}>
        <Row gutter={[16, 16]}>
          <Col xs={12} sm={8} md={6}>
            <Button 
              type="primary" 
              block 
              icon={<CreditCardOutlined />}
              onClick={() => navigate('/accounting/invoices/create')}
            >
              Create Invoice
            </Button>
          </Col>
          <Col xs={12} sm={8} md={6}>
            <Button 
              block 
              icon={<ShoppingCartOutlined />}
              onClick={() => navigate('/sales/orders/create')}
            >
              New Sales Order
            </Button>
          </Col>
          <Col xs={12} sm={8} md={6}>
            <Button 
              block 
              icon={<ShopOutlined />}
              onClick={() => navigate('/purchase/orders/create')}
            >
              New Purchase Order
            </Button>
          </Col>
          <Col xs={12} sm={8} md={6}>
            <Button 
              block 
              icon={<WalletOutlined />}
              onClick={() => navigate('/expenses/create')}
            >
              Submit Expense
            </Button>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default Dashboard;
