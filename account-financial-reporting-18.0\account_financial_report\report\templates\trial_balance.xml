<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <template id="trial_balance">
        <t t-call="account_financial_report.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-call="account_financial_report.internal_layout">
                    <t t-call="account_financial_report.report_trial_balance_base" />
                </t>
            </t>
        </t>
    </template>
    <template id="report_trial_balance_base">
        <!-- Saved flag fields into variables, used to define columns display -->
        <t t-set="show_partner_details" t-value="show_partner_details" />
        <t t-set="foreign_currency" t-value="foreign_currency" />
        <t t-set="show_hierarchy_level" t-value="show_hierarchy_level" />
        <t t-set="limit_hierarchy_level" t-value="limit_hierarchy_level" />
        <!-- Defines global variables used by internal layout -->
        <t t-set="title">
            Trial Balance -
            <t t-out="company_name" />
            -
            <t t-out="currency_name" />
        </t>
        <t t-set="company_name" t-value="Company_Name" />
        <!--       <t t-set="res_company" t-value="company_id"/>-->
        <t class="page">
            <div class="row">
                <h4
                    class="mt0"
                    t-out="title or 'Odoo Report'"
                    style="text-align: center;"
                />
            </div>
            <!-- Display filters -->
            <t t-call="account_financial_report.report_trial_balance_filters" />
            <div class="act_as_table list_table" style="margin-top: 10px;" />
            <!-- Display account lines -->
            <t t-set="aml_domain_extra" t-value="[]" />
            <t t-if="not show_partner_details">
                <t t-if="trial_balance_grouped">
                    <t t-foreach="trial_balance_grouped" t-as="grouped_item">
                        <div class="act_as_table data_table" style="width: 100%;">
                            <div class="act_as_thead">
                                <div class="act_as_row labels">
                                    <div class="act_as_cell" style="width: 100%;">
                                        <t t-esc="grouped_item['name']" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <t
                            t-set="aml_domain_extra"
                            t-if="grouped_item['id'] > 0"
                            t-value="[('analytic_account_ids', '=', grouped_item['id'])]"
                        />
                        <t
                            t-set="aml_domain_extra"
                            t-else=""
                            t-value="[('analytic_account_ids', '=', False)]"
                        />
                        <div class="act_as_table data_table" style="width: 100%;">
                            <t
                                t-call="account_financial_report.report_trial_balance_lines_header"
                            />
                            <!-- Display each lines -->
                            <t t-foreach="grouped_item['account_data']" t-as="balance">
                                <!-- Adapt -->
                                <t t-set="style" t-value="'font-size:12px;'" />
                                <t
                                    t-call="account_financial_report.report_trial_balance_line"
                                />
                            </t>
                        </div>
                        <!-- Display footer with totals !-->
                        <t t-set="balance" t-value="grouped_item" />
                        <t
                            t-call="account_financial_report.report_trial_balance_account_footer"
                        />
                    </t>
                    <!-- TOTAL !-->
                    <t t-if="grouped_by">
                        <t t-set="balance" t-value="total_amount_grouped" />
                        <t
                            t-call="account_financial_report.report_trial_balance_account_footer"
                        />
                    </t>
                </t>
                <t t-else="">
                    <div class="act_as_table data_table" style="width: 100%;">
                        <!-- Display account header -->
                        <t
                            t-call="account_financial_report.report_trial_balance_lines_header"
                        />
                        <!-- Display each lines -->
                        <t t-foreach="trial_balance" t-as="balance">
                            <!-- Adapt -->
                            <t t-set="style" t-value="'font-size:12px;'" />
                            <!-- Different style for account group -->
                            <t t-if="show_hierarchy">
                                <t t-if="balance['type'] == 'group_type'">
                                    <t
                                        t-set="style"
                                        t-value="style + 'font-weight: bold; color: blue;'"
                                    />
                                </t>
                            </t>
                            <t t-if="show_hierarchy and limit_hierarchy_level">
                                <t
                                    t-if="show_hierarchy_level > balance['level'] and (not hide_parent_hierarchy_level or (show_hierarchy_level - 1) == balance['level'])"
                                >
                                    <t
                                        t-call="account_financial_report.report_trial_balance_line"
                                    />
                                </t>
                            </t>
                            <t t-else="">
                                <t
                                    t-call="account_financial_report.report_trial_balance_line"
                                />
                            </t>
                        </t>
                    </div>
                </t>
            </t>
            <!-- Display partner lines -->
            <t t-if="show_partner_details">
                <t t-set="padding" t-value="0" />
                <t t-foreach="total_amount.keys()" t-as="account_id">
                    <div class="page_break">
                        <t t-set="style" t-value="'font-size:12px;'" />
                        <!-- Display account header -->
                        <div
                            class="act_as_table list_table"
                            style="margin-top: 10px;"
                        />
                        <div class="act_as_caption account_title" style="width: 100%;">
                            <span
                                t-att-res-id="account_id"
                                res-model="account.account"
                                view-type="form"
                            >
                                        <t
                                t-out="accounts_data[account_id]['code']"
                            /> - <t t-out="accounts_data[account_id]['name']" />
                            </span>
                        </div>
                        <div class="act_as_table data_table" style="width: 100%;">
                            <!-- Display account/partner header -->
                            <t
                                t-call="account_financial_report.report_trial_balance_lines_header"
                            />
                            <!-- Adapt style -->
                            <t t-set="padding" t-value="padding+4" />
                            <!-- Display each partners -->
                            <t
                                t-foreach="total_amount[account_id].keys()"
                                t-as="partner_id"
                            >
                                <t t-if="isinstance(partner_id, int)">
                                    <t t-set="type" t-value='"partner_type"' />
                                    <!-- Display partner line -->
                                    <t
                                        t-call="account_financial_report.report_trial_balance_line"
                                    />
                                </t>
                            </t>
                            <t t-set="padding" t-value="padding-4" />
                        </div>
                        <!-- Display account footer -->
                        <t t-set="type" t-value='"account_type"' />
                        <t
                            t-call="account_financial_report.report_trial_balance_account_footer"
                        />
                    </div>
                </t>
            </t>
        </t>
    </template>
    <template id="account_financial_report.report_trial_balance_filters">
        <div class="act_as_table data_table" style="width: 100%;">
            <div class="act_as_row labels">
                <div class="act_as_cell">Date range filter</div>
                <div class="act_as_cell">Target moves filter</div>
                <div class="act_as_cell">Account at 0 filter</div>
                <div class="act_as_cell">Limit hierarchy levels</div>
            </div>
            <div class="act_as_row">
                <div class="act_as_cell">
                    From:
                    <span t-out="date_from" t-options="{'widget': 'date'}" />
                    To
                    <span t-out="date_to" t-options="{'widget': 'date'}" />
                </div>
                <div class="act_as_cell">
                    <t t-if="only_posted_moves">All posted entries</t>
                    <t t-if="not only_posted_moves">All entries</t>
                </div>
                <div class="act_as_cell">
                    <t t-if="hide_account_at_0">Hide</t>
                    <t t-if="not hide_account_at_0">Show</t>
                </div>
                <div class="act_as_cell">
                    <t t-if="limit_hierarchy_level">
                        Level
                        <span t-out="show_hierarchy_level" />
                    </t>
                    <t t-if="not limit_hierarchy_level">No limit</t>
                </div>
            </div>
        </div>
    </template>
    <template id="account_financial_report.report_trial_balance_lines_header">
        <!-- Display table headers for lines -->
        <div class="act_as_thead">
            <div class="act_as_row labels">
                <t t-if="not show_partner_details">
                    <!--## Code-->
                    <div class="act_as_cell" style="width: 8%;">Code</div>
                    <!--## Account-->
                    <div class="act_as_cell" style="width: 25%;">Account</div>
                </t>
                <t t-if="show_partner_details">
                    <!--## Partner-->
                    <div class="act_as_cell" style="width: 33%;">Partner</div>
                </t>
                <!--## Initial balance-->
                <div class="act_as_cell" style="width: 9%;">
                    Initial
                    balance
                </div>
                <!--## Debit-->
                <div class="act_as_cell" style="width: 9%;">Debit</div>
                <!--## Credit-->
                <div class="act_as_cell" style="width: 9%;">Credit</div>
                <!--## Period balance-->
                <div class="act_as_cell" style="width: 9%;">Period balance</div>
                <!--## Ending balance-->
                <div class="act_as_cell" style="width: 9%;">Ending balance</div>
                <t t-if="foreign_currency">
                    <!--## amount_currency-->
                    <div class="act_as_cell" style="width: 11%;">Initial
                        balance cur.</div>
                    <div class="act_as_cell" style="width: 11%;">Ending balance
                        cur.</div>
                </t>
            </div>
        </div>
    </template>
    <template id="account_financial_report.report_trial_balance_line">
        <t
            t-set="aml_domain_common"
            t-if="only_posted_moves"
            t-value="[('parent_state', '=', 'posted')]"
        />
        <t
            t-set="aml_domain_common"
            t-else=""
            t-value="[('parent_state', 'in', ('posted', 'draft'))]"
        />
        <!-- # line -->
        <div class="act_as_row lines">
            <t t-if="not show_partner_details">
                <!--## Code-->
                <t t-if="balance['type'] == 'account_type'">
                    <div class="act_as_cell left" t-att-style="style">
                        <span
                            t-att-res-id="balance['id']"
                            res-model="account.account"
                            view-type="form"
                        >
                            <t t-out="balance['code']" />
                        </span>
                    </div>
                    <!--            ## Account/Partner-->
                    <div class="act_as_cell left" t-att-style="style">
                        <span
                            t-att-res-id="balance['id']"
                            res-model="account.account"
                            view-type="form"
                        >
                            <t t-out="balance['name']" />
                        </span>
                    </div>
                </t>
                <t t-if="balance['type'] == 'group_type'">
                    <div class="act_as_cell left" t-att-style="style">
                        <t t-set="res_model" t-value="'account.group'" />
                        <span
                            t-att-res-id="balance['id']"
                            res-model="account.group"
                            view-type="form"
                        >
                            <t t-out="balance['code']" />
                        </span>
                    </div>
                    <div class="act_as_cell left" t-att-style="style">
                        <t t-set="res_model" t-value="'account.group'" />
                        <span
                            t-att-res-id="balance['id']"
                            res-model="account.group"
                            view-type="form"
                        >
                            <t t-out="balance['name']" />
                        </span>
                    </div>
                </t>
            </t>
            <t t-if="show_partner_details">
                <div class="act_as_cell left" t-att-style="style">
                    <t t-set="res_model" t-value="'res.partner'" />
                    <span
                        t-att-res-id="partner_id"
                        res-model="res.partner"
                        view-type="form"
                    >
                        <t t-out="partners_data[partner_id]['name']" />
                    </span>
                </div>
            </t>
            <!--## Initial balance-->
            <div class="act_as_cell amount" t-att-style="style">
                <t t-if="not show_partner_details">
                    <t t-if="balance['type'] == 'account_type'">
                        <t
                            t-set="domain"
                            t-value="[('account_id', '=', balance['id']),
                               ('date', '&lt;', date_from)]"
                        />
                        <span
                            t-att-domain="domain+aml_domain_common+aml_domain_extra"
                            res-model="account.move.line"
                        >
                            <t
                                t-out="balance['initial_balance']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </span>
                    </t>
                    <t t-if="balance['type'] == 'group_type'">
                        <t
                            t-set="domain"
                            t-value="[('account_id', 'in', balance['account_ids']),
                                    ('date', '&lt;', date_from)]"
                        />
                        <span
                            t-att-domain="domain+aml_domain_common"
                            res-model="account.move.line"
                        >
                            <t
                                t-out="balance['initial_balance']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </span>
                    </t>
                </t>
                <t t-if="type == 'partner_type'">
                    <t
                        t-set="domain"
                        t-value="[('account_id', '=', int(account_id)),
                                 ('partner_id', '=', int(partner_id)),
                                 ('date', '&lt;', date_from)]"
                    />
                    <span
                        t-att-domain="domain+aml_domain_common"
                        res-model="account.move.line"
                    >
                        <t
                            t-out="total_amount[account_id][partner_id]['initial_balance']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </span>
                </t>
            </div>
            <!--## Debit-->
            <div class="act_as_cell amount" t-att-style="style">
                <t t-if="not show_partner_details">
                    <t t-if="balance['type'] == 'account_type'">
                        <t
                            t-set="domain"
                            t-value="[('account_id', '=', balance['id']),
                                         ('date', '&gt;=', date_from),
                                         ('date', '&lt;=', date_to),
                                         ('debit', '&lt;&gt;', 0)]"
                        />
                        <span
                            t-att-domain="domain+aml_domain_common+aml_domain_extra"
                            res-model="account.move.line"
                        >
                            <t
                                t-out="balance['debit']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </span>
                    </t>
                    <t t-if="balance['type'] == 'group_type'">
                        <t
                            t-set="domain"
                            t-value="[('account_id', 'in', balance['account_ids']),
                                    ('date', '&gt;=', date_from),
                                    ('date', '&lt;=', date_to),
                                    ('debit', '&lt;&gt;', 0)]"
                        />
                        <span
                            t-att-domain="domain+aml_domain_common"
                            res-model="account.move.line"
                        >
                            <t
                                t-out="balance['debit']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </span>
                    </t>
                </t>
                <t t-if="type == 'partner_type'">
                    <t
                        t-set="domain"
                        t-value="[('account_id', '=', int(account_id)),
                                 ('partner_id', '=', int(partner_id)),
                                 ('date', '&gt;=', date_from),
                                 ('date', '&lt;=', date_to),
                                 ('debit', '&lt;&gt;', 0)]"
                    />
                    <span
                        t-att-domain="domain+aml_domain_common"
                        res-model="account.move.line"
                    >
                        <t
                            t-out="total_amount[account_id][partner_id]['debit']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </span>
                </t>
            </div>
            <!--            &lt;!&ndash;## Credit&ndash;&gt;-->
            <div class="act_as_cell amount" t-att-style="style">
                <t t-if="not show_partner_details">
                    <t t-if="balance['type'] == 'account_type'">
                        <t
                            t-set="domain"
                            t-value="[('account_id', '=', balance['id']),
                                         ('date', '&gt;=', date_from),
                                         ('date', '&lt;=', date_to),
                                         ('credit', '&lt;&gt;', 0)]"
                        />
                        <span
                            t-att-domain="domain+aml_domain_common+aml_domain_extra"
                            res-model="account.move.line"
                        >
                            <t
                                t-out="balance['credit']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </span>
                    </t>
                    <t t-if="balance['type'] == 'group_type'">
                        <t
                            t-set="domain"
                            t-value="[('account_id', 'in', balance['account_ids']),
                                    ('date', '&gt;=', date_from),
                                    ('date', '&lt;=', date_to),
                                    ('credit', '&lt;&gt;', 0)]"
                        />
                        <span
                            t-att-domain="domain+aml_domain_common"
                            res-model="account.move.line"
                        >
                            <t
                                t-out="balance['credit']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </span>
                    </t>
                </t>
                <t t-if="type == 'partner_type'">
                    <t
                        t-set="domain"
                        t-value="[('account_id', '=', int(account_id)),
                                 ('partner_id', '=', int(partner_id)),
                                 ('date', '&gt;=', date_from),
                                 ('date', '&lt;=', date_to),
                                 ('credit', '&lt;&gt;', 0)]"
                    />
                    <span
                        t-att-domain="domain+aml_domain_common"
                        res-model="account.move.line"
                    >
                        <t
                            t-out="total_amount[account_id][partner_id]['credit']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </span>
                </t>
            </div>
            <!--            &lt;!&ndash;## Period balance&ndash;&gt;-->
            <div class="act_as_cell amount" t-att-style="style">
                <t t-if="not show_partner_details">
                    <t t-if="balance['type'] == 'account_type'">
                        <t
                            t-set="domain"
                            t-value="[('account_id', '=', balance['id']),
                                    ('date', '&gt;=', date_from),
                                    ('date', '&lt;=', date_to),
                                    ('balance', '&lt;&gt;', 0)]"
                        />
                        <span
                            t-att-domain="domain+aml_domain_common+aml_domain_extra"
                            res-model="account.move.line"
                        >
                            <t
                                t-out="balance['balance']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </span>
                    </t>
                    <t t-if="balance['type'] == 'group_type'">
                        <t
                            t-set="domain"
                            t-value="[('account_id', 'in', balance['account_ids']),
                                    ('date', '&gt;=', date_from),
                                    ('date', '&lt;=', date_to)]"
                        />
                        <span
                            t-att-domain="domain+aml_domain_common"
                            res-model="account.move.line"
                        >
                            <t
                                t-out="balance['balance']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </span>
                    </t>
                </t>
                <t t-if="type == 'partner_type'">
                    <t
                        t-set="domain"
                        t-value="[('account_id', '=', int(account_id)),
                                 ('partner_id', '=', int(partner_id)),
                                 ('date', '&gt;=', date_from),
                                 ('date', '&lt;=', date_to),
                                 ('balance', '&lt;&gt;', 0)]"
                    />
                    <span
                        t-att-domain="domain+aml_domain_common"
                        res-model="account.move.line"
                    >
                        <t
                            t-out="total_amount[account_id][partner_id]['balance']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </span>
                </t>
            </div>
            <!--            &lt;!&ndash;## Ending balance&ndash;&gt;-->
            <div class="act_as_cell amount" t-att-style="style">
                <t t-if="not show_partner_details">
                    <t t-if="balance['type'] == 'account_type'">
                        <t
                            t-set="domain"
                            t-value="[('account_id', '=', balance['id']),
                                         ('date', '&lt;=', date_to)]"
                        />
                        <span
                            t-att-domain="domain+aml_domain_common+aml_domain_extra"
                            res-model="account.move.line"
                        >
                            <t
                                t-out="balance['ending_balance']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </span>
                    </t>
                    <t t-if="balance['type'] == 'group_type'">
                        <t
                            t-set="domain"
                            t-value="[('account_id', 'in', balance['account_ids'])]"
                        />
                        <span
                            t-att-domain="domain+aml_domain_common"
                            res-model="account.move.line"
                        >
                            <t
                                t-out="balance['ending_balance']"
                                t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                            />
                        </span>
                    </t>
                </t>
                <t t-if="type == 'partner_type'">
                    <t
                        t-set="domain"
                        t-value="[('account_id', '=', int(account_id)),
                                 ('partner_id', '=', int(partner_id)),
                                 ('date', '&lt;=', date_to)]"
                    />
                    <span
                        t-att-domain="domain+aml_domain_common"
                        res-model="account.move.line"
                    >
                        <t
                            t-out="total_amount[account_id][partner_id]['ending_balance']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </span>
                </t>
            </div>
            <t t-if="foreign_currency">
                <t t-if="not show_partner_details">
                    <t t-if="balance['type'] == 'account_type'">
                        <t t-if="balance['currency_id']">
                            <t
                                t-set="balance_currency"
                                t-value="currency_model.browse(balance['currency_id'])"
                            />
                            <!--## Initial balance cur.-->
                            <div class="act_as_cell amount" t-att-style="style">
                                <t
                                    t-set="domain"
                                    t-value="[('account_id', '=', balance['id'])]"
                                />
                                <span
                                    t-att-domain="domain+aml_domain_common+aml_domain_extra"
                                    res-model="account.move.line"
                                >
                                    <t
                                        t-out="balance['initial_currency_balance']"
                                        t-options="{'widget': 'monetary', 'display_currency': balance_currency}"
                                    />
                                </span>
                                <!--                            <t t-if="line.account_group_id">-->
                                <!--                                <t t-set="domain"-->
                                <!--                                   t-value="[('account_id', 'in', line.compute_account_ids.ids)]"/>-->
                                <!--                                <span>-->
                                <!--                                    <a t-att-data-t-att-domain="domain"-->
                                <!--                                       t-att-data-res-model="'account.move.line'"-->
                                <!--                                       class="o_account_financial_reports_web_action_monetary_multi"-->
                                <!--                                       t-att-style="style">-->
                                <!--                                    <t t-att-style="style" t-out="line.initial_balance_foreign_currency" t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/></a>-->
                                <!--                                </span>-->
                                <!--                            </t>-->
                            </div>
                        </t>
                    </t>
                </t>
                <t t-if="show_partner_details">
                    <t t-if="total_amount[account_id]['currency_id']">
                        <t t-if="type == 'partner_type'">
                            <div class="act_as_cell amount" t-att-style="style">
                                <t
                                    t-set="domain"
                                    t-value="[('account_id', '=', account_id),
                                            ('partner_id', '=', partner_id)]"
                                />
                                <span
                                    t-att-domain="domain+aml_domain_common"
                                    res-model="account.move.line"
                                >
                                    <t
                                        t-set="total_amount_item_currency"
                                        t-value="currency_model.browse(total_amount[account_id]['currency_id'])"
                                    />
                                    <t
                                        t-out="total_amount[account_id][partner_id]['initial_currency_balance']"
                                        t-options="{'widget': 'monetary', 'display_currency': total_amount_item_currency}"
                                    />
                                </span>
                            </div>
                        </t>
                    </t>
                </t>
                <!--## Ending balance cur.-->
                <t t-if="not show_partner_details">
                    <t t-if="balance['type'] == 'account_type'">
                        <t t-if="balance['currency_id']">
                            <div class="act_as_cell amount" t-att-style="style">
                                <t
                                    t-set="domain"
                                    t-value="[('account_id', '=', balance['id'])]"
                                />
                                <span
                                    t-att-domain="domain+aml_domain_common+aml_domain_extra"
                                    res-model="account.move.line"
                                >
                                    <t
                                        t-out="balance['ending_currency_balance']"
                                        t-options="{'widget': 'monetary', 'display_currency': balance_currency}"
                                    />
                                </span>
                                <!--                            <t t-if="line.account_group_id">-->
                                <!--                                <t t-set="domain"-->
                                <!--                                   t-value="[('account_id', 'in', line.compute_account_ids.ids)]"/>-->
                                <!--                                <span>-->
                                <!--                                    <a t-att-data-t-att-domain="domain"-->
                                <!--                                       t-att-data-res-model="'account.move.line'"-->
                                <!--                                       class="o_account_financial_reports_web_action_monetary_multi"-->
                                <!--                                       t-att-style="style">-->
                                <!--                                    <t t-att-style="style" t-out="line.final_balance_foreign_currency" t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/></a>-->
                                <!--                                </span>-->
                                <!--                            </t>-->
                            </div>
                        </t>
                    </t>
                </t>
                <t t-if="show_partner_details">
                    <t t-if="total_amount[account_id]['currency_id']">
                        <div class="act_as_cell amount" t-att-style="style">
                            <t t-if="type == 'partner_type'">
                                <t
                                    t-set="domain"
                                    t-value="[('account_id', '=', account_id),
                                            ('partner_id', '=', partner_id)]"
                                />
                                <span
                                    t-att-domain="domain+aml_domain_common"
                                    res-model="account.move.line"
                                >
                                    <t
                                        t-set="total_amount_item_currency"
                                        t-value="currency_model.browse(total_amount[account_id]['currency_id'])"
                                    />
                                    <t
                                        t-out="total_amount[account_id][partner_id]['ending_currency_balance']"
                                        t-options="{'widget': 'monetary', 'display_currency': total_amount_item_currency}"
                                    />
                                </span>
                            </t>
                        </div>
                    </t>
                </t>
                <t t-if="show_partner_details">
                    <t t-if="not total_amount[account_id]['currency_id']">
                        <!--## balance_currency-->
                        <div class="act_as_cell" />
                        <div class="act_as_cell" />
                    </t>
                </t>
                <t t-if="not show_partner_details">
                    <t t-if="balance['type'] == 'account_type'">
                        <t t-if="not balance['currency_id']">
                            <!--## balance_currency-->
                            <div class="act_as_cell" />
                            <div class="act_as_cell" />
                        </t>
                    </t>
                    <t t-if="balance['type'] == 'group_type'">
                        <!--## balance_currency-->
                        <div class="act_as_cell" />
                        <div class="act_as_cell" />
                    </t>
                </t>
            </t>
        </div>
    </template>
    <!--    <template id="account_financial_report.report_trial_balance_account_footer">-->
    <!--        &lt;!&ndash; Display account footer &ndash;&gt;-->
    <!--        <div class="act_as_table list_table" style="width: 100%;">-->
    <!--            <div class="act_as_row labels" style="font-weight: bold;">-->
    <!--                &lt;!&ndash;## Account&ndash;&gt;-->
    <!--                <div class="act_as_cell left" style="width: 61.44%;">-->
    <!--                    <t t-set="res_model" t-value="'account.account'"/>-->
    <!--                    <span>-->
    <!--                        <a t-att-data-active-id="account.account_id.id"-->
    <!--                           t-att-data-res-model="res_model"-->
    <!--                           class="o_account_financial_reports_web_action"-->
    <!--                           t-att-style="style">-->
    <!--                            <t t-att-style="style" t-out="account.code"/> - <t t-att-style="style" t-out="account.name"/></a>-->
    <!--                    </span>-->
    <!--                </div>-->
    <!--                &lt;!&ndash;## Initial balance&ndash;&gt;-->
    <!--                <div class="act_as_cell amount" style="width: 9.64%;">-->
    <!--                    <t t-set="domain"-->
    <!--                        t-value="[('account_id', '=', account.account_id.id),-->
    <!--                                  ('date', '&lt;', o.date_from.strftime('%Y-%m-%d'))]"/>-->
    <!--                    <span>-->
    <!--                        <a t-att-data-t-att-domain="domain"-->
    <!--                           t-att-data-res-model="'account.move.line'"-->
    <!--                           class="o_account_financial_reports_web_action_monetary_multi"-->
    <!--                           t-att-style="style">-->
    <!--                        <t t-att-style="style" t-out="account.initial_balance" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></a>-->
    <!--                    </span>-->
    <!--                </div>-->
    <!--                &lt;!&ndash;## Debit&ndash;&gt;-->
    <!--                <div class="act_as_cell amount" style="width: 9.64%;">-->
    <!--                    <t t-set="domain"-->
    <!--                        t-value="[('account_id', '=', account.account_id.id),-->
    <!--                                  ('date', '&gt;=', account.report_id.date_from.strftime('%Y-%m-%d')),-->
    <!--                                  ('date', '&lt;=', account.report_id.date_to.strftime('%Y-%m-%d')),-->
    <!--                                  ('debit', '&lt;&gt;', 0)]"/>-->
    <!--                    <span>-->
    <!--                        <a t-att-data-t-att-domain="domain"-->
    <!--                           t-att-data-res-model="'account.move.line'"-->
    <!--                           class="o_account_financial_reports_web_action_monetary_multi"-->
    <!--                           t-att-style="style">-->
    <!--                            <t t-att-style="style" t-out="account.debit" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></a>-->
    <!--                    </span>-->
    <!--                </div>-->
    <!--                &lt;!&ndash;## Credit&ndash;&gt;-->
    <!--                <div class="act_as_cell amount" style="width: 9.64%;">-->
    <!--                    <t t-set="domain"-->
    <!--                        t-value="[('account_id', '=', account.account_id.id),-->
    <!--                                  ('date', '&gt;=', account.report_id.date_from.strftime('%Y-%m-%d')),-->
    <!--                                  ('date', '&lt;=', account.report_id.date_to.strftime('%Y-%m-%d')),-->
    <!--                                  ('credit', '&lt;&gt;', 0)]"/>-->
    <!--                    <span>-->
    <!--                        <a t-att-data-t-att-domain="domain"-->
    <!--                           t-att-data-res-model="'account.move.line'"-->
    <!--                           class="o_account_financial_reports_web_action_monetary_multi"-->
    <!--                           t-att-style="style">-->
    <!--                            <t t-att-style="style" t-out="account.credit" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></a>-->
    <!--                    </span>-->
    <!--                </div>-->
    <!--                &lt;!&ndash;## Period Balance &ndash;&gt;-->
    <!--                <div class="act_as_cell amount" style="width: 9.64%;">-->
    <!--                    <t t-set="domain"-->
    <!--                        t-value="[('account_id', '=', account.account_id.id),-->
    <!--                                  ('date', '&gt;=', account.report_id.date_from.strftime('%Y-%m-%d')),-->
    <!--                                  ('date', '&lt;=', account.report_id.date_to.strftime('%Y-%m-%d')),-->
    <!--                                  ('period_balance', '&lt;&gt;', 0)]"/>-->
    <!--                    <span>-->
    <!--                        <a t-att-data-t-att-domain="domain"-->
    <!--                           t-att-data-res-model="'account.move.line'"-->
    <!--                           class="o_account_financial_reports_web_action_monetary_multi"-->
    <!--                           t-att-style="style">-->
    <!--                            <t t-att-style="style" t-out="account.period_balance" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></a>-->
    <!--                    </span>-->
    <!--                </div>-->
    <!--                &lt;!&ndash;## Ending balance&ndash;&gt;-->
    <!--                <div class="act_as_cell amount" style="width: 9.64%;">-->
    <!--                    <t t-set="domain"-->
    <!--                           t-value="[('account_id', '=', account.account_id.id)]"/>-->
    <!--                    <span>-->
    <!--                        <a t-att-data-t-att-domain="domain"-->
    <!--                           t-att-data-res-model="'account.move.line'"-->
    <!--                           class="o_account_financial_reports_web_action_monetary_multi"-->
    <!--                           t-att-style="style" >-->
    <!--                            <t t-att-style="style" t-out="account.final_balance" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></a>-->
    <!--                    </span>-->
    <!--                </div>-->
    <!--                <t t-if="foreign_currency">-->
    <!--                    <t t-if="account.currency_id.id">-->
    <!--                        &lt;!&ndash;## currency_name&ndash;&gt;-->
    <!--                        <div class="act_as_cell" style="width: 4.43%;">-->
    <!--                            <t t-set="domain"-->
    <!--                            t-value="[('account_id', '=', account.account_id.id)]"/>-->
    <!--                            <span t-field="account.currency_id.display_name"/>-->
    <!--                        </div>-->
    <!--                        &lt;!&ndash;## balance_currency&ndash;&gt;-->
    <!--                        <div class="act_as_cell amount" style="width: 8.86%;">-->
    <!--                            <t t-set="domain"-->
    <!--                            t-value="[('account_id', '=', account.account_id.id),-->
    <!--                                      ('date', '&lt;', date_from.strftime('%Y-%m-%d'))]"/>-->
    <!--                            <span>-->
    <!--                                <a t-att-data-t-att-domain="domain"-->
    <!--                                   t-att-data-res-model="'account.move.line'"-->
    <!--                                   class="o_account_financial_reports_web_action_monetary_multi"-->
    <!--                                   t-att-style="style">-->
    <!--                                <t t-att-style="style" t-out="account.initial_balance_foreign_currency" t-options="{'widget': 'monetary', 'display_currency': account.account_id.currency_id}"/></a>-->
    <!--                            </span>-->
    <!--                        </div>-->
    <!--                        <div class="act_as_cell amount" style="width: 8.86%;">-->
    <!--                            <t t-set="domain"-->
    <!--                               t-value="[('account_id', '=', account.account_id.id)]"/>-->
    <!--                            <span>-->
    <!--                                <a t-att-data-t-att-domain="domain"-->
    <!--                                   t-att-data-res-model="'account.move.line'"-->
    <!--                                   class="o_account_financial_reports_web_action_monetary_multi"-->
    <!--                                   t-att-style="style" >-->
    <!--                                    <t t-att-style="style" t-out="account.final_balance_foreign_currency" t-options="{'widget': 'monetary', 'display_currency': account.account_id.currency_id}"/></a>-->
    <!--                            </span>-->
    <!--                        </div>-->
    <!--                    </t>-->
    <!--                    <t t-if="not account.currency_id.id">-->
    <!--                        <div class="act_as_cell" style="width: 4.43%;"/>-->
    <!--                        <div class="act_as_cell" style="width: 8.86%;"/>-->
    <!--                        <div class="act_as_cell" style="width: 8.86%;"/>-->
    <!--                    </t>-->
    <!--                </t>-->
    <!--            </div>-->
    <!--        </div>-->
    <!--    </template>-->
    <template id="account_financial_report.report_trial_balance_account_footer">
        <!-- Display ending balance line for account or partner -->
        <div class="act_as_table list_table" style="width: 100%;">
            <div class="act_as_row labels" style="font-weight: bold;">
                <!--## date-->
                <div class="act_as_cell first_column" style="width: 33%;">
                    <t
                        t-if="grouped_by and balance['type'] in ('analytic_account_type', 'total')"
                    >
                        <span t-out="balance['name']" />
                    </t>
                    <t t-else="">
                        <span t-out="accounts_data[account_id]['code']" />
                        -
                        <span t-out="accounts_data[account_id]['name']" />
                    </t>
                </div>
                <!--## Initial Balance-->
                <div class="act_as_cell amount" style="width: 9%;">
                    <t
                        t-if="grouped_by and balance['type'] in ('analytic_account_type', 'total')"
                    >
                        <span
                            t-out="balance['initial_balance']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </t>
                    <t t-else="">
                        <span
                            t-out="total_amount[account_id]['initial_balance']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </t>
                </div>
                <!--## Debit-->
                <div class="act_as_cell amount" style="width: 9%;">
                    <t
                        t-if="grouped_by and balance['type'] in ('analytic_account_type', 'total')"
                    >
                        <span
                            t-out="balance['debit']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </t>
                    <t t-else="">
                        <span
                            t-out="total_amount[account_id]['debit']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </t>
                </div>
                <!--## Credit-->
                <div class="act_as_cell amount" style="width: 9%;">
                    <t
                        t-if="grouped_by and balance['type'] in ('analytic_account_type', 'total')"
                    >
                        <span
                            t-out="balance['credit']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </t>
                    <t t-else="">
                        <span
                            t-out="total_amount[account_id]['credit']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </t>
                </div>
                <!--## Period balance-->
                <div class="act_as_cell amount" style="width: 9%;">
                    <t
                        t-if="grouped_by and balance['type'] in ('analytic_account_type', 'total')"
                    >
                        <span
                            t-out="balance['balance']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </t>
                    <t t-else="">
                        <span
                            t-out="total_amount[account_id]['balance']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </t>
                </div>
                <!--## Ending balance-->
                <div class="act_as_cell amount" style="width: 9%;">
                    <t
                        t-if="grouped_by and balance['type'] in ('analytic_account_type', 'total')"
                    >
                        <span
                            t-out="balance['ending_balance']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </t>
                    <t t-else="">
                        <span
                            t-out="total_amount[account_id]['ending_balance']"
                            t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"
                        />
                    </t>
                </div>
                <t t-if="foreign_currency">
                    <!--## amount_total_due_currency-->
                    <div class="act_as_cell" style="width: 11%;" />
                    <!--## amount_residual_currency-->
                    <div class="act_as_cell" style="width: 11%;" />
                </t>
            </div>
        </div>
    </template>
</odoo>
