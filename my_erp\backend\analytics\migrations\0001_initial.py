# Generated by Django 4.2.21 on 2025-07-15 19:37

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounting', '0003_delete_ircron'),
    ]

    operations = [
        migrations.CreateModel(
            name='AnalyticsDashboard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('type', models.CharField(choices=[('sales', 'Sales Dashboard'), ('financial', 'Financial Dashboard'), ('hr', 'HR Dashboard'), ('inventory', 'Inventory Dashboard'), ('project', 'Project Dashboard'), ('crm', 'CRM Dashboard'), ('executive', 'Executive Dashboard')], default='executive', max_length=16)),
                ('active', models.BooleanField(default=True)),
                ('config', models.TextField(default='{}', help_text='Dashboard Configuration (JSON)')),
                ('public', models.BooleanField(default=False, help_text='Public Dashboard')),
                ('layout', models.TextField(default='{}', help_text='Dashboard Layout (JSON)')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('user_ids', models.ManyToManyField(blank=True, help_text='Allowed Users', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'analytics_dashboard',
            },
        ),
        migrations.CreateModel(
            name='IrUiView',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('model', models.CharField(help_text='Model', max_length=64)),
                ('type', models.CharField(choices=[('tree', 'Tree'), ('form', 'Form'), ('kanban', 'Kanban'), ('calendar', 'Calendar'), ('pivot', 'Pivot'), ('graph', 'Graph'), ('dashboard', 'Dashboard'), ('search', 'Search')], default='tree', max_length=16)),
                ('arch', models.TextField(help_text='View Architecture (XML)')),
                ('arch_db', models.TextField(blank=True, help_text='Arch Stored in DB')),
                ('mode', models.CharField(choices=[('primary', 'Base view'), ('extension', 'Extension View')], default='primary', max_length=16)),
                ('active', models.BooleanField(default=True)),
                ('priority', models.IntegerField(default=16)),
                ('groups_id', models.CharField(blank=True, help_text='Groups', max_length=256)),
                ('inherit_id', models.ForeignKey(blank=True, help_text='Inherited View', null=True, on_delete=django.db.models.deletion.CASCADE, to='analytics.iruiview')),
            ],
            options={
                'db_table': 'ir_ui_view',
            },
        ),
        migrations.CreateModel(
            name='IrFilters',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('model_id', models.CharField(help_text='Model', max_length=64)),
                ('domain', models.TextField(help_text='Filter Domain')),
                ('context', models.TextField(default='{}', help_text='Context')),
                ('sort', models.TextField(default='[]', help_text='Sort Order')),
                ('is_default', models.BooleanField(default=False, help_text='Default Filter')),
                ('active', models.BooleanField(default=True)),
                ('action_id', models.IntegerField(blank=True, help_text='Action', null=True)),
                ('user_id', models.ForeignKey(blank=True, help_text='User (if personal)', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'ir_filters',
            },
        ),
        migrations.CreateModel(
            name='IrCron',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('active', models.BooleanField(default=True)),
                ('interval_number', models.IntegerField(default=1, help_text='Repeat Every')),
                ('interval_type', models.CharField(choices=[('minutes', 'Minutes'), ('hours', 'Hours'), ('days', 'Days'), ('weeks', 'Weeks'), ('months', 'Months')], default='months', max_length=16)),
                ('numbercall', models.IntegerField(default=-1, help_text='Number of Calls (-1 = unlimited)')),
                ('doall', models.BooleanField(default=True, help_text='Repeat Missed')),
                ('model_id', models.CharField(help_text='Model', max_length=64)),
                ('function', models.CharField(help_text='Function', max_length=64)),
                ('args', models.TextField(default='()', help_text='Arguments')),
                ('nextcall', models.DateTimeField(default=datetime.datetime.now, help_text='Next Execution Date')),
                ('lastcall', models.DateTimeField(blank=True, help_text='Last Execution Date', null=True)),
                ('priority', models.IntegerField(default=5, help_text='Priority')),
                ('user_id', models.ForeignKey(help_text='User', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'ir_cron',
            },
        ),
        migrations.CreateModel(
            name='IrActWindow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('type', models.CharField(default='ir.actions.act_window', max_length=32)),
                ('res_model', models.CharField(help_text='Model', max_length=64)),
                ('view_mode', models.CharField(default='tree,form', help_text='View Mode', max_length=256)),
                ('domain', models.TextField(default='[]', help_text='Domain')),
                ('context', models.TextField(default='{}', help_text='Context')),
                ('target', models.CharField(choices=[('current', 'Current Window'), ('new', 'New Window'), ('inline', 'Inline')], default='current', max_length=16)),
                ('limit', models.IntegerField(default=80, help_text='Record Limit')),
                ('auto_search', models.BooleanField(default=True, help_text='Auto Search')),
                ('view_id', models.ForeignKey(blank=True, help_text='View', null=True, on_delete=django.db.models.deletion.SET_NULL, to='analytics.iruiview')),
            ],
            options={
                'db_table': 'ir_act_window',
            },
        ),
        migrations.CreateModel(
            name='BaseImport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('res_model', models.CharField(help_text='Model', max_length=64)),
                ('file', models.FileField(blank=True, help_text='File', null=True, upload_to='imports/')),
                ('file_name', models.CharField(blank=True, help_text='File Name', max_length=256)),
                ('file_type', models.CharField(choices=[('csv', 'CSV'), ('xls', 'XLS'), ('xlsx', 'XLSX')], default='csv', max_length=16)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('cancel', 'Cancelled'), ('done', 'Done')], default='draft', max_length=16)),
                ('import_messages', models.TextField(blank=True, help_text='Import Messages')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('create_uid', models.ForeignKey(help_text='Created by', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'base_import',
            },
        ),
        migrations.CreateModel(
            name='AnalyticsReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('type', models.CharField(choices=[('sales_report', 'Sales Report'), ('financial_report', 'Financial Report'), ('hr_report', 'HR Report'), ('inventory_report', 'Inventory Report'), ('project_report', 'Project Report'), ('crm_report', 'CRM Report'), ('custom_report', 'Custom Report')], default='custom_report', max_length=32)),
                ('active', models.BooleanField(default=True)),
                ('model_id', models.CharField(help_text='Source Model', max_length=64)),
                ('domain', models.TextField(default='[]', help_text='Filter Domain')),
                ('fields', models.TextField(default='[]', help_text='Report Fields (JSON)')),
                ('groupby', models.TextField(default='[]', help_text='Group By Fields (JSON)')),
                ('output_format', models.CharField(choices=[('pdf', 'PDF'), ('xlsx', 'Excel'), ('csv', 'CSV'), ('html', 'HTML')], default='pdf', max_length=16)),
                ('auto_generate', models.BooleanField(default=False, help_text='Auto Generate')),
                ('public', models.BooleanField(default=False, help_text='Public Report')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('schedule_cron_id', models.ForeignKey(blank=True, help_text='Scheduled Action', null=True, on_delete=django.db.models.deletion.SET_NULL, to='analytics.ircron')),
                ('user_ids', models.ManyToManyField(blank=True, help_text='Allowed Users', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'analytics_report',
            },
        ),
        migrations.CreateModel(
            name='AnalyticsKpi',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('type', models.CharField(choices=[('revenue', 'Revenue'), ('profit', 'Profit'), ('sales_count', 'Sales Count'), ('customer_count', 'Customer Count'), ('employee_count', 'Employee Count'), ('project_count', 'Project Count'), ('task_completion', 'Task Completion Rate'), ('inventory_turnover', 'Inventory Turnover'), ('custom', 'Custom KPI')], default='custom', max_length=32)),
                ('active', models.BooleanField(default=True)),
                ('current_value', models.DecimalField(decimal_places=2, default=0, help_text='Current Value', max_digits=16)),
                ('target_value', models.DecimalField(decimal_places=2, default=0, help_text='Target Value', max_digits=16)),
                ('previous_value', models.DecimalField(decimal_places=2, default=0, help_text='Previous Period Value', max_digits=16)),
                ('period', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly')], default='monthly', max_length=16)),
                ('model_id', models.CharField(blank=True, help_text='Source Model', max_length=64)),
                ('field_name', models.CharField(blank=True, help_text='Field Name', max_length=64)),
                ('domain', models.TextField(default='[]', help_text='Filter Domain')),
                ('suffix', models.CharField(blank=True, help_text='Value Suffix (%, $, etc.)', max_length=16)),
                ('color', models.CharField(choices=[('green', 'Green'), ('yellow', 'Yellow'), ('red', 'Red')], default='green', max_length=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('last_update', models.DateTimeField(blank=True, help_text='Last Update', null=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('dashboard_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='kpi_ids', to='analytics.analyticsdashboard')),
            ],
            options={
                'db_table': 'analytics_kpi',
            },
        ),
    ]
