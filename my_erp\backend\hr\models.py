"""
HR Module Models - Complete Odoo HR Management
Based on Odoo's HR Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime, timedelta

# Import accounting models for integration
from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, ResCountry, ResCountryState
)


class HrDepartment(models.Model):
    """HR Department - Based on Odoo hr.department"""

    name = models.CharField(max_length=128)
    complete_name = models.CharField(max_length=256, blank=True)
    active = models.BooleanField(default=True)

    # Hierarchy
    parent_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='child_departments', help_text="Parent Department")

    # Management
    manager_id = models.ForeignKey('HrEmployee', null=True, blank=True, on_delete=models.SET_NULL, related_name='managed_departments', help_text="Department Manager")

    # Settings
    color = models.IntegerField(default=0, help_text="Color Index")
    note = models.TextField(blank=True)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_department'

    def __str__(self):
        return self.complete_name or self.name

    def save(self, *args, **kwargs):
        # Compute complete name
        if self.parent_id:
            self.complete_name = f"{self.parent_id.complete_name} / {self.name}"
        else:
            self.complete_name = self.name
        super().save(*args, **kwargs)


class HrJob(models.Model):
    """Job Position - Based on Odoo hr.job"""

    STATES = [
        ('recruit', 'Recruitment in Progress'),
        ('open', 'Not Recruiting'),
    ]

    name = models.CharField(max_length=128)
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10)

    # Department and reporting
    department_id = models.ForeignKey(HrDepartment, null=True, blank=True, on_delete=models.SET_NULL)

    # Job details
    description = models.TextField(blank=True, help_text="Job Description")
    requirements = models.TextField(blank=True, help_text="Job Requirements")

    # Recruitment
    state = models.CharField(max_length=16, choices=STATES, default='open')
    no_of_recruitment = models.IntegerField(default=1, help_text="Expected New Employees")
    no_of_hired_employee = models.IntegerField(default=0, help_text="Hired Employees")

    # Contract details
    contract_type_id = models.ForeignKey('HrContractType', null=True, blank=True, on_delete=models.SET_NULL)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_job'

    def __str__(self):
        return self.name

    @property
    def no_of_employee(self):
        """Current number of employees in this job"""
        return self.employee_ids.filter(active=True).count()


class HrEmployee(models.Model):
    """Employee - Based on Odoo hr.employee"""

    GENDERS = [
        ('male', 'Male'),
        ('female', 'Female'),
        ('other', 'Other'),
    ]

    MARITAL_STATUS = [
        ('single', 'Single'),
        ('married', 'Married'),
        ('cohabitant', 'Legal Cohabitant'),
        ('widower', 'Widower'),
        ('divorced', 'Divorced'),
    ]

    # Basic Information
    name = models.CharField(max_length=128)
    active = models.BooleanField(default=True)

    # Personal Information
    private_name = models.CharField(max_length=128, blank=True, help_text="Private Name")
    gender = models.CharField(max_length=8, choices=GENDERS, blank=True)
    marital = models.CharField(max_length=16, choices=MARITAL_STATUS, blank=True)
    spouse_complete_name = models.CharField(max_length=128, blank=True)
    spouse_birthdate = models.DateField(null=True, blank=True)
    children = models.IntegerField(default=0)

    # Dates
    birthday = models.DateField(null=True, blank=True)

    # Contact Information
    private_street = models.CharField(max_length=128, blank=True)
    private_street2 = models.CharField(max_length=128, blank=True)
    private_city = models.CharField(max_length=64, blank=True)
    private_state_id = models.ForeignKey(ResCountryState, null=True, blank=True, on_delete=models.SET_NULL, related_name='employee_private_state')
    private_zip = models.CharField(max_length=16, blank=True)
    private_country_id = models.ForeignKey(ResCountry, null=True, blank=True, on_delete=models.SET_NULL, related_name='employee_private_country')
    private_phone = models.CharField(max_length=32, blank=True)
    private_email = models.EmailField(blank=True)

    # Work Information
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)
    department_id = models.ForeignKey(HrDepartment, null=True, blank=True, on_delete=models.SET_NULL, related_name='employee_ids')
    job_id = models.ForeignKey(HrJob, null=True, blank=True, on_delete=models.SET_NULL, related_name='employee_ids')
    parent_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL, related_name='child_ids', help_text="Manager")
    coach_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL, related_name='coached_employees', help_text="Coach")

    # Work Contact
    work_phone = models.CharField(max_length=32, blank=True)
    work_email = models.EmailField(blank=True)
    work_location = models.CharField(max_length=128, blank=True)

    # Employment
    employee_type = models.CharField(max_length=16, choices=[('employee', 'Employee'), ('student', 'Student'), ('trainee', 'Trainee'), ('contractor', 'Contractor'), ('freelance', 'Freelancer')], default='employee')

    # Identification
    identification_id = models.CharField(max_length=32, blank=True, help_text="Identification No")
    passport_id = models.CharField(max_length=32, blank=True, help_text="Passport No")
    bank_account_id = models.CharField(max_length=64, blank=True, help_text="Bank Account Number")

    # System Integration
    user_id = models.OneToOneField(User, null=True, blank=True, on_delete=models.SET_NULL, related_name='employee')
    address_home_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL, related_name='employee_home')

    # HR Settings
    pin = models.CharField(max_length=16, blank=True, help_text="PIN Code")
    barcode = models.CharField(max_length=128, blank=True, help_text="Badge ID")

    # Additional Info
    notes = models.TextField(blank=True)
    color = models.IntegerField(default=0)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_employee'

    def __str__(self):
        return self.name

    @property
    def age(self):
        """Calculate employee age"""
        if self.birthday:
            today = date.today()
            return today.year - self.birthday.year - ((today.month, today.day) < (self.birthday.month, self.birthday.day))
        return None

    def get_current_contract(self):
        """Get current active contract"""
        return self.contract_ids.filter(
            state='open',
            date_start__lte=date.today()
        ).filter(
            models.Q(date_end__isnull=True) | models.Q(date_end__gte=date.today())
        ).first()


class HrContractType(models.Model):
    """Contract Type - Based on Odoo hr.contract.type"""

    name = models.CharField(max_length=64)
    sequence = models.IntegerField(default=10)

    class Meta:
        db_table = 'hr_contract_type'

    def __str__(self):
        return self.name


class HrContract(models.Model):
    """Employee Contract - Based on Odoo hr.contract"""

    STATES = [
        ('draft', 'New'),
        ('open', 'Running'),
        ('pending', 'To Renew'),
        ('close', 'Expired'),
        ('cancel', 'Cancelled'),
    ]

    name = models.CharField(max_length=128)
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10)

    # Employee
    employee_id = models.ForeignKey(HrEmployee, on_delete=models.CASCADE, related_name='contract_ids')
    department_id = models.ForeignKey(HrDepartment, null=True, blank=True, on_delete=models.SET_NULL)
    job_id = models.ForeignKey(HrJob, null=True, blank=True, on_delete=models.SET_NULL)

    # Contract Details
    type_id = models.ForeignKey(HrContractType, null=True, blank=True, on_delete=models.SET_NULL)
    state = models.CharField(max_length=16, choices=STATES, default='draft')

    # Dates
    date_start = models.DateField()
    date_end = models.DateField(null=True, blank=True)

    # Salary
    wage = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Basic Wage")
    currency_id = models.CharField(max_length=3, default='PKR')

    # Work Schedule
    resource_calendar_id = models.IntegerField(null=True, blank=True, help_text="Working Schedule")
    working_hours = models.CharField(max_length=64, blank=True, help_text="Working Schedule")

    # Trial Period
    trial_date_end = models.DateField(null=True, blank=True, help_text="End of Trial Period")

    # Notes
    notes = models.TextField(blank=True)

    # Accounting Integration
    analytic_account_id = models.IntegerField(null=True, blank=True, help_text="Analytic Account")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_contract'

    def __str__(self):
        return f"{self.employee_id.name} - {self.name}"

    def action_start(self):
        """Start the contract"""
        if self.state == 'draft':
            self.state = 'open'
            self.save()
            return True
        return False

    def action_close(self):
        """Close the contract"""
        if self.state == 'open':
            self.state = 'close'
            self.save()
            return True
        return False


class HrLeaveType(models.Model):
    """Leave Type - Based on Odoo hr.leave.type"""

    ALLOCATION_TYPES = [
        ('no', 'No Allocation'),
        ('fixed', 'Fixed Allocation'),
        ('fixed_allocation', 'Fixed by HR'),
    ]

    VALIDITY_TYPES = [
        ('forever', 'Forever'),
        ('year', 'Year'),
    ]

    name = models.CharField(max_length=64)
    sequence = models.IntegerField(default=100)
    active = models.BooleanField(default=True)

    # Leave Configuration
    allocation_type = models.CharField(max_length=16, choices=ALLOCATION_TYPES, default='fixed')
    validity_start = models.DateField(null=True, blank=True)
    validity_stop = models.DateField(null=True, blank=True)

    # Display
    color_name = models.CharField(max_length=32, blank=True, help_text="Color")
    color = models.IntegerField(default=0)

    # Limits
    max_leaves = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Maximum Allowed")
    leaves_taken = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Leaves Already Taken")

    # Settings
    double_validation = models.BooleanField(default=False, help_text="Apply Double Validation")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'hr_leave_type'

    def __str__(self):
        return self.name


class HrLeave(models.Model):
    """Leave Request - Based on Odoo hr.leave"""

    STATES = [
        ('draft', 'To Submit'),
        ('confirm', 'To Approve'),
        ('refuse', 'Refused'),
        ('validate1', 'Second Approval'),
        ('validate', 'Approved'),
        ('cancel', 'Cancelled'),
    ]

    REQUEST_TYPES = [
        ('remove', 'Leave Request'),
        ('add', 'Allocation Request'),
    ]

    name = models.CharField(max_length=64, blank=True)
    state = models.CharField(max_length=16, choices=STATES, default='confirm')
    request_type = models.CharField(max_length=8, choices=REQUEST_TYPES, default='remove')

    # Employee and Leave Type
    employee_id = models.ForeignKey(HrEmployee, on_delete=models.CASCADE, related_name='leave_ids')
    holiday_status_id = models.ForeignKey(HrLeaveType, on_delete=models.CASCADE, help_text="Leave Type")

    # Dates
    date_from = models.DateTimeField(help_text="Start Date")
    date_to = models.DateTimeField(help_text="End Date")
    request_date_from = models.DateField(help_text="Request Start Date")
    request_date_to = models.DateField(help_text="Request End Date")

    # Duration
    number_of_days = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Number of Days")
    number_of_hours_display = models.CharField(max_length=16, blank=True, help_text="Duration in Hours")

    # Approval
    user_id = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, help_text="Responsible User")
    manager_id = models.ForeignKey(HrEmployee, null=True, blank=True, on_delete=models.SET_NULL, related_name='managed_leaves', help_text="Manager")

    # Details
    notes = models.TextField(blank=True, help_text="Reason")

    # System
    department_id = models.ForeignKey(HrDepartment, null=True, blank=True, on_delete=models.SET_NULL)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_leave'

    def __str__(self):
        return f"{self.employee_id.name} - {self.holiday_status_id.name}"

    def action_approve(self):
        """Approve the leave request"""
        if self.state == 'confirm':
            if self.holiday_status_id.double_validation:
                self.state = 'validate1'
            else:
                self.state = 'validate'
            self.save()
            return True
        elif self.state == 'validate1':
            self.state = 'validate'
            self.save()
            return True
        return False

    def action_refuse(self):
        """Refuse the leave request"""
        if self.state in ['confirm', 'validate1']:
            self.state = 'refuse'
            self.save()
            return True
        return False

    def action_draft(self):
        """Reset to draft"""
        if self.state in ['refuse', 'cancel']:
            self.state = 'draft'
            self.save()
            return True
        return False


class HrAttendance(models.Model):
    """Employee Attendance - Based on Odoo hr.attendance"""

    employee_id = models.ForeignKey(HrEmployee, on_delete=models.CASCADE, related_name='attendance_ids')
    check_in = models.DateTimeField(help_text="Check In")
    check_out = models.DateTimeField(null=True, blank=True, help_text="Check Out")

    # Calculated fields
    worked_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Worked Hours")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_attendance'
        ordering = ['-check_in']

    def __str__(self):
        return f"{self.employee_id.name} - {self.check_in.strftime('%Y-%m-%d %H:%M')}"

    def save(self, *args, **kwargs):
        # Calculate worked hours
        if self.check_in and self.check_out:
            duration = self.check_out - self.check_in
            self.worked_hours = duration.total_seconds() / 3600
        super().save(*args, **kwargs)


class HrPayslip(models.Model):
    """Employee Payslip - Based on Odoo hr.payslip"""

    STATES = [
        ('draft', 'Draft'),
        ('verify', 'Waiting'),
        ('done', 'Done'),
        ('cancel', 'Rejected'),
    ]

    name = models.CharField(max_length=64)
    number = models.CharField(max_length=64, blank=True)
    employee_id = models.ForeignKey(HrEmployee, on_delete=models.CASCADE, related_name='slip_ids')

    # Dates
    date_from = models.DateField(help_text="Date From")
    date_to = models.DateField(help_text="Date To")

    # State
    state = models.CharField(max_length=16, choices=STATES, default='draft')

    # Contract
    contract_id = models.ForeignKey(HrContract, null=True, blank=True, on_delete=models.SET_NULL)

    # Amounts
    basic_wage = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    net_wage = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Details
    note = models.TextField(blank=True)

    # Accounting Integration
    move_id = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.SET_NULL, help_text="Accounting Entry")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_payslip'

    def __str__(self):
        return f"{self.employee_id.name} - {self.name}"

    def action_payslip_done(self):
        """Confirm the payslip"""
        if self.state == 'verify':
            self.state = 'done'
            self.save()

            # Create accounting entry
            self._create_accounting_entry()
            return True
        return False

    def compute_sheet(self):
        """Compute payslip amounts"""
        if self.contract_id:
            self.basic_wage = self.contract_id.wage
            # Add calculations for allowances, deductions, etc.
            self.net_wage = self.basic_wage  # Simplified calculation
            self.save()

    def _create_accounting_entry(self):
        """Create accounting entry for payslip"""
        if self.move_id:
            return  # Already created

        # Create journal entry for salary payment
        move = AccountMove.objects.create(
            journal_id=AccountJournal.objects.filter(type='general', company_id=self.company_id).first(),
            date=self.date_to,
            ref=f"Payslip: {self.name}",
            company_id=self.company_id,
        )

        # Salary expense line
        AccountMoveLine.objects.create(
            move_id=move,
            name=f"Salary: {self.employee_id.name}",
            account_id=AccountAccount.objects.filter(account_type='expense', company_id=self.company_id).first(),
            debit=self.net_wage,
            credit=0,
            company_id=self.company_id,
        )

        # Salary payable line
        AccountMoveLine.objects.create(
            move_id=move,
            name=f"Salary Payable: {self.employee_id.name}",
            account_id=AccountAccount.objects.filter(account_type='liability_current', company_id=self.company_id).first(),
            debit=0,
            credit=self.net_wage,
            company_id=self.company_id,
        )

        self.move_id = move
        self.save()
