# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_financial_report
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-05-14 11:23+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.10.4\n"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "&gt; 120 d."
msgstr "&gt; 120 d."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "1 - 30 d."
msgstr "1 - 30 d."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
msgid "10"
msgstr "10"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "31 - 60 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "61 - 90 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "91 - 120 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "<b>Taxes summary</b>"
msgstr "<b>Vergi Özeti</b>"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid ""
"<i class=\"fa fa-exclamation-triangle mr-3\"/>\n"
"                    Duplicate amounts may be shown because more than one "
"analytical account may be defined in the journal items."
msgstr ""
"<i class=\"fa fa-exclamation-triangle mr-3\"/>\n"
"                    Yevmiye kalemlerinde birden fazla analitik hesap "
"tanımlanmış olabileceğinden dolayı mükerrer tutarlar gösterilebilir."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Intervals configuration</span>"
msgstr "<span class=\"o_form_label\">Aralıklar yapılandırması</span>"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid "<span class=\"oe_inline\">To</span>"
msgstr "<span class=\"oe_inline\">Bitiş</span>"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_abstract_report
msgid "Abstract Report"
msgstr "Temel Rapor"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_financial_report_abstract_wizard
msgid "Abstract Wizard"
msgstr "Temel Sihirbaz"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_abstract_report_xlsx
msgid "Abstract XLSX Account Financial Report"
msgstr "Temel XLSX Mali Hesap Raporu"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model,name:account_financial_report.model_account_account
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Account"
msgstr "Hesap"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__account_age_report_config_id
msgid "Account Age Report Config"
msgstr "Hesap Yaş Raporu Yapılandırması"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_code_from
msgid "Account Code From"
msgstr "Hesap Kodu"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_code_to
msgid "Account Code To"
msgstr "Hesap Kodu İle"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_group
msgid "Account Group"
msgstr "Hesap Grubu"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Account Name"
msgstr "Hesap Adı"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Account at 0 filter"
msgstr "0 filtresindeki hesap"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
msgid "Account balance at 0 filter"
msgstr "0 Filtrelenen Hesap Bakiyesi"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__account_ids
msgid "Accounts"
msgstr "Hesaplar"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__centralize
msgid "Activate centralization"
msgstr "Merkezileştirmeyi Etkinleştir"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Additional Filtering"
msgstr "Ek Filtreleme"

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.action_aged_partner_report_configuration
msgid "Age Partner Report Configuration"
msgstr "Yaşlandırılmış İş Ortağı Raporu Yapılandırması"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 120\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 120 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 30\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 30 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 60\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 60 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 90\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 90 d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_aged_partner_balance_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_aged_partner_balance_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_aged_partner_balance_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_aged_partner_balance_wizard
msgid "Aged Partner Balance"
msgstr "Yaşlandırılmış İş Ortağı Bakiyesi"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_base
msgid "Aged Partner Balance -"
msgstr "Yaşlandırılmış İş Ortağı Bakiyesi -"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_aged_partner_balance
msgid "Aged Partner Balance Report"
msgstr "Yaşlandırılmış İş Ortağı Bakiyesi Raporu"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_aged_partner_balance_report_wizard
msgid "Aged Partner Balance Wizard"
msgstr "Yaşlandırılmış İş Ortağı Bakiyesi Raporu"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_aged_partner_balance_xlsx
msgid "Aged Partner Balance XLSL Report"
msgstr "Yaşlandırılmış İş Ortağı Bakiye XLSL Raporu"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_aged_partner_balance_xlsx
msgid "Aged Partner Balance XLSX"
msgstr "Yaşlandırılmış İş Ortağı Bakiye XLS"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "All"
msgstr "Hepsi"

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__aged_partner_balance_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__open_items_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__target_move__all
msgid "All Entries"
msgstr "Tüm Kayıtlar"

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__aged_partner_balance_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__open_items_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__target_move__posted
msgid "All Posted Entries"
msgstr "İşlenen Tüm Girişler"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "All entries"
msgstr "Tüm Kayıtlar"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "All posted entries"
msgstr "Tüm onaylı kayıtlar"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Amount Cur."
msgstr "Döviz tutar."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Amount Currency"
msgstr "Tutar Para Birimi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Amount cur."
msgstr "Döviz tutar."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_move_line__analytic_account_ids
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__grouped_by__analytic_account
msgid "Analytic Account"
msgstr "Analitik Hesap"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Analytic Distribution"
msgstr "Analitik Dağılım"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Balance"
msgstr "Bakiye"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Base Amount"
msgstr "Matrah Tutarı"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Balance"
msgstr "Matrah Bakiyesi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Credit"
msgstr "Matrah Alacak"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Debit"
msgstr "Matrah Borç"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__based_on
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Based On"
msgstr "Esas"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Based on"
msgstr "Esas"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Cancel"
msgstr "İptal"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "Centralize filter"
msgstr "Merkezi Filtre"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_account__centralized
msgid "Centralized"
msgstr "Merkezileştirilmiş"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__group_child_ids
msgid "Child Groups"
msgstr "Alt Gruplar"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Code"
msgstr "Kod"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_account_financial_report_abstract_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__company_id
msgid "Company"
msgstr "Firma"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__compute_account_ids
msgid "Compute accounts"
msgstr "Grup hesapları"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "Configurations"
msgstr "Yapılandırmalar"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Credit"
msgstr "Alacak"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Cumul cur."
msgstr "Kümüle Bak."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Cumul. Bal."
msgstr "Cumul. Bal."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur."
msgstr "Döviz"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur. Original"
msgstr "Döviz Orjinal"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur. Residual"
msgstr "Döviz Kalan"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Currency"
msgstr "Para Birimi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid "Current"
msgstr "Güncel"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Date"
msgstr "Tarih"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__date_at
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__date_at
msgid "Date At"
msgstr "Tarihden"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_from
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Date From"
msgstr "Tarihinden"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_to
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_to
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Date To"
msgstr "Tarihine"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
msgid "Date at filter"
msgstr "Filtre Tarihi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Date from"
msgstr "Tarihinden"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_range_id
msgid "Date range"
msgstr "Tarih Aralığı"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Date range filter"
msgstr "Tarih Aralığı Filtre"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Date to"
msgstr "Tarihine"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Debit"
msgstr "Borç"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Description"
msgstr "Açıklama"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__tax_detail
msgid "Detail Taxes"
msgstr "Vergi Detayları"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__display_name
msgid "Display Name"
msgstr "Ad Görünümü"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__foreign_currency
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__foreign_currency
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__foreign_currency
msgid ""
"Display foreign currency for move lines, unless account currency is not "
"setup through chart of accounts will display initial and final balance in "
"that currency."
msgstr ""
"Hesap planı aracılığıyla hesap para birimi ayarlanmadığı sürece, hareket "
"satırları için yabancı para birimini görüntüleyin, ilk ve son bakiyeyi o "
"para biriminde görüntüleyecektir."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__hide_parent_hierarchy_level
msgid "Do not display parent levels"
msgstr "Üst düzeylerini gösterme"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Due\n"
"                        date"
msgstr ""
"Vade\n"
"                        Tarihi"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid ""
"Due\n"
"                    date"
msgstr ""
"Vade\n"
"                    Tarihi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
msgid "Due date"
msgstr "Vade Tarihi"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_to
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_to
msgid "End date"
msgstr "Bitiş Tarihi"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_ending_cumul
msgid ""
"Ending\n"
"                        balance"
msgstr ""
"Son\n"
"                        Bakiye"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_aged_partner_balance_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__account_code_to
msgid "Ending account in a range"
msgstr "Hesabı bir aralıkta sonlandırma"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Ending balance"
msgstr "Kapanış Bakiyesi"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid ""
"Ending balance\n"
"                        cur."
msgstr ""
"Kapanış döviz\n"
"                        bak."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Entries sorted by"
msgstr "Girişler Göre Sırala"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Entry"
msgstr "Yevmiye Girişi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Entry number"
msgstr "Giriş Numarası"

#. module: account_financial_report
#. odoo-javascript
#: code:addons/account_financial_report/static/src/xml/report.xml:0
msgid "Export"
msgstr "Dışa Aktar"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Export PDF"
msgstr "Dışa Aktar PDF"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Export XLSX"
msgstr "Dışa Aktar XLSX"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Filter accounts"
msgstr "Hesapları Filtrele"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Filter analytic accounts"
msgstr "Analitik Hesapları Filtreleme"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__cost_center_ids
msgid "Filter cost centers"
msgstr "Merkezi Maliyetleri Filtreleme"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_journal_ids
msgid "Filter journals"
msgstr "Yevmiyeleri Filtreleyin"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__partner_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Filter partners"
msgstr "İş Ortakları filtrele"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__foreign_currency
msgid "Foreign Currency"
msgstr "Yabancı Para Birimi"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid "From Code"
msgstr "Koddan"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "From:"
msgstr "İtibaren:"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "From: %(date_from)s To: %(date_to)s"
msgstr "Den: %(date_from)s To: %(date_to)s"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__complete_code
msgid "Full Code"
msgstr "Full Kod"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__complete_name
msgid "Full Name"
msgstr "Tam Ad"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__fy_start_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__fy_start_date
msgid "Fy Start Date"
msgstr "Mali Yıl Başlangıç Tarihi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.act_action_general_ledger_wizard_partner_relation
#: model:ir.actions.act_window,name:account_financial_report.action_general_ledger_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_general_ledger_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_general_ledger_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_general_ledger_wizard
msgid "General Ledger"
msgstr "Büyük Defter"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_base
msgid "General Ledger -"
msgstr "Büyük Defter -"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_general_ledger
msgid "General Ledger Report"
msgstr "Büyük Defter Raporu"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_general_ledger_report_wizard
msgid "General Ledger Report Wizard"
msgstr "Büyük Defter Rapor Sihirbazı"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_general_ledger_xlsx
msgid "General Ledger XLSL Report"
msgstr "Büyük Defter XLSL Raporu"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_general_ledger_xlsx
msgid "General Ledger XLSX"
msgstr "Büyük Defter XLSL"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid ""
"General Ledger can be computed only if selected company have\n"
"                        only one unaffected earnings account."
msgstr ""
"Büyük Defter yalnızca seçilen şirketin sahip olması durumunda "
"hesaplanabilir..\n"
"                        etkilenmeyen tek bir kazanç hesabı."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__group_option
msgid "Group entries by"
msgstr "Giriş gruplama"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__grouped_by
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__grouped_by
msgid "Grouped By"
msgstr "Gruplanma"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid ""
"Here you can set the intervals that will appear on the Aged Partner Balance."
msgstr "Burada İş Ortağı Bakiyesinde görünecek aralıkları ayarlayabilirsiniz."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Hide"
msgstr "Gizle"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__hide_account_at_0
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__hide_account_at_0
msgid "Hide account ending balance at 0"
msgstr "Hesap 0 biten bakiyeyi gizle"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__hide_account_at_0
msgid "Hide accounts at 0"
msgstr "0 Bakiye Hesapları Gizle"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_hierarchy_level
msgid "Hierarchy Levels to display"
msgstr "Görüntülenecek Hiyerarşi Düzeyleri"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__id
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__id
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_account_account__centralized
msgid ""
"If flagged, no details will be displayed in the General Ledger report (the "
"webkit one only), only centralized amounts per period."
msgstr ""
"İşaretlenirse Büyük Defter raporunda ayrıntılar gösterilmez, sadece "
"merkezileştirilmiş dönem tutarları gösterilir."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__inferior_limit
msgid "Inferior Limit"
msgstr "Alt Limit"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/models/account_age_report_configuration.py:0
msgid "Inferior Limit must be greather than zero"
msgstr "Alt Limit sıfırdan büyük olmalıdır"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid ""
"Initial\n"
"                        balance cur."
msgstr ""
"Başlangıç\n"
"                        bakiye döviz."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid ""
"Initial\n"
"                    balance"
msgstr ""
"Açılış\n"
"                    Bakiyesi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Initial balance"
msgstr "Açılış Bakiyesi"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__age_partner_config_id
#: model:ir.model.fields,field_description:account_financial_report.field_res_config_settings__age_partner_config_id
msgid "Intervals configuration"
msgstr "Aralıklar yapılandırması"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__journal_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Journal"
msgstr "Yevmiye"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_move_line
msgid "Journal Item"
msgstr "Yevmiye Kalemi"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__domain
msgid "Journal Items Domain"
msgstr "Yevmiye Öğeleri Alanı"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_journal_ledger_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_journal_ledger_wizard_html
#: model:ir.ui.menu,name:account_financial_report.menu_journal_ledger_wizard
msgid "Journal Ledger"
msgstr "Yevmiye Defteri"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_base
msgid "Journal Ledger -"
msgstr "Yevmiye Defteri -"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_journal_ledger
msgid "Journal Ledger Report"
msgstr "Yevmiye Defteri Raporu"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_journal_ledger_report_wizard
msgid "Journal Ledger Report Wizard"
msgstr "Yevmiye Defteri Rapor Sihirbazı"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_journal_ledger_xlsx
msgid "Journal Ledger XLSX"
msgstr "Yevmiye Defteri XLSX"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_journal_ledger_xlsx
msgid "Journal Ledger XLSX Report"
msgstr "Yevmiye Defteri XLSX Rapor"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__journal_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Journals"
msgstr "Yevmiyeler"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__level
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Level"
msgstr "Seviye"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "Level %s"
msgstr "Seviye %s"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__limit_hierarchy_level
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Limit hierarchy levels"
msgstr "Hiyerarşi düzeylerini sınırlayın"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__line_ids
msgid "Line"
msgstr "Satır"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger.py:0
#: code:addons/account_financial_report/report/open_items.py:0
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid "Missing Partner"
msgstr "Eksik İş Ortağı"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_age_report_configuration_line
msgid "Model to set interval lines for Age partner balance report"
msgstr "İş Ortağı Bakiye Raporu için aralık satırlarını ayarlamak için model"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_age_report_configuration
msgid "Model to set intervals for Age partner balance report"
msgstr "İş Ortağı Bakiye Raporu için aralıkları ayarlamak için model"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__move_target
msgid "Move Target"
msgstr "Hedef Harketler"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal
msgid "Moves"
msgstr "Hareketler"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/models/account_age_report_configuration.py:0
msgid "Must complete Configuration Lines"
msgstr "Yapılandırma Satırlarını Tamamlamalısınız"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__name
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__name
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Name"
msgstr "Adı"

#. module: account_financial_report
#: model:ir.model.constraint,message:account_financial_report.constraint_account_age_report_configuration_line_unique_name_config_combination
msgid "Name must be unique per report configuration"
msgstr "İsim rapor yapılandırması için benzersiz olmalıdır"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Net"
msgstr "Net"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "No"
msgstr "Yok"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "No group"
msgstr "Grupsuz"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "No limit"
msgstr "Limitsiz"

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__
msgid "None"
msgstr "Hiçbiri"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Not Posted"
msgstr "Onaylanmamış"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "Not due"
msgstr "Vadesi Gelmemiş"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "OCA Aged Report Configuration"
msgstr "OCA Yaşlandırılmış Rapor Yapılandırması"

#. module: account_financial_report
#: model:ir.ui.menu,name:account_financial_report.menu_oca_reports
msgid "OCA accounting reports"
msgstr "OCA Muhasebe Raporları"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid "Older"
msgstr "Daha Eski"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__only_one_unaffected_earnings_account
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__only_one_unaffected_earnings_account
msgid "Only One Unaffected Earnings Account"
msgstr "Sadece Bir Etkilenmemiş Net Kar Zarar Hesabı"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_open_items_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_open_items_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_open_items_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_open_items_wizard
msgid "Open Items"
msgstr "Açık Pozisyonlar"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_base
msgid "Open Items -"
msgstr "Açık Pozisyonlar -"

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.act_action_open_items_wizard_partner_relation
msgid "Open Items Partner"
msgstr "İş Ortağı Açık İşlemleri"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_open_items
msgid "Open Items Report"
msgstr "Açık Pozisyon Raporu"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_open_items_report_wizard
msgid "Open Items Report Wizard"
msgstr "Açık İşlemler Rapor Sihirbazı"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_open_items_xlsx
msgid "Open Items XLSX"
msgstr "Açık Pozisyon XLSX"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_open_items_xlsx
msgid "Open Items XLSX Report"
msgstr "Açık Pozisyon XLSX Raporu"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Options"
msgstr "Seçenekler"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Original"
msgstr "Orijinal"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Partner"
msgstr "İş Ortağı"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_partner_ending_cumul
msgid ""
"Partner\n"
"                    cumul aged balance"
msgstr ""
"İş ortağı\n"
"                    kümülatif yaşlandırılmış bakiyesi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
msgid "Partner Initial balance"
msgstr "İş Ortağı Başlangıç bakiyesi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Partner cumul aged balance"
msgstr "İş ortağı kümülatif yaşlandırılmış bakiyesi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_ending_cumul
msgid "Partner ending balance"
msgstr "İş ortağı kapanış bakiyesi"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Partner initial balance"
msgstr "Ortak başlangıç bakiyesi"

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__partners
msgid "Partners"
msgstr "İş Ortakları"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__payable_accounts_only
msgid "Payable Accounts Only"
msgstr "Sadece Borç Hesapları"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_account_ending_cumul
msgid "Percents"
msgstr "Yüzdeler"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Period balance"
msgstr "Dönem Bakiyesi"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Periods"
msgstr "Dönemler"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Posted"
msgstr "İşlenmiş"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Rec."
msgstr "Uzl."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__receivable_accounts_only
msgid "Receivable Accounts Only"
msgstr "Sadece Alacak Hesapları"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid ""
"Ref -\n"
"                        Label"
msgstr ""
"Ref -\n"
"                        Etiketi"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid ""
"Ref -\n"
"                    Label"
msgstr ""
"Ref -\n"
"                    Etiketi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Ref - Label"
msgstr "Ref - Etiketi"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_ir_actions_report
msgid "Report Action"
msgstr "Rapor İşlemi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Residual"
msgstr "Kalan"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Sequence"
msgstr "Sıra"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Show"
msgstr "Göster"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__show_cost_center
msgid "Show Analytic Account"
msgstr "Analitik Hesabı Göster"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__with_auto_sequence
msgid "Show Auto Sequence"
msgstr "Otomatik Sıralamayı Göster"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__show_move_line_details
msgid "Show Move Line Details"
msgstr "Hareket Satırı Ayrıntılarını Göster"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__show_partner_details
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_partner_details
msgid "Show Partner Details"
msgstr "İş Ortağı Ayrıntılarını Göster"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__foreign_currency
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__foreign_currency
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__foreign_currency
msgid "Show foreign currency"
msgstr "Yabancı Para Birimini Göster"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_hierarchy
msgid "Show hierarchy"
msgstr "Hiyerarşiyi Göster"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__sort_option
msgid "Sort entries by"
msgstr "Girişler Göre Sırala"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_from
msgid "Start Date"
msgstr "Başlama Tarihi"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_from
msgid "Start date"
msgstr "Başlama Tarihi"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_aged_partner_balance_report_wizard__account_code_from
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__account_code_from
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__account_code_from
msgid "Starting account in a range"
msgstr "Hesap kod aralığı başlangıcı"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "TOTAL"
msgstr "TOPLAM"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Tags"
msgstr "Etiketler"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__target_move
msgid "Target Moves"
msgstr "Hedef Harketler"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Target moves filter"
msgstr "Hedef Hareket Filtresi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Tax"
msgstr "Vergi"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Tax Amount"
msgstr "Vergi Tutarı"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Balance"
msgstr "Vergi Bakiyesi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Credit"
msgstr "Vergi İndirimi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Debit"
msgstr "Vergi Borcu"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__based_on__taxgroups
msgid "Tax Groups"
msgstr "Vergi Grupları"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
msgid "Tax Initial balance"
msgstr "Vergi Açılış Bakiyesi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__based_on__taxtags
msgid "Tax Tags"
msgstr "Vergi Etiketleri"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
msgid "Tax ending balance"
msgstr "Vergi Kapanş Bakiyesi"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Tax initial balance"
msgstr "Vergi Açılış Bakiyesi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Taxes"
msgstr "Vergiler"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/general_ledger_wizard.py:0
msgid ""
"The Company in the General Ledger Report Wizard and in Date Range must be "
"the same."
msgstr ""
"Büyük Defter Rapor Sihirbazı ve Tarih Aralığının şirketi aynı olmalıdır."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/trial_balance_wizard.py:0
msgid ""
"The Company in the Trial Balance Report Wizard and in Date Range must be the "
"same."
msgstr ""
"Geçici Mizan Rapor Sihirbazı ve Tarih Aralığının şirketi aynı olmalıdır."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/vat_report_wizard.py:0
msgid ""
"The Company in the Vat Report Wizard and in Date Range must be the same."
msgstr "Vergi Rapor Sihirbazı ve Tarih Aralığının şirketi aynı olmalıdır."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/trial_balance_wizard.py:0
msgid "The hierarchy level to filter on must be greater than 0."
msgstr "Filtrelenecek Hiyerarşi seviyesi sıfırdan büyük olmalıdır."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid ""
"There is a problem in the structure of the account groups. You may need to "
"create some child group of %s."
msgstr ""
"Hesap grupları yapısında bir sorun var. %s'nin bazı alt gruplarını "
"oluşturmanız gerekebilir."

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__domain
msgid "This domain will be used to select specific domain for Journal Items"
msgstr ""
"Bu alan adı Yevmiye Kalemleri için özel bir alan adı seçmek için "
"kullanılacaktır"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "To"
msgstr "Bitiş"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "To:"
msgstr "Bitiş:"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_account_ending_cumul
msgid "Total"
msgstr "Toplam"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_trial_balance_wizard
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_html
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_trial_balance_wizard
msgid "Trial Balance"
msgstr "Geçici Mizan"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_base
msgid "Trial Balance -"
msgstr "Geçici Mizan -"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_trial_balance
msgid "Trial Balance Report"
msgstr "Geçici Mizan Raporu"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_trial_balance_report_wizard
msgid "Trial Balance Report Wizard"
msgstr "Mizan Raporu Sihirbazı"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_xlsx
msgid "Trial Balance XLSX"
msgstr "Geçici Mizan XLSX"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_trial_balance_xlsx
msgid "Trial Balance XLSX Report"
msgstr "Geçici Mizan XLSX Raporu"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid ""
"Trial Balance can be computed only if selected company have only\n"
"                        one unaffected earnings account."
msgstr ""
"Mizan, Bakiyesi yalnızca seçilen şirketin yalnızca\n"
"                         etkilenmemiş bir kazanı hesabı."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__unaffected_earnings_account
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__unaffected_earnings_account
msgid "Unaffected Earnings Account"
msgstr "Etkilenmeyen Kazanç Hesabı"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__hide_account_at_0
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__hide_account_at_0
msgid ""
"Use this filter to hide an account or a partner with an ending balance at 0. "
"If partners are filtered, debits and credits totals will not match the trial "
"balance."
msgstr ""
"Bu filtreyi başlangıç bakiyesi 0 olan iş ortaklarını gizlemek için kullanın. "
"Eğer iş ortakları filtrelenirse, borç alacak toplamları geçici mizanla "
"tutmayacaktır."

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__show_hierarchy
msgid "Use when your account groups are hierarchical"
msgstr "Hesap gruplarınız hiyerarşik ise kullanın"

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.action_vat_report_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_vat_report_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_vat_report_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_vat_report_wizard
msgid "VAT Report"
msgstr "KDV Raporu"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "VAT Report -"
msgstr "KDV Raporu -"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "VAT Report Options"
msgstr "KDV Raporu Se??enekleri"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_vat_report_wizard
msgid "VAT Report Wizard"
msgstr "KDV Raporu Sihirbazı"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_vat_report_xlsx
msgid "VAT Report XLSX"
msgstr "KDV Raporu XLSX"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Vat Report"
msgstr "KDV Raporu"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_vat_report
msgid "Vat Report Report"
msgstr "KDV Raporu Raporu"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_vat_report_xlsx
msgid "Vat Report XLSX Report"
msgstr "KDV Raporu XLSX Raporu"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "View"
msgstr "Görünüm"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__hide_account_at_0
msgid ""
"When this option is enabled, the trial balance will not display accounts "
"that have initial balance = debit = credit = end balance = 0"
msgstr ""
"Bu seçenek açılırsa, geçici mizan başlangıç bakiyesi = borç = alacak = bitiş "
"bakiyesi = 0 olan hesapları göstermeyecektir"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__with_account_name
msgid "With Account Name"
msgstr "Hesap Adıyla"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid "Without analytic account"
msgstr "Analitik Hesap olmadan"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "Yes"
msgstr "Evet"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger.py:0
msgid "future"
msgstr "gelecek"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "or"
msgstr "veya"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_print_journal_ledger_wizard_qweb
msgid "ournal Ledger"
msgstr "Yevmiye Defteri"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal
msgid "to"
msgstr "-"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 16.21%;"
msgstr "genişlik: 16.21%;"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 23.24%;"
msgstr "genişlik: 23.24%;"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 23.78%;"
msgstr "genişlik: 23.78%;"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 31.35%;"
msgstr "enişlik: 31.35%;"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 38.92%;"
msgstr "genişlik: 38.92%;"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 8.11%;"
msgstr "genişlik: 8.11%;"

#~ msgid "Last Modified on"
#~ msgstr "Son D??zenleme"
