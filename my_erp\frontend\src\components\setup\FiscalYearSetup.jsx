/**
 * Fiscal Year Setup Component
 */
import React from 'react';
import { Card, Typography, Alert } from 'antd';
import { CalendarOutlined } from '@ant-design/icons';

const { Title } = Typography;

const FiscalYearSetup = () => {
  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <CalendarOutlined style={{ marginRight: 8 }} />
        Fiscal Year Setup
      </Title>
      
      <Card>
        <Alert
          message="Coming Soon"
          description="Fiscal Year setup will be available soon."
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
};

export default FiscalYearSetup;
