/**
 * Notification Center Component - Professional ERP Notifications
 * Following Odoo's notification system
 */
import React, { useState, useEffect } from 'react';
import {
  Badge,
  Button,
  Dropdown,
  List,
  Typography,
  Space,
  Avatar,
  Divider,
  Empty,
  Spin,
  Tag
} from 'antd';
import {
  BellOutlined,
  CheckOutlined,
  DeleteOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useAuth } from '../auth/AuthProvider';

const { Text } = Typography;

const NotificationCenter = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const { user } = useAuth();

  // Sample notifications data
  useEffect(() => {
    // Simulate loading notifications
    setLoading(true);
    setTimeout(() => {
      const sampleNotifications = [
        {
          id: 1,
          type: 'success',
          title: 'Invoice Payment Received',
          message: 'Payment of $5,000 received from ABC Corp for Invoice INV/2025/001',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          read: false,
          priority: 'normal',
          module: 'accounting'
        },
        {
          id: 2,
          type: 'warning',
          title: 'Low Stock Alert',
          message: 'Product "Office Chair" is running low in stock (5 units remaining)',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
          read: false,
          priority: 'high',
          module: 'inventory'
        },
        {
          id: 3,
          type: 'info',
          title: 'New Sales Order',
          message: 'Sales Order SO/2025/001 has been confirmed by customer',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
          read: true,
          priority: 'normal',
          module: 'sales'
        },
        {
          id: 4,
          type: 'error',
          title: 'Failed Bank Reconciliation',
          message: 'Bank reconciliation failed for account "Main Bank Account"',
          timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
          read: false,
          priority: 'high',
          module: 'accounting'
        },
        {
          id: 5,
          type: 'info',
          title: 'Expense Report Submitted',
          message: 'John Doe submitted expense report for approval',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
          read: true,
          priority: 'normal',
          module: 'expenses'
        }
      ];
      
      setNotifications(sampleNotifications);
      setUnreadCount(sampleNotifications.filter(n => !n.read).length);
      setLoading(false);
    }, 1000);
  }, []);

  const getNotificationIcon = (type) => {
    const iconProps = { style: { fontSize: '16px' } };
    switch (type) {
      case 'success':
        return <CheckCircleOutlined {...iconProps} style={{ color: '#52c41a' }} />;
      case 'warning':
        return <WarningOutlined {...iconProps} style={{ color: '#faad14' }} />;
      case 'error':
        return <CloseCircleOutlined {...iconProps} style={{ color: '#ff4d4f' }} />;
      default:
        return <InfoCircleOutlined {...iconProps} style={{ color: '#1890ff' }} />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return '#ff4d4f';
      case 'medium':
        return '#faad14';
      default:
        return '#52c41a';
    }
  };

  const getModuleColor = (module) => {
    const colors = {
      accounting: '#1890ff',
      sales: '#52c41a',
      purchase: '#fa8c16',
      inventory: '#722ed1',
      hr: '#eb2f96',
      expenses: '#ff7a45'
    };
    return colors[module] || '#8c8c8c';
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return `${days}d ago`;
    }
  };

  const handleMarkAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const handleMarkAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
    setUnreadCount(0);
  };

  const handleDeleteNotification = (notificationId) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== notificationId)
    );
    const notification = notifications.find(n => n.id === notificationId);
    if (notification && !notification.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };

  const handleClearAll = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  const notificationMenu = (
    <div style={{ width: 400, maxHeight: 500, overflow: 'hidden' }}>
      {/* Header */}
      <div style={{ 
        padding: '12px 16px', 
        borderBottom: '1px solid #f0f0f0',
        background: '#fafafa'
      }}>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Text strong>Notifications</Text>
          <Space size="small">
            {unreadCount > 0 && (
              <Button 
                type="link" 
                size="small"
                onClick={handleMarkAllAsRead}
              >
                Mark all read
              </Button>
            )}
            <Button 
              type="text" 
              size="small" 
              icon={<SettingOutlined />}
            />
          </Space>
        </Space>
      </div>

      {/* Notifications List */}
      <div style={{ maxHeight: 400, overflowY: 'auto' }}>
        {loading ? (
          <div style={{ padding: '40px', textAlign: 'center' }}>
            <Spin />
          </div>
        ) : notifications.length === 0 ? (
          <Empty 
            description="No notifications"
            style={{ padding: '40px' }}
          />
        ) : (
          <List
            dataSource={notifications}
            renderItem={(notification) => (
              <List.Item
                style={{
                  padding: '12px 16px',
                  background: notification.read ? '#fff' : '#f6ffed',
                  borderBottom: '1px solid #f0f0f0',
                  cursor: 'pointer'
                }}
                onClick={() => !notification.read && handleMarkAsRead(notification.id)}
                actions={[
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteNotification(notification.id);
                    }}
                    style={{ color: '#ff4d4f' }}
                  />
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar 
                      icon={getNotificationIcon(notification.type)}
                      style={{ backgroundColor: 'transparent' }}
                    />
                  }
                  title={
                    <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                      <Text 
                        strong={!notification.read}
                        style={{ fontSize: '14px' }}
                      >
                        {notification.title}
                      </Text>
                      <Space size="small">
                        {notification.priority === 'high' && (
                          <Tag color={getPriorityColor(notification.priority)} size="small">
                            HIGH
                          </Tag>
                        )}
                        <Tag color={getModuleColor(notification.module)} size="small">
                          {notification.module.toUpperCase()}
                        </Tag>
                      </Space>
                    </Space>
                  }
                  description={
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <Text 
                        type="secondary" 
                        style={{ 
                          fontSize: '12px',
                          display: 'block',
                          lineHeight: '1.4'
                        }}
                      >
                        {notification.message}
                      </Text>
                      <Text 
                        type="secondary" 
                        style={{ fontSize: '11px' }}
                      >
                        {formatTimestamp(notification.timestamp)}
                      </Text>
                    </Space>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </div>

      {/* Footer */}
      {notifications.length > 0 && (
        <>
          <Divider style={{ margin: 0 }} />
          <div style={{ 
            padding: '8px 16px', 
            textAlign: 'center',
            background: '#fafafa'
          }}>
            <Space>
              <Button type="link" size="small">
                View All
              </Button>
              <Button 
                type="link" 
                size="small" 
                danger
                onClick={handleClearAll}
              >
                Clear All
              </Button>
            </Space>
          </div>
        </>
      )}
    </div>
  );

  return (
    <Dropdown
      overlay={notificationMenu}
      trigger={['click']}
      placement="bottomRight"
      overlayClassName="notification-dropdown"
    >
      <Button 
        type="text" 
        icon={
          <Badge count={unreadCount} size="small">
            <BellOutlined style={{ fontSize: '16px' }} />
          </Badge>
        }
        className="notification-button"
      />
    </Dropdown>
  );
};

export default NotificationCenter;
