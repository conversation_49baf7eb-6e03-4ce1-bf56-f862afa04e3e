import React, { useState, useEffect } from 'react';
import { Table, Button, Typography, Tag, Space, message } from 'antd';
import { PlusOutlined, EditOutlined, CheckOutlined } from '@ant-design/icons';

const { Title } = Typography;

const JournalEntries = () => {
  const [entries, setEntries] = useState([]);
  const [loading, setLoading] = useState(false);

  // Mock data
  const mockEntries = [
    {
      id: 1,
      name: 'MISC/2024/001',
      date: '2024-01-15',
      journal: 'Miscellaneous Operations',
      partner: 'ABC Company',
      ref: 'Invoice Payment',
      state: 'posted',
      amount_total: 15000,
    },
    {
      id: 2,
      name: 'BILL/2024/001',
      date: '2024-01-14',
      journal: 'Vendor Bills',
      partner: 'XYZ Supplier',
      ref: 'Office Supplies',
      state: 'draft',
      amount_total: 8000,
    },
  ];

  const columns = [
    {
      title: 'Number',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: 'Journal',
      dataIndex: 'journal',
      key: 'journal',
    },
    {
      title: 'Partner',
      dataIndex: 'partner',
      key: 'partner',
    },
    {
      title: 'Reference',
      dataIndex: 'ref',
      key: 'ref',
    },
    {
      title: 'Total',
      dataIndex: 'amount_total',
      key: 'amount_total',
      render: (amount) => `PKR ${amount.toLocaleString()}`,
      align: 'right',
    },
    {
      title: 'Status',
      dataIndex: 'state',
      key: 'state',
      render: (state) => (
        <Tag color={state === 'posted' ? 'green' : 'orange'}>
          {state === 'posted' ? 'Posted' : 'Draft'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="text" icon={<EditOutlined />} size="small" />
          {record.state === 'draft' && (
            <Button type="text" icon={<CheckOutlined />} size="small" />
          )}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    setEntries(mockEntries);
  }, []);

  return (
    <div>
      <div className="page-header">
        <Title level={2}>Journal Entries</Title>
        <p>Manage journal entries and accounting transactions</p>
      </div>

      <div className="action-buttons">
        <Button type="primary" icon={<PlusOutlined />}>
          Create Entry
        </Button>
      </div>

      <div className="table-container">
        <Table
          columns={columns}
          dataSource={entries}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} entries`,
          }}
        />
      </div>
    </div>
  );
};

export default JournalEntries;
