# Generated by Django 4.2.21 on 2025-07-15 20:28

import datetime
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0003_delete_ircron'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountPaymentRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_type', models.CharField(choices=[('outbound', 'Send Money'), ('inbound', 'Receive Money')], default='inbound', max_length=16)),
                ('partner_type', models.CharField(choices=[('customer', 'Customer'), ('supplier', 'Vendor')], default='customer', max_length=16)),
                ('source_amount', models.DecimalField(decimal_places=2, default=0, help_text='Amount to Pay', max_digits=16)),
                ('source_amount_currency', models.DecimalField(decimal_places=2, default=0, help_text='Amount in Currency', max_digits=16)),
                ('amount', models.DecimalField(decimal_places=2, default=0, help_text='Payment Amount', max_digits=16)),
                ('payment_date', models.DateField(default=datetime.date.today, help_text='Payment Date')),
                ('communication', models.CharField(blank=True, help_text='Memo', max_length=256)),
                ('currency_id', models.CharField(default='PKR', help_text='Currency', max_length=3)),
                ('group_payment', models.BooleanField(default=False, help_text='Group Payments')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(help_text='Payment Journal', on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
                ('line_ids', models.ManyToManyField(help_text='Journal Items', to='accounting.accountmoveline')),
            ],
            options={
                'db_table': 'account_payment_register',
            },
        ),
        migrations.CreateModel(
            name='AccountMoveReversal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('refund_method', models.CharField(choices=[('refund', 'Partial Refund'), ('cancel', 'Full Refund'), ('modify', 'Cancel: create refund and new draft invoice')], default='refund', max_length=16)),
                ('reason', models.CharField(blank=True, help_text='Reason', max_length=128)),
                ('date', models.DateField(default=datetime.date.today, help_text='Reversal Date')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(help_text='Use Specific Journal', on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
                ('move_ids', models.ManyToManyField(help_text='Journal Entries', to='accounting.accountmove')),
                ('new_move_ids', models.ManyToManyField(blank=True, help_text='Reversal Moves', related_name='reversal_new_moves', to='accounting.accountmove')),
            ],
            options={
                'db_table': 'account_move_reversal',
            },
        ),
        migrations.CreateModel(
            name='AccountInvoiceSend',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('template_id', models.CharField(choices=[('invoice', 'Invoice'), ('invoice_reminder', 'Invoice Reminder')], default='invoice', max_length=32)),
                ('email_from', models.EmailField(help_text='From', max_length=254)),
                ('email_to', models.TextField(help_text='To')),
                ('email_cc', models.TextField(blank=True, help_text='Cc')),
                ('subject', models.CharField(help_text='Subject', max_length=256)),
                ('body', models.TextField(help_text='Contents')),
                ('attachment_ids', models.TextField(blank=True, help_text='Attachments')),
                ('is_email', models.BooleanField(default=True, help_text='Email')),
                ('is_print', models.BooleanField(default=False, help_text='Print')),
                ('printed', models.BooleanField(default=False, help_text='Is Printed')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('invoice_ids', models.ManyToManyField(help_text='Invoices', to='accounting.accountmove')),
            ],
            options={
                'db_table': 'account_invoice_send',
            },
        ),
        migrations.CreateModel(
            name='AccountDebitNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('copy_lines', models.CharField(choices=[('copy_lines', 'Copy lines'), ('refund_lines', 'Refund lines')], default='copy_lines', max_length=16)),
                ('reason', models.CharField(help_text='Reason', max_length=128)),
                ('date', models.DateField(default=datetime.date.today, help_text='Debit Note Date')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(help_text='Journal', on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
                ('move_ids', models.ManyToManyField(help_text='Journal Entries', to='accounting.accountmove')),
            ],
            options={
                'db_table': 'account_debit_note',
            },
        ),
    ]
