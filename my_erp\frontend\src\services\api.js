import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available (using test token for now)
    const token = localStorage.getItem('authToken') || '63177388a58e748084acbc7a603a77d0d262f48a';
    if (token) {
      config.headers.Authorization = `Token ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API endpoints
export const accountingAPI = {
  // Companies
  getCompanies: () => api.get('/accounting/companies/'),
  getCompany: (id) => api.get(`/accounting/companies/${id}/`),
  createCompany: (data) => api.post('/accounting/companies/', data),
  updateCompany: (id, data) => api.put(`/accounting/companies/${id}/`, data),
  deleteCompany: (id) => api.delete(`/accounting/companies/${id}/`),

  // Partners (Customers/Vendors)
  getPartners: (params) => api.get('/api/partners/', { params }),
  getPartner: (id) => api.get(`/api/partners/${id}/`),
  createPartner: (data) => api.post('/api/partners/', data),
  updatePartner: (id, data) => api.put(`/api/partners/${id}/`, data),
  deletePartner: (id) => api.delete(`/api/partners/${id}/`),
  getCustomers: () => api.get('/api/partners/customers/'),
  getVendors: () => api.get('/api/partners/vendors/'),

  // Chart of Accounts
  getAccounts: (params) => api.get('/api/accounts/', { params }),
  getAccount: (id) => api.get(`/api/accounts/${id}/`),
  createAccount: (data) => api.post('/api/accounts/', data),
  updateAccount: (id, data) => api.put(`/api/accounts/${id}/`, data),
  deleteAccount: (id) => api.delete(`/api/accounts/${id}/`),
  getReceivableAccounts: () => api.get('/api/accounts/receivable/'),
  getPayableAccounts: () => api.get('/api/accounts/payable/'),

  // Journals
  getJournals: (params) => api.get('/api/journals/', { params }),
  getJournal: (id) => api.get(`/api/journals/${id}/`),
  createJournal: (data) => api.post('/api/journals/', data),
  updateJournal: (id, data) => api.put(`/api/journals/${id}/`, data),
  deleteJournal: (id) => api.delete(`/api/journals/${id}/`),

  // Journal Entries/Moves
  getMoves: (params) => api.get('/api/moves/', { params }),
  getMove: (id) => api.get(`/api/moves/${id}/`),
  createMove: (data) => api.post('/api/moves/', data),
  updateMove: (id, data) => api.put(`/api/moves/${id}/`, data),
  deleteMove: (id) => api.delete(`/api/moves/${id}/`),
  postMove: (id) => api.post(`/api/moves/${id}/action_post/`),
  getInvoices: () => api.get('/api/moves/invoices/'),
  getBills: () => api.get('/api/moves/bills/'),

  // Move Lines
  getMoveLines: (params) => api.get('/api/move-lines/', { params }),
  getMoveLine: (id) => api.get(`/api/move-lines/${id}/`),
  createMoveLine: (data) => api.post('/api/move-lines/', data),
  updateMoveLine: (id, data) => api.put(`/api/move-lines/${id}/`, data),
  deleteMoveLine: (id) => api.delete(`/api/move-lines/${id}/`),
  getUnreconciledLines: () => api.get('/api/move-lines/unreconciled/'),

  // Payments
  getPayments: (params) => api.get('/api/payments/', { params }),
  getPayment: (id) => api.get(`/api/payments/${id}/`),
  createPayment: (data) => api.post('/api/payments/', data),
  updatePayment: (id, data) => api.put(`/api/payments/${id}/`, data),
  deletePayment: (id) => api.delete(`/api/payments/${id}/`),
  postPayment: (id) => api.post(`/api/payments/${id}/action_post/`),

  // Taxes
  getTaxes: (params) => api.get('/api/taxes/', { params }),
  getTax: (id) => api.get(`/api/taxes/${id}/`),
  createTax: (data) => api.post('/api/taxes/', data),
  updateTax: (id, data) => api.put(`/api/taxes/${id}/`, data),
  deleteTax: (id) => api.delete(`/api/taxes/${id}/`),

  // Payment Terms
  getPaymentTerms: (params) => api.get('/api/payment-terms/', { params }),
  getPaymentTerm: (id) => api.get(`/api/payment-terms/${id}/`),
  createPaymentTerm: (data) => api.post('/api/payment-terms/', data),
  updatePaymentTerm: (id, data) => api.put(`/api/payment-terms/${id}/`, data),
  deletePaymentTerm: (id) => api.delete(`/api/payment-terms/${id}/`),

  // Dashboard & Reports
  getDashboardStats: (params) => api.get('/api/dashboard/stats/', { params }),
  getTrialBalance: (params) => api.get('/api/reports/trial-balance/', { params }),
  getProfitLoss: (params) => api.get('/api/reports/profit-loss/', { params }),
  getBalanceSheet: (params) => api.get('/api/reports/balance-sheet/', { params }),

  // Reconciliation
  getBankReconciliation: (params) => api.get('/api/reconciliation/bank/', { params }),
  performManualReconciliation: (data) => api.post('/api/reconciliation/manual/', data),

  // Utilities
  getNextSequence: (params) => api.get('/api/sequence/next/', { params }),
  setupChartOfAccounts: (data) => api.post('/api/chart-of-accounts/setup/', data),
};

// Setup API endpoints
export const setupAPI = {
  // Countries
  getCountries: () => api.get('/setup/countries/'),
  getCountry: (id) => api.get(`/setup/countries/${id}/`),

  // Currencies
  getCurrencies: () => api.get('/setup/currencies/'),
  getCurrency: (id) => api.get(`/setup/currencies/${id}/`),

  // Timezones
  getTimezones: () => api.get('/setup/timezones/'),
  getTimezone: (id) => api.get(`/setup/timezones/${id}/`),

  // Company Setup
  getCompanySetups: () => api.get('/setup/companies/'),
  getCompanySetup: (id) => api.get(`/setup/companies/${id}/`),
  createCompanySetup: (data) => api.post('/setup/companies/', data),
  updateCompanySetup: (id, data) => api.put(`/setup/companies/${id}/`, data),
  deleteCompanySetup: (id) => api.delete(`/setup/companies/${id}/`),

  // Company Setup Progress
  getSetupProgress: (id) => api.get(`/setup/companies/${id}/progress/`),
  completeSetupStep: (id, data) => api.post(`/setup/companies/${id}/complete_step/`, data),
  validateSetup: (id) => api.get(`/setup/companies/${id}/validate/`),

  // Bank Accounts
  getBankAccounts: (companyId) => api.get(`/setup/bank-accounts/?company=${companyId}`),
  getBankAccount: (id) => api.get(`/setup/bank-accounts/${id}/`),
  createBankAccount: (data) => api.post('/setup/bank-accounts/', data),
  updateBankAccount: (id, data) => api.put(`/setup/bank-accounts/${id}/`, data),
  deleteBankAccount: (id) => api.delete(`/setup/bank-accounts/${id}/`),

  // Tax Configurations
  getTaxConfigurations: (companyId) => api.get(`/setup/tax-configurations/?company=${companyId}`),
  getTaxConfiguration: (id) => api.get(`/setup/tax-configurations/${id}/`),
  createTaxConfiguration: (data) => api.post('/setup/tax-configurations/', data),
  updateTaxConfiguration: (id, data) => api.put(`/setup/tax-configurations/${id}/`, data),
  deleteTaxConfiguration: (id) => api.delete(`/setup/tax-configurations/${id}/`),

  // Payment Terms
  getPaymentTerms: (companyId) => api.get(`/setup/payment-terms/?company=${companyId}`),
  getPaymentTerm: (id) => api.get(`/setup/payment-terms/${id}/`),
  createPaymentTerm: (data) => api.post('/setup/payment-terms/', data),
  updatePaymentTerm: (id, data) => api.put(`/setup/payment-terms/${id}/`, data),
  deletePaymentTerm: (id) => api.delete(`/setup/payment-terms/${id}/`),

  // Setup Templates
  getSetupTemplates: (businessType) => api.get(`/setup/templates/${businessType ? `?business_type=${businessType}` : ''}`),
  getSetupTemplate: (id) => api.get(`/setup/templates/${id}/`),
  applySetupTemplate: (id, data) => api.post(`/setup/templates/${id}/apply_template/`, data),

  // System Configuration (Admin only)
  getSystemConfigurations: () => api.get('/setup/system-config/'),
  getSystemConfiguration: (id) => api.get(`/setup/system-config/${id}/`),
  createSystemConfiguration: (data) => api.post('/setup/system-config/', data),
  updateSystemConfiguration: (id, data) => api.put(`/setup/system-config/${id}/`, data),
  deleteSystemConfiguration: (id) => api.delete(`/setup/system-config/${id}/`),
};

export default api;
