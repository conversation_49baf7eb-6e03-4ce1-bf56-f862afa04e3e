"""
Analytics/Reporting Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import (
    IrUiView, IrFilters, IrActWindow, BaseImport, IrCron,
    AnalyticsDashboard, AnalyticsKpi, AnalyticsReport
)


@admin.register(IrUiView)
class IrUiViewAdmin(admin.ModelAdmin):
    list_display = ['name', 'model', 'type', 'mode', 'priority', 'active']
    list_filter = ['type', 'mode', 'active']
    search_fields = ['name', 'model']
    ordering = ['model', 'type', 'priority']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'model', 'type', 'active')
        }),
        ('Inheritance', {
            'fields': ('inherit_id', 'mode')
        }),
        ('Properties', {
            'fields': ('priority', 'groups_id')
        }),
        ('Architecture', {
            'fields': ('arch', 'arch_db'),
            'classes': ('collapse',)
        }),
    )


@admin.register(IrFilters)
class IrFiltersAdmin(admin.ModelAdmin):
    list_display = ['name', 'model_id', 'user_id', 'is_default', 'active']
    list_filter = ['is_default', 'active', 'model_id']
    search_fields = ['name', 'model_id']
    ordering = ['model_id', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'model_id', 'active')
        }),
        ('User & Sharing', {
            'fields': ('user_id', 'is_default')
        }),
        ('Filter Definition', {
            'fields': ('domain', 'context', 'sort'),
            'classes': ('collapse',)
        }),
        ('Advanced', {
            'fields': ('action_id',),
            'classes': ('collapse',)
        }),
    )


@admin.register(IrActWindow)
class IrActWindowAdmin(admin.ModelAdmin):
    list_display = ['name', 'res_model', 'view_mode', 'target', 'limit']
    list_filter = ['target', 'auto_search']
    search_fields = ['name', 'res_model']
    ordering = ['name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'type', 'res_model')
        }),
        ('Views', {
            'fields': ('view_mode', 'view_id')
        }),
        ('Domain & Context', {
            'fields': ('domain', 'context'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('target', 'limit', 'auto_search')
        }),
    )


@admin.register(BaseImport)
class BaseImportAdmin(admin.ModelAdmin):
    list_display = ['res_model', 'file_name', 'file_type', 'state', 'create_uid', 'create_date']
    list_filter = ['state', 'file_type', 'create_date']
    search_fields = ['res_model', 'file_name']
    ordering = ['-create_date']

    fieldsets = (
        ('Import Information', {
            'fields': ('res_model', 'file', 'file_name', 'file_type')
        }),
        ('State', {
            'fields': ('state',)
        }),
        ('Results', {
            'fields': ('import_messages',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['create_uid']


@admin.register(IrCron)
class IrCronAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'interval_display', 'nextcall', 'lastcall', 'user_id']
    list_filter = ['active', 'interval_type', 'user_id']
    search_fields = ['name', 'model_id', 'function']
    ordering = ['nextcall', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'active', 'user_id')
        }),
        ('Scheduling', {
            'fields': ('interval_number', 'interval_type', 'numbercall', 'doall')
        }),
        ('Execution', {
            'fields': ('model_id', 'function', 'args')
        }),
        ('Next Execution', {
            'fields': ('nextcall', 'lastcall')
        }),
        ('Advanced', {
            'fields': ('priority',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['lastcall']

    def interval_display(self, obj):
        return f"Every {obj.interval_number} {obj.interval_type}"
    interval_display.short_description = 'Interval'


class AnalyticsKpiInline(admin.TabularInline):
    model = AnalyticsKpi
    extra = 0
    fields = ['name', 'type', 'current_value', 'target_value', 'period', 'color']


@admin.register(AnalyticsDashboard)
class AnalyticsDashboardAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'active', 'public', 'kpi_count', 'company_id']
    list_filter = ['type', 'active', 'public', 'company_id']
    search_fields = ['name']
    inlines = [AnalyticsKpiInline]
    filter_horizontal = ['user_ids']
    ordering = ['type', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'type', 'active')
        }),
        ('Access', {
            'fields': ('user_ids', 'public')
        }),
        ('Configuration', {
            'fields': ('config', 'layout'),
            'classes': ('collapse',)
        }),
    )

    def kpi_count(self, obj):
        count = obj.kpi_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    kpi_count.short_description = 'KPIs'


@admin.register(AnalyticsKpi)
class AnalyticsKpiAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'current_value_display', 'target_value_display', 'variance_display', 'period', 'color_display']
    list_filter = ['type', 'period', 'color', 'active', 'company_id']
    search_fields = ['name', 'model_id']
    ordering = ['dashboard_id', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'type', 'active', 'dashboard_id')
        }),
        ('Values', {
            'fields': ('current_value', 'target_value', 'previous_value', 'suffix')
        }),
        ('Period', {
            'fields': ('period',)
        }),
        ('Calculation', {
            'fields': ('model_id', 'field_name', 'domain'),
            'classes': ('collapse',)
        }),
        ('Display', {
            'fields': ('color',)
        }),
        ('System', {
            'fields': ('last_update',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['last_update']

    def current_value_display(self, obj):
        return format_html(
            '<span style="font-weight: bold;">{:,.2f}{}</span>',
            obj.current_value, obj.suffix
        )
    current_value_display.short_description = 'Current Value'

    def target_value_display(self, obj):
        return format_html(
            '<span style="color: blue;">{:,.2f}{}</span>',
            obj.target_value, obj.suffix
        )
    target_value_display.short_description = 'Target Value'

    def variance_display(self, obj):
        variance = obj.variance
        color = 'green' if variance >= 0 else 'red'
        symbol = '+' if variance >= 0 else ''
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}{:.1f}%</span>',
            color, symbol, variance
        )
    variance_display.short_description = 'Variance'

    def color_display(self, obj):
        colors = {'green': '#28a745', 'yellow': '#ffc107', 'red': '#dc3545'}
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 8px; border-radius: 3px;">{}</span>',
            colors.get(obj.color, '#6c757d'), obj.color.title()
        )
    color_display.short_description = 'Color'

    actions = ['action_update_values']

    def action_update_values(self, request, queryset):
        updated = 0
        for kpi in queryset:
            kpi.update_value()
            updated += 1
        self.message_user(request, f'{updated} KPIs updated successfully.')
    action_update_values.short_description = "Update selected KPI values"


@admin.register(AnalyticsReport)
class AnalyticsReportAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'model_id', 'output_format', 'auto_generate', 'active']
    list_filter = ['type', 'output_format', 'auto_generate', 'active', 'company_id']
    search_fields = ['name', 'model_id']
    filter_horizontal = ['user_ids']
    ordering = ['type', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'type', 'active')
        }),
        ('Configuration', {
            'fields': ('model_id', 'domain', 'fields', 'groupby')
        }),
        ('Output', {
            'fields': ('output_format',)
        }),
        ('Scheduling', {
            'fields': ('auto_generate', 'schedule_cron_id'),
            'classes': ('collapse',)
        }),
        ('Access', {
            'fields': ('user_ids', 'public'),
            'classes': ('collapse',)
        }),
    )

    actions = ['action_generate_reports']

    def action_generate_reports(self, request, queryset):
        generated = 0
        for report in queryset:
            report.generate_report()
            generated += 1
        self.message_user(request, f'{generated} reports generated successfully.')
    action_generate_reports.short_description = "Generate selected reports"


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - Analytics & Reporting"
