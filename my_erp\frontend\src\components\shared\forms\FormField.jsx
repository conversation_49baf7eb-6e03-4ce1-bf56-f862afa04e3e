/**
 * FormField - Standardized form field components with consistent styling
 */
import React from 'react';
import { Form, Input, Select, DatePicker, InputNumber, Switch, Radio, Checkbox, Upload, Button } from 'antd';
import { UploadOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

// Text Input Field
export const TextField = ({ 
  name, 
  label, 
  placeholder, 
  required = false, 
  rules = [], 
  disabled = false,
  maxLength,
  showCount = false,
  prefix,
  suffix,
  addonBefore,
  addonAfter,
  size = 'default',
  ...props 
}) => {
  const fieldRules = required 
    ? [{ required: true, message: `Please enter ${label?.toLowerCase() || 'this field'}` }, ...rules]
    : rules;

  return (
    <Form.Item
      name={name}
      label={label}
      rules={fieldRules}
      {...props}
    >
      <Input
        placeholder={placeholder || `Enter ${label?.toLowerCase() || 'value'}`}
        disabled={disabled}
        maxLength={maxLength}
        showCount={showCount}
        prefix={prefix}
        suffix={suffix}
        addonBefore={addonBefore}
        addonAfter={addonAfter}
        size={size}
      />
    </Form.Item>
  );
};

// Password Input Field
export const PasswordField = ({ 
  name, 
  label, 
  placeholder, 
  required = false, 
  rules = [], 
  disabled = false,
  size = 'default',
  ...props 
}) => {
  const fieldRules = required 
    ? [{ required: true, message: `Please enter ${label?.toLowerCase() || 'password'}` }, ...rules]
    : rules;

  return (
    <Form.Item
      name={name}
      label={label}
      rules={fieldRules}
      {...props}
    >
      <Input.Password
        placeholder={placeholder || `Enter ${label?.toLowerCase() || 'password'}`}
        disabled={disabled}
        size={size}
        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
      />
    </Form.Item>
  );
};

// Textarea Field
export const TextAreaField = ({ 
  name, 
  label, 
  placeholder, 
  required = false, 
  rules = [], 
  disabled = false,
  rows = 4,
  maxLength,
  showCount = true,
  ...props 
}) => {
  const fieldRules = required 
    ? [{ required: true, message: `Please enter ${label?.toLowerCase() || 'this field'}` }, ...rules]
    : rules;

  return (
    <Form.Item
      name={name}
      label={label}
      rules={fieldRules}
      {...props}
    >
      <TextArea
        placeholder={placeholder || `Enter ${label?.toLowerCase() || 'description'}`}
        disabled={disabled}
        rows={rows}
        maxLength={maxLength}
        showCount={showCount}
      />
    </Form.Item>
  );
};

// Select Field
export const SelectField = ({ 
  name, 
  label, 
  placeholder, 
  required = false, 
  rules = [], 
  disabled = false,
  options = [],
  mode,
  allowClear = true,
  showSearch = true,
  size = 'default',
  loading = false,
  ...props 
}) => {
  const fieldRules = required 
    ? [{ required: true, message: `Please select ${label?.toLowerCase() || 'an option'}` }, ...rules]
    : rules;

  return (
    <Form.Item
      name={name}
      label={label}
      rules={fieldRules}
      {...props}
    >
      <Select
        placeholder={placeholder || `Select ${label?.toLowerCase() || 'option'}`}
        disabled={disabled}
        mode={mode}
        allowClear={allowClear}
        showSearch={showSearch}
        size={size}
        loading={loading}
        filterOption={(input, option) =>
          option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
      >
        {options.map(option => (
          <Option key={option.value} value={option.value}>
            {option.label}
          </Option>
        ))}
      </Select>
    </Form.Item>
  );
};

// Number Input Field
export const NumberField = ({ 
  name, 
  label, 
  placeholder, 
  required = false, 
  rules = [], 
  disabled = false,
  min,
  max,
  step = 1,
  precision,
  formatter,
  parser,
  addonBefore,
  addonAfter,
  size = 'default',
  ...props 
}) => {
  const fieldRules = required 
    ? [{ required: true, message: `Please enter ${label?.toLowerCase() || 'a number'}` }, ...rules]
    : rules;

  return (
    <Form.Item
      name={name}
      label={label}
      rules={fieldRules}
      {...props}
    >
      <InputNumber
        placeholder={placeholder || `Enter ${label?.toLowerCase() || 'number'}`}
        disabled={disabled}
        min={min}
        max={max}
        step={step}
        precision={precision}
        formatter={formatter}
        parser={parser}
        addonBefore={addonBefore}
        addonAfter={addonAfter}
        size={size}
        style={{ width: '100%' }}
      />
    </Form.Item>
  );
};

// Date Picker Field
export const DateField = ({ 
  name, 
  label, 
  placeholder, 
  required = false, 
  rules = [], 
  disabled = false,
  format = 'YYYY-MM-DD',
  showTime = false,
  size = 'default',
  ...props 
}) => {
  const fieldRules = required 
    ? [{ required: true, message: `Please select ${label?.toLowerCase() || 'a date'}` }, ...rules]
    : rules;

  return (
    <Form.Item
      name={name}
      label={label}
      rules={fieldRules}
      {...props}
    >
      <DatePicker
        placeholder={placeholder || `Select ${label?.toLowerCase() || 'date'}`}
        disabled={disabled}
        format={format}
        showTime={showTime}
        size={size}
        style={{ width: '100%' }}
      />
    </Form.Item>
  );
};

// Switch Field
export const SwitchField = ({ 
  name, 
  label, 
  disabled = false,
  checkedChildren = 'Yes',
  unCheckedChildren = 'No',
  size = 'default',
  ...props 
}) => {
  return (
    <Form.Item
      name={name}
      label={label}
      valuePropName="checked"
      {...props}
    >
      <Switch
        disabled={disabled}
        checkedChildren={checkedChildren}
        unCheckedChildren={unCheckedChildren}
        size={size}
      />
    </Form.Item>
  );
};

// Radio Group Field
export const RadioField = ({ 
  name, 
  label, 
  required = false, 
  rules = [], 
  disabled = false,
  options = [],
  buttonStyle = 'outline',
  size = 'default',
  ...props 
}) => {
  const fieldRules = required 
    ? [{ required: true, message: `Please select ${label?.toLowerCase() || 'an option'}` }, ...rules]
    : rules;

  return (
    <Form.Item
      name={name}
      label={label}
      rules={fieldRules}
      {...props}
    >
      <Radio.Group disabled={disabled} buttonStyle={buttonStyle} size={size}>
        {options.map(option => (
          <Radio.Button key={option.value} value={option.value}>
            {option.label}
          </Radio.Button>
        ))}
      </Radio.Group>
    </Form.Item>
  );
};

// Checkbox Field
export const CheckboxField = ({ 
  name, 
  label, 
  disabled = false,
  children,
  ...props 
}) => {
  return (
    <Form.Item
      name={name}
      valuePropName="checked"
      {...props}
    >
      <Checkbox disabled={disabled}>
        {children || label}
      </Checkbox>
    </Form.Item>
  );
};

// File Upload Field
export const UploadField = ({
  name,
  label,
  required = false,
  rules = [],
  disabled = false,
  accept,
  maxCount = 1,
  listType = 'text',
  action = '/api/upload/',
  ...props
}) => {
  const fieldRules = required
    ? [{ required: true, message: `Please upload ${label?.toLowerCase() || 'a file'}` }, ...rules]
    : rules;

  return (
    <Form.Item
      name={name}
      label={label}
      rules={fieldRules}
      valuePropName="fileList"
      getValueFromEvent={(e) => {
        if (Array.isArray(e)) {
          return e;
        }
        return e?.fileList;
      }}
      {...props}
    >
      <Upload
        disabled={disabled}
        accept={accept}
        maxCount={maxCount}
        listType={listType}
        action={action}
      >
        <Button icon={<UploadOutlined />} disabled={disabled}>
          Upload {label}
        </Button>
      </Upload>
    </Form.Item>
  );
};

// Currency Field (Number with currency formatting)
export const CurrencyField = ({
  name,
  label,
  placeholder,
  required = false,
  rules = [],
  disabled = false,
  currency = 'USD',
  min = 0,
  precision = 2,
  size = 'default',
  ...props
}) => {
  const fieldRules = required
    ? [{ required: true, message: `Please enter ${label?.toLowerCase() || 'amount'}` }, ...rules]
    : rules;

  const formatter = (value) => {
    if (!value) return '';
    return `${currency} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const parser = (value) => {
    if (!value) return '';
    return value.replace(new RegExp(`${currency}\\s?|(,*)`, 'g'), '');
  };

  return (
    <Form.Item
      name={name}
      label={label}
      rules={fieldRules}
      {...props}
    >
      <InputNumber
        placeholder={placeholder || `Enter ${label?.toLowerCase() || 'amount'}`}
        disabled={disabled}
        min={min}
        precision={precision}
        formatter={formatter}
        parser={parser}
        size={size}
        style={{ width: '100%' }}
      />
    </Form.Item>
  );
};

// Percentage Field
export const PercentageField = ({
  name,
  label,
  placeholder,
  required = false,
  rules = [],
  disabled = false,
  min = 0,
  max = 100,
  precision = 2,
  size = 'default',
  ...props
}) => {
  const fieldRules = required
    ? [{ required: true, message: `Please enter ${label?.toLowerCase() || 'percentage'}` }, ...rules]
    : rules;

  return (
    <Form.Item
      name={name}
      label={label}
      rules={fieldRules}
      {...props}
    >
      <InputNumber
        placeholder={placeholder || `Enter ${label?.toLowerCase() || 'percentage'}`}
        disabled={disabled}
        min={min}
        max={max}
        precision={precision}
        formatter={(value) => `${value}%`}
        parser={(value) => value?.replace('%', '')}
        size={size}
        style={{ width: '100%' }}
      />
    </Form.Item>
  );
};
