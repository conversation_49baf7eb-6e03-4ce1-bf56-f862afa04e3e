/**
 * Authentication Provider - Super User & Admin User Management
 * Following Odoo's user hierarchy and security model
 */
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { message } from 'antd';
import { authAPI } from '../../../services/api';

// User Types following Odoo hierarchy
export const USER_TYPES = {
  SUPER_USER: 'super_user',    // Undeletable master/owner
  ADMIN: 'admin',              // Full ERP copy rights
  USER: 'user',                // Regular user with group permissions
  PORTAL: 'portal'             // External portal user
};

// User Groups following Odoo access rights
export const USER_GROUPS = {
  // Administration
  SETTINGS: 'base.group_system',
  USER_ADMIN: 'base.group_user',
  
  // Accounting
  ACCOUNTING_MANAGER: 'account.group_account_manager',
  ACCOUNTING_USER: 'account.group_account_user',
  ACCOUNTING_READONLY: 'account.group_account_readonly',
  
  // Sales
  SALES_MANAGER: 'sales_team.group_sale_manager',
  SALES_USER: 'sales_team.group_sale_salesman',
  
  // Purchase
  PURCHASE_MANAGER: 'purchase.group_purchase_manager',
  PURCHASE_USER: 'purchase.group_purchase_user',
  
  // Inventory
  STOCK_MANAGER: 'stock.group_stock_manager',
  STOCK_USER: 'stock.group_stock_user',
  
  // HR
  HR_MANAGER: 'hr.group_hr_manager',
  HR_USER: 'hr.group_hr_user',
  
  // Manufacturing
  MRP_MANAGER: 'mrp.group_mrp_manager',
  MRP_USER: 'mrp.group_mrp_user',
  
  // Point of Sale
  POS_MANAGER: 'point_of_sale.group_pos_manager',
  POS_USER: 'point_of_sale.group_pos_user'
};

// Initial state
const initialState = {
  user: null,
  isAuthenticated: false,
  loading: true,
  permissions: [],
  groups: [],
  companies: [],
  currentCompany: null,
  token: localStorage.getItem('token'),
  refreshToken: localStorage.getItem('refreshToken')
};

// Action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SET_LOADING: 'SET_LOADING',
  UPDATE_USER: 'UPDATE_USER',
  SET_COMPANY: 'SET_COMPANY',
  REFRESH_TOKEN: 'REFRESH_TOKEN'
};

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return { ...state, loading: true };
      
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        isAuthenticated: true,
        loading: false,
        permissions: action.payload.permissions || [],
        groups: action.payload.groups || [],
        companies: action.payload.companies || [],
        currentCompany: action.payload.currentCompany || action.payload.companies?.[0],
        token: action.payload.token,
        refreshToken: action.payload.refreshToken
      };
      
    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        loading: false,
        permissions: [],
        groups: [],
        token: null,
        refreshToken: null
      };
      
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState,
        loading: false,
        token: null,
        refreshToken: null
      };
      
    case AUTH_ACTIONS.SET_LOADING:
      return { ...state, loading: action.payload };
      
    case AUTH_ACTIONS.UPDATE_USER:
      return { ...state, user: { ...state.user, ...action.payload } };
      
    case AUTH_ACTIONS.SET_COMPANY:
      return { ...state, currentCompany: action.payload };
      
    case AUTH_ACTIONS.REFRESH_TOKEN:
      return {
        ...state,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken
      };
      
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check if user is Super User (undeletable master)
  const isSuperUser = () => {
    return state.user?.user_type === USER_TYPES.SUPER_USER || state.user?.id === 1;
  };

  // Check if user is Admin (full ERP rights)
  const isAdmin = () => {
    return isSuperUser() || state.user?.user_type === USER_TYPES.ADMIN || 
           state.groups?.includes(USER_GROUPS.SETTINGS);
  };

  // Check if user has specific permission
  const hasPermission = (permission) => {
    if (isSuperUser()) return true;
    return state.permissions?.includes(permission);
  };

  // Check if user belongs to specific group
  const hasGroup = (group) => {
    if (isSuperUser()) return true;
    return state.groups?.includes(group);
  };

  // Check if user can access module
  const canAccessModule = (module) => {
    if (isSuperUser()) return true;
    
    const modulePermissions = {
      accounting: [USER_GROUPS.ACCOUNTING_MANAGER, USER_GROUPS.ACCOUNTING_USER],
      sales: [USER_GROUPS.SALES_MANAGER, USER_GROUPS.SALES_USER],
      purchase: [USER_GROUPS.PURCHASE_MANAGER, USER_GROUPS.PURCHASE_USER],
      inventory: [USER_GROUPS.STOCK_MANAGER, USER_GROUPS.STOCK_USER],
      hr: [USER_GROUPS.HR_MANAGER, USER_GROUPS.HR_USER],
      manufacturing: [USER_GROUPS.MRP_MANAGER, USER_GROUPS.MRP_USER],
      pos: [USER_GROUPS.POS_MANAGER, USER_GROUPS.POS_USER]
    };

    const requiredGroups = modulePermissions[module] || [];
    return requiredGroups.some(group => hasGroup(group));
  };

  // Login function
  const login = async (user, token) => {
    try {
      dispatch({ type: AUTH_ACTIONS.LOGIN_START });

      // Transform user data to match our format
      const transformedUser = {
        id: user.id,
        name: user.name || user.username,
        email: user.email,
        type: user.userType || (user.is_superuser ? USER_TYPES.SUPER_USER : user.is_staff ? USER_TYPES.ADMIN : USER_TYPES.USER),
        userType: user.userType || (user.is_superuser ? USER_TYPES.SUPER_USER : user.is_staff ? USER_TYPES.ADMIN : USER_TYPES.USER)
      };

      // Store tokens
      localStorage.setItem('token', token);

      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: {
          user: transformedUser,
          token,
          refreshToken: null,
          permissions: ['all'], // TODO: Get from API
          groups: user.groups || ['all'], // TODO: Get from API
          companies: [{
            id: 1,
            name: 'DataiCraft Solutions',
            email: '<EMAIL>'
          }],
          currentCompany: {
            id: 1,
            name: 'DataiCraft Solutions',
            email: '<EMAIL>'
          }
        }
      });

      message.success(`Welcome ${transformedUser.name}! ${transformedUser.userType === USER_TYPES.SUPER_USER ? '(Super User)' : transformedUser.userType === USER_TYPES.ADMIN ? '(Administrator)' : ''}`);
      return { success: true };

    } catch (error) {
      dispatch({ type: AUTH_ACTIONS.LOGIN_FAILURE });
      const errorMessage = error.response?.data?.message || 'Login failed';
      message.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Update user function
  const updateUser = async (userData) => {
    try {
      const response = await authAPI.updateProfile(userData);
      if (response.success) {
        dispatch({ type: AUTH_ACTIONS.UPDATE_USER, payload: response.user });
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      console.error('Update user error:', error);
      return { success: false, error: error.error || error.message };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await authAPI.logout();
      message.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      message.error('Logout failed');
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      // Redirect to login page
      window.location.href = '/login';
    }
  };

  // Switch company
  const switchCompany = async (companyId) => {
    try {
      const company = state.companies.find(c => c.id === companyId);
      if (company) {
        dispatch({ type: AUTH_ACTIONS.SET_COMPANY, payload: company });
        // Mock switch company - replace with real API call
        // await authAPI.switchCompany(companyId);
        message.success(`Switched to ${company.name}`);
      }
    } catch (error) {
      message.error('Failed to switch company');
    }
  };

  // Refresh token
  const refreshAuthToken = async () => {
    try {
      // Mock refresh token - replace with real API call
      const response = {
        data: {
          token: '63177388a58e748084acbc7a603a77d0d262f48a',
          refresh_token: 'mock_refresh_token'
        }
      };
      const { token, refresh_token } = response.data;
      
      localStorage.setItem('token', token);
      localStorage.setItem('refreshToken', refresh_token);
      
      dispatch({
        type: AUTH_ACTIONS.REFRESH_TOKEN,
        payload: { token, refreshToken: refresh_token }
      });
      
      return token;
    } catch (error) {
      logout();
      throw error;
    }
  };

  // Initialize auth on mount
  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('token');
      if (token) {
        try {
          // Mock get current user - replace with real API call
          const response = {
            data: {
              user: {
                id: 1,
                name: 'Super User',
                email: '<EMAIL>',
                type: USER_TYPES.SUPER_USER
              },
              permissions: ['all'],
              groups: ['all'],
              companies: [{
                id: 1,
                name: 'DataiCraft Solutions',
                email: '<EMAIL>'
              }]
            }
          };
          const { user, permissions, groups, companies } = response.data;
          
          dispatch({
            type: AUTH_ACTIONS.LOGIN_SUCCESS,
            payload: {
              user,
              token,
              refreshToken: localStorage.getItem('refreshToken'),
              permissions,
              groups,
              companies,
              currentCompany: companies?.[0]
            }
          });
        } catch (error) {
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          dispatch({ type: AUTH_ACTIONS.LOGIN_FAILURE });
        }
      } else {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      }
    };

    initAuth();
  }, []);

  const value = {
    ...state,
    login,
    logout,
    updateUser,
    switchCompany,
    refreshAuthToken,
    isSuperUser,
    isAdmin,
    hasPermission,
    hasGroup,
    canAccessModule,
    USER_TYPES,
    USER_GROUPS
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthProvider;
