"""
Authentication Serializers - User Management API
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.models import User as DjangoUser
from .models import UserGroup, UserGroupMembership

# Use Django's built-in User model for now
User = DjangoUser


class UserSerializer(serializers.ModelSerializer):
    """User serializer for profile management"""
    groups = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'name', 'phone', 'avatar',
            'user_type', 'is_super_user', 'bio', 'department', 'position',
            'preferences', 'last_login', 'created_at', 'groups'
        ]
        read_only_fields = ['id', 'username', 'user_type', 'is_super_user', 'last_login', 'created_at']
    
    def get_groups(self, obj):
        """Get user groups"""
        return [membership.group.name for membership in obj.group_memberships.all()]


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user profile"""
    
    class Meta:
        model = User
        fields = ['name', 'email', 'phone', 'bio', 'avatar']
    
    def validate_email(self, value):
        """Validate email uniqueness"""
        user = self.instance
        if User.objects.exclude(pk=user.pk).filter(email=value).exists():
            raise serializers.ValidationError("This email is already in use.")
        return value


class UserPreferencesSerializer(serializers.Serializer):
    """Serializer for user preferences"""
    theme = serializers.ChoiceField(choices=['light', 'dark', 'auto'], default='light')
    language = serializers.CharField(max_length=10, default='en')
    timezone = serializers.CharField(max_length=50, default='UTC')
    dateFormat = serializers.CharField(max_length=20, default='MM/DD/YYYY')
    timeFormat = serializers.ChoiceField(choices=['12h', '24h'], default='12h')
    currency = serializers.CharField(max_length=10, default='USD')
    
    # Nested preferences
    notifications = serializers.DictField(default={
        'email': True,
        'browser': True,
        'sound': False
    })
    dashboard = serializers.DictField(default={
        'autoRefresh': True,
        'refreshInterval': 30,
        'showWelcome': True
    })
    interface = serializers.DictField(default={
        'sidebarCollapsed': False,
        'compactMode': False,
        'showTooltips': True
    })


class LoginSerializer(serializers.Serializer):
    """Login serializer"""
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    remember_me = serializers.BooleanField(default=False)
    
    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')

        if email and password:
            # Find user by email first
            try:
                user_obj = User.objects.filter(email=email).first()
                if not user_obj:
                    raise serializers.ValidationError('Invalid email or password.')

                # Try to authenticate with username
                user = authenticate(username=user_obj.username, password=password)

                if user:
                    if not user.is_active:
                        raise serializers.ValidationError('User account is disabled.')
                    attrs['user'] = user
                else:
                    raise serializers.ValidationError('Invalid email or password.')
            except Exception:
                raise serializers.ValidationError('Invalid email or password.')
        else:
            raise serializers.ValidationError('Must include email and password.')

        return attrs


class ChangePasswordSerializer(serializers.Serializer):
    """Change password serializer"""
    current_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True)
    confirm_password = serializers.CharField(write_only=True)
    
    def validate_current_password(self, value):
        """Validate current password"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('Current password is incorrect.')
        return value
    
    def validate_new_password(self, value):
        """Validate new password"""
        validate_password(value)
        return value
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError('New passwords do not match.')
        return attrs


class UserGroupSerializer(serializers.ModelSerializer):
    """User group serializer"""
    
    class Meta:
        model = UserGroup
        fields = '__all__'


class UserRegistrationSerializer(serializers.ModelSerializer):
    """User registration serializer"""
    password = serializers.CharField(write_only=True)
    confirm_password = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = ['username', 'email', 'name', 'password', 'confirm_password']
    
    def validate_email(self, value):
        """Validate email uniqueness"""
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("This email is already registered.")
        return value
    
    def validate_username(self, value):
        """Validate username uniqueness"""
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError("This username is already taken.")
        return value
    
    def validate_password(self, value):
        """Validate password strength"""
        validate_password(value)
        return value
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['confirm_password']:
            raise serializers.ValidationError('Passwords do not match.')
        return attrs
    
    def create(self, validated_data):
        """Create new user"""
        validated_data.pop('confirm_password')
        password = validated_data.pop('password')
        
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        
        return user
