/**
 * Sales Tax Form Component - Create/Edit Sales Tax Form (Input/Output)
 */
import React, { useEffect } from 'react';
import { Form, Row, Col, Alert } from 'antd';
import FormWrapper from '../../../components/shared/forms/FormWrapper';
import { 
  TextField, 
  TextAreaField, 
  SelectField, 
  SwitchField,
  PercentageField
} from '../../../components/shared/forms/FormField';

const SalesTaxForm = ({ 
  initialValues, 
  taxDirection = 'input', // 'input' or 'output'
  onSubmit, 
  onCancel, 
  loading = false 
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    } else {
      // Set default values for new tax
      form.setFieldsValue({
        active: true,
        tax_direction: taxDirection,
        scope: 'state'
      });
    }
  }, [initialValues, taxDirection, form]);

  const handleSubmit = (values) => {
    onSubmit({
      ...values,
      tax_direction: taxDirection
    });
  };

  // Scope options
  const scopeOptions = [
    { value: 'federal', label: 'Federal' },
    { value: 'state', label: 'State' },
    { value: 'county', label: 'County' },
    { value: 'city', label: 'City' },
    { value: 'local', label: 'Local' },
  ];

  // Tax type options based on direction
  const getTypeOptions = () => {
    if (taxDirection === 'input') {
      return [
        { value: 'vat_input', label: 'VAT Input Tax' },
        { value: 'purchase_tax', label: 'Purchase Tax' },
        { value: 'import_duty', label: 'Import Duty' },
        { value: 'excise_input', label: 'Excise Input Tax' },
        { value: 'service_input', label: 'Service Input Tax' },
      ];
    } else {
      return [
        { value: 'vat_output', label: 'VAT Output Tax' },
        { value: 'sales_tax', label: 'Sales Tax' },
        { value: 'service_tax', label: 'Service Tax' },
        { value: 'excise_output', label: 'Excise Output Tax' },
        { value: 'luxury_tax', label: 'Luxury Tax' },
      ];
    }
  };

  // Calculation method options
  const calculationMethodOptions = [
    { value: 'percentage', label: 'Percentage of Amount' },
    { value: 'fixed_amount', label: 'Fixed Amount' },
    { value: 'tiered', label: 'Tiered Rate' },
    { value: 'compound', label: 'Compound Tax' },
  ];

  // Account type options
  const accountTypeOptions = taxDirection === 'input' 
    ? [
        { value: 'input_tax_account', label: 'Input Tax Account' },
        { value: 'vat_recoverable', label: 'VAT Recoverable' },
        { value: 'tax_credit_account', label: 'Tax Credit Account' },
      ]
    : [
        { value: 'output_tax_account', label: 'Output Tax Account' },
        { value: 'vat_payable', label: 'VAT Payable' },
        { value: 'tax_liability_account', label: 'Tax Liability Account' },
      ];

  const getFormTitle = () => {
    const direction = taxDirection === 'input' ? 'Input' : 'Output';
    const action = initialValues ? 'Edit' : 'Create';
    return `${action} ${direction} Tax`;
  };

  const getFormDescription = () => {
    if (taxDirection === 'input') {
      return 'Input tax is VAT paid on purchases and expenses that can typically be reclaimed.';
    } else {
      return 'Output tax is VAT charged on sales and services that must be paid to tax authorities.';
    }
  };

  return (
    <FormWrapper
      title={null}
      form={form}
      onFinish={handleSubmit}
      onCancel={onCancel}
      loading={loading}
      submitText={getFormTitle()}
      showReset={true}
    >
      <Alert
        message={`${taxDirection === 'input' ? 'Input' : 'Output'} Tax Configuration`}
        description={getFormDescription()}
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      {/* Basic Information */}
      <Row gutter={16}>
        <Col span={12}>
          <TextField
            name="name"
            label="Tax Name"
            required
            placeholder={`Enter ${taxDirection} tax name`}
          />
        </Col>
        <Col span={12}>
          <TextField
            name="code"
            label="Tax Code"
            required
            placeholder={`Enter ${taxDirection} tax code`}
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <SelectField
            name="tax_type"
            label="Tax Type"
            required
            options={getTypeOptions()}
            placeholder="Select tax type"
          />
        </Col>
        <Col span={12}>
          <SelectField
            name="scope"
            label="Tax Scope"
            required
            options={scopeOptions}
            placeholder="Select tax scope"
          />
        </Col>
      </Row>

      {/* Tax Rate Configuration */}
      <Row gutter={16}>
        <Col span={12}>
          <PercentageField
            name="rate"
            label="Tax Rate"
            required
            placeholder="Enter tax rate"
            min={0}
            max={100}
          />
        </Col>
        <Col span={12}>
          <SelectField
            name="calculation_method"
            label="Calculation Method"
            options={calculationMethodOptions}
            placeholder="Select calculation method"
          />
        </Col>
      </Row>

      {/* Account Configuration */}
      <Row gutter={16}>
        <Col span={12}>
          <SelectField
            name="account_type"
            label="Account Type"
            options={accountTypeOptions}
            placeholder="Select account type"
          />
        </Col>
        <Col span={12}>
          <TextField
            name="account_code"
            label="Account Code"
            placeholder="Enter account code"
          />
        </Col>
      </Row>

      {/* Additional Settings */}
      <Row gutter={16}>
        <Col span={12}>
          <TextField
            name="jurisdiction"
            label="Jurisdiction"
            placeholder="Enter jurisdiction (e.g., California, Ontario)"
          />
        </Col>
        <Col span={12}>
          <TextField
            name="registration_number"
            label="Tax Registration Number"
            placeholder="Enter tax registration number"
          />
        </Col>
      </Row>

      {/* Status and Options */}
      <Row gutter={16}>
        <Col span={8}>
          <div style={{ paddingTop: '30px' }}>
            <SwitchField
              name="active"
              label="Active"
              checkedChildren="Active"
              unCheckedChildren="Inactive"
            />
          </div>
        </Col>
        <Col span={8}>
          <div style={{ paddingTop: '30px' }}>
            <SwitchField
              name="default_tax"
              label="Default Tax"
              checkedChildren="Default"
              unCheckedChildren="Optional"
            />
          </div>
        </Col>
        <Col span={8}>
          <div style={{ paddingTop: '30px' }}>
            <SwitchField
              name="compound_tax"
              label="Compound Tax"
              checkedChildren="Compound"
              unCheckedChildren="Simple"
            />
          </div>
        </Col>
      </Row>

      {/* Description */}
      <Row gutter={16}>
        <Col span={24}>
          <TextAreaField
            name="description"
            label="Description"
            placeholder="Enter tax description and notes"
            rows={3}
          />
        </Col>
      </Row>
    </FormWrapper>
  );
};

export default SalesTaxForm;
