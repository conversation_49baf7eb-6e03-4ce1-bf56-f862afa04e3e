
[![Runboat](https://img.shields.io/badge/runboat-Try%20me-875A7B.png)](https://runboat.odoo-community.org/builds?repo=OCA/account-financial-reporting&target_branch=18.0)
[![Pre-commit Status](https://github.com/OCA/account-financial-reporting/actions/workflows/pre-commit.yml/badge.svg?branch=18.0)](https://github.com/OCA/account-financial-reporting/actions/workflows/pre-commit.yml?query=branch%3A18.0)
[![Build Status](https://github.com/OCA/account-financial-reporting/actions/workflows/test.yml/badge.svg?branch=18.0)](https://github.com/OCA/account-financial-reporting/actions/workflows/test.yml?query=branch%3A18.0)
[![codecov](https://codecov.io/gh/OCA/account-financial-reporting/branch/18.0/graph/badge.svg)](https://codecov.io/gh/OCA/account-financial-reporting)
[![Translation Status](https://translation.odoo-community.org/widgets/account-financial-reporting-18-0/-/svg-badge.svg)](https://translation.odoo-community.org/engage/account-financial-reporting-18-0/?utm_source=widget)

<!-- /!\ do not modify above this line -->

# account-financial-reporting

account-financial-reporting

<!-- /!\ do not modify below this line -->

<!-- prettier-ignore-start -->

[//]: # (addons)

Available addons
----------------
addon | version | maintainers | summary
--- | --- | --- | ---
[account_financial_report](account_financial_report/) | 18.0.1.2.0 |  | OCA Financial Reports
[account_tax_balance](account_tax_balance/) | 18.0.1.0.0 |  | Compute tax balances based on date range

[//]: # (end addons)

<!-- prettier-ignore-end -->

## Licenses

This repository is licensed under [AGPL-3.0](LICENSE).

However, each module can have a totally different license, as long as they adhere to Odoo Community Association (OCA)
policy. Consult each module's `__manifest__.py` file, which contains a `license` key
that explains its license.

----
OCA, or the [Odoo Community Association](http://odoo-community.org/), is a nonprofit
organization whose mission is to support the collaborative development of Odoo features
and promote its widespread use.
