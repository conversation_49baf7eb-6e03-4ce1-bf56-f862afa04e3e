/**
 * FormWrapper - Standardized form container with consistent styling
 */
import React from 'react';
import { Card, Form, Space, Button, Divider } from 'antd';
import { SaveOutlined, CloseOutlined, ReloadOutlined } from '@ant-design/icons';

const FormWrapper = ({
  title,
  subtitle,
  children,
  form,
  onFinish,
  onCancel,
  onReset,
  loading = false,
  submitText = 'Save',
  showCancel = true,
  showReset = false,
  extra,
  layout = 'vertical',
  size = 'default',
  className = '',
  style = {},
  ...formProps
}) => {
  const handleReset = () => {
    form?.resetFields();
    if (onReset) {
      onReset();
    }
  };

  return (
    <Card
      title={title}
      extra={extra}
      className={`form-wrapper ${className}`}
      style={{
        margin: '0 auto',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        borderRadius: '8px',
        ...style
      }}
    >
      {subtitle && (
        <>
          <p style={{ color: '#666', marginBottom: '24px' }}>{subtitle}</p>
          <Divider style={{ margin: '0 0 24px 0' }} />
        </>
      )}
      
      <Form
        form={form}
        layout={layout}
        size={size}
        onFinish={onFinish}
        scrollToFirstError
        requiredMark="optional"
        {...formProps}
      >
        {children}
        
        <Divider style={{ margin: '32px 0 24px 0' }} />
        
        <div style={{ textAlign: 'right' }}>
          <Space>
            {showReset && (
              <Button
                icon={<ReloadOutlined />}
                onClick={handleReset}
                disabled={loading}
              >
                Reset
              </Button>
            )}
            
            {showCancel && onCancel && (
              <Button
                icon={<CloseOutlined />}
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
            )}
            
            <Button
              type="primary"
              htmlType="submit"
              icon={<SaveOutlined />}
              loading={loading}
              size={size}
            >
              {submitText}
            </Button>
          </Space>
        </div>
      </Form>
    </Card>
  );
};

export default FormWrapper;
