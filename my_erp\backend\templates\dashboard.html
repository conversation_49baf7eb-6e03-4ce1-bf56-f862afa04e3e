<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My ERP - Accounting Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1em;
        }
        
        .section {
            background: white;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #667eea;
            color: white;
            padding: 15px 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .api-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .api-link {
            display: block;
            padding: 15px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-decoration: none;
            color: #495057;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .api-link:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .feature-card {
            padding: 20px;
            border-left: 4px solid #667eea;
        }
        
        .feature-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #667eea;
        }
        
        .feature-desc {
            color: #666;
            line-height: 1.6;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #eee;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 My ERP - Accounting System</h1>
        <p>Complete Odoo-Style Accounting Solution with Django + React</p>
    </div>
    
    <div class="container">
        <div class="stats-grid" id="stats">
            <div class="stat-card">
                <div class="stat-number" id="companies-count">-</div>
                <div class="stat-label">Companies</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="accounts-count">-</div>
                <div class="stat-label">Chart of Accounts</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="customers-count">-</div>
                <div class="stat-label">Customers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="vendors-count">-</div>
                <div class="stat-label">Vendors</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="journals-count">-</div>
                <div class="stat-label">Journals</div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">📊 API Endpoints</div>
            <div class="section-content">
                <div class="api-links">
                    <a href="/api/accounting/api/" class="api-link">🏠 API Root</a>
                    <a href="/api/accounting/api/companies/" class="api-link">🏢 Companies</a>
                    <a href="/api/accounting/api/accounts/" class="api-link">📊 Chart of Accounts</a>
                    <a href="/api/accounting/api/partners/" class="api-link">👥 Partners</a>
                    <a href="/api/accounting/api/journals/" class="api-link">📖 Journals</a>
                    <a href="/admin/" class="api-link">⚙️ Admin Panel</a>
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">✨ Key Features</div>
            <div class="section-content">
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-title">📊 Odoo-Style Models</div>
                        <div class="feature-desc">Complete replication of Odoo's accounting models including ResCompany, ResPartner, AccountAccount, and AccountJournal with all business logic.</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-title">🏗️ Django REST API</div>
                        <div class="feature-desc">Professional RESTful APIs with filtering, searching, and pagination. Full CRUD operations for all accounting entities.</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-title">👥 Partner Management</div>
                        <div class="feature-desc">Unified customer and vendor management system with ranking, credit limits, and contact information.</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-title">💰 Chart of Accounts</div>
                        <div class="feature-desc">Complete chart of accounts with 18 Odoo account types, balance calculations, and reconciliation support.</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-title">📖 Journal System</div>
                        <div class="feature-desc">Multi-journal support for sales, purchases, cash, bank, and general transactions with proper sequencing.</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-title">🔧 Admin Interface</div>
                        <div class="feature-desc">Comprehensive Django admin interface for managing all accounting data with custom displays and filters.</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>🚀 Built with Django + React | Inspired by Odoo's Excellence | Ready for Production</p>
    </div>
    
    <script>
        // Load statistics from API
        async function loadStats() {
            try {
                // Load companies
                const companiesResponse = await fetch('/api/accounting/api/companies/');
                const companiesData = await companiesResponse.json();
                document.getElementById('companies-count').textContent = companiesData.length || companiesData.count || 0;
                
                // Load accounts
                const accountsResponse = await fetch('/api/accounting/api/accounts/');
                const accountsData = await accountsResponse.json();
                document.getElementById('accounts-count').textContent = accountsData.length || accountsData.count || 0;
                
                // Load partners
                const partnersResponse = await fetch('/api/accounting/api/partners/');
                const partnersData = await partnersResponse.json();
                const partners = partnersData.results || partnersData;
                
                const customers = partners.filter(p => p.customer_rank > 0);
                const vendors = partners.filter(p => p.supplier_rank > 0);
                
                document.getElementById('customers-count').textContent = customers.length;
                document.getElementById('vendors-count').textContent = vendors.length;
                
                // Load journals
                const journalsResponse = await fetch('/api/accounting/api/journals/');
                const journalsData = await journalsResponse.json();
                document.getElementById('journals-count').textContent = journalsData.length || journalsData.count || 0;
                
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }
        
        // Load stats when page loads
        document.addEventListener('DOMContentLoaded', loadStats);
    </script>
</body>
</html>
