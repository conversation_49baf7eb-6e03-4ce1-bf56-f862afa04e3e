# Generated by Django 4.2.21 on 2025-07-15 19:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0003_delete_ircron'),
        ('inventory', '0002_alter_stocklocation_unique_together'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MrpBom',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_qty', models.DecimalField(decimal_places=4, default=1, help_text='Quantity', max_digits=16)),
                ('product_uom_id', models.IntegerField(default=1, help_text='Unit of Measure')),
                ('type', models.CharField(choices=[('normal', 'Manufacture this product'), ('phantom', 'Kit'), ('subcontract', 'Subcontracting')], default='normal', max_length=16)),
                ('sequence', models.IntegerField(default=1)),
                ('active', models.BooleanField(default=True)),
                ('picking_type_id', models.IntegerField(blank=True, help_text='Operation Type', null=True)),
                ('consumption', models.CharField(choices=[('flexible', 'Allowed'), ('warning', 'Allowed with warning'), ('strict', 'Blocked')], default='flexible', max_length=16)),
                ('ready_to_produce', models.CharField(choices=[('all_available', 'When all components are available'), ('asap', 'When components for 1st operation are available')], default='asap', max_length=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('product_id', models.ForeignKey(blank=True, help_text='Product Variant', null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
                ('product_tmpl_id', models.ForeignKey(help_text='Product', on_delete=django.db.models.deletion.CASCADE, to='accounting.producttemplate')),
            ],
            options={
                'db_table': 'mrp_bom',
            },
        ),
        migrations.CreateModel(
            name='MrpProduction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Reference', max_length=64, unique=True)),
                ('origin', models.CharField(blank=True, help_text='Source Document', max_length=64)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1, help_text='Quantity to Produce', max_digits=16)),
                ('product_uom_id', models.IntegerField(default=1, help_text='Unit of Measure')),
                ('qty_producing', models.DecimalField(decimal_places=4, default=0, help_text='Currently Produced Quantity', max_digits=16)),
                ('qty_produced', models.DecimalField(decimal_places=4, default=0, help_text='Produced Quantity', max_digits=16)),
                ('date_planned_start', models.DateTimeField(help_text='Scheduled Start Date')),
                ('date_planned_finished', models.DateTimeField(help_text='Scheduled End Date')),
                ('date_start', models.DateTimeField(blank=True, help_text='Start Date', null=True)),
                ('date_finished', models.DateTimeField(blank=True, help_text='End Date', null=True)),
                ('date_deadline', models.DateTimeField(blank=True, help_text='Deadline', null=True)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('confirmed', 'Confirmed'), ('progress', 'In Progress'), ('to_close', 'To Close'), ('done', 'Done'), ('cancel', 'Cancelled')], default='draft', max_length=16)),
                ('priority', models.CharField(choices=[('0', 'Not urgent'), ('1', 'Normal'), ('2', 'Urgent'), ('3', 'Very Urgent')], default='1', max_length=1)),
                ('picking_type_id', models.IntegerField(help_text='Operation Type')),
                ('procurement_group_id', models.IntegerField(blank=True, help_text='Procurement Group', null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('analytic_account_id', models.ForeignKey(blank=True, help_text='Analytic Account', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountanalyticaccount')),
                ('bom_id', models.ForeignKey(blank=True, help_text='Bill of Materials', null=True, on_delete=django.db.models.deletion.SET_NULL, to='manufacturing.mrpbom')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('location_dest_id', models.ForeignKey(help_text='Finished Products Location', on_delete=django.db.models.deletion.CASCADE, related_name='mrp_production_dest', to='inventory.stocklocation')),
                ('location_src_id', models.ForeignKey(help_text='Components Location', on_delete=django.db.models.deletion.CASCADE, related_name='mrp_production_src', to='inventory.stocklocation')),
                ('product_id', models.ForeignKey(help_text='Product to Produce', on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
                ('product_tmpl_id', models.ForeignKey(help_text='Product Template', on_delete=django.db.models.deletion.CASCADE, to='accounting.producttemplate')),
            ],
            options={
                'db_table': 'mrp_production',
                'ordering': ['-date_planned_start'],
            },
        ),
        migrations.CreateModel(
            name='MrpRouting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=1)),
                ('note', models.TextField(blank=True, help_text='Description')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('location_id', models.ForeignKey(help_text='Production Location', on_delete=django.db.models.deletion.CASCADE, to='inventory.stocklocation')),
            ],
            options={
                'db_table': 'mrp_routing',
            },
        ),
        migrations.CreateModel(
            name='MrpRoutingWorkcenter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('sequence', models.IntegerField(default=1)),
                ('time_mode', models.CharField(choices=[('auto', 'Compute based on tracked time'), ('manual', 'Set duration manually')], default='auto', max_length=16)),
                ('time_mode_batch', models.IntegerField(default=10, help_text='Based on')),
                ('time_cycle_manual', models.DecimalField(decimal_places=2, default=60, help_text='Manual Duration (minutes)', max_digits=8)),
                ('time_cycle', models.DecimalField(decimal_places=2, default=60, help_text='Duration (minutes)', max_digits=8)),
                ('batch', models.CharField(choices=[('no', 'Once all products are processed'), ('yes', 'Once some products are processed')], default='no', max_length=16)),
                ('batch_size', models.DecimalField(decimal_places=2, default=1, help_text='Batch Size', max_digits=8)),
                ('note', models.TextField(blank=True, help_text='Description')),
                ('worksheet_type', models.CharField(choices=[('pdf', 'PDF'), ('google_slide', 'Google Slide'), ('text', 'Text')], default='pdf', max_length=16)),
                ('worksheet', models.TextField(blank=True, help_text='Worksheet')),
                ('routing_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operation_ids', to='manufacturing.mrprouting')),
            ],
            options={
                'db_table': 'mrp_routing_workcenter',
                'ordering': ['sequence'],
            },
        ),
        migrations.CreateModel(
            name='MrpWorkcenter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=1)),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('note', models.TextField(blank=True, help_text='Description')),
                ('capacity', models.DecimalField(decimal_places=2, default=1, help_text='Working Capacity', max_digits=8)),
                ('time_efficiency', models.DecimalField(decimal_places=2, default=100, help_text='Time Efficiency (%)', max_digits=5)),
                ('costs_hour', models.DecimalField(decimal_places=2, default=0, help_text='Cost per Hour', max_digits=16)),
                ('time_start', models.DecimalField(decimal_places=2, default=0, help_text='Setup Time (minutes)', max_digits=8)),
                ('time_stop', models.DecimalField(decimal_places=2, default=0, help_text='Cleanup Time (minutes)', max_digits=8)),
                ('default_capacity', models.DecimalField(decimal_places=2, default=1, help_text='Default Capacity', max_digits=8)),
                ('resource_calendar_id', models.IntegerField(blank=True, help_text='Working Hours', null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'mrp_workcenter',
            },
        ),
        migrations.CreateModel(
            name='MrpWorkorder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('qty_production', models.DecimalField(decimal_places=4, default=1, help_text='Original Production Quantity', max_digits=16)),
                ('qty_produced', models.DecimalField(decimal_places=4, default=0, help_text='Produced Quantity', max_digits=16)),
                ('qty_producing', models.DecimalField(decimal_places=4, default=0, help_text='Currently Produced Quantity', max_digits=16)),
                ('state', models.CharField(choices=[('pending', 'Waiting for another WO'), ('waiting', 'Waiting for components'), ('ready', 'Ready'), ('progress', 'In Progress'), ('done', 'Finished'), ('cancel', 'Cancelled')], default='pending', max_length=16)),
                ('sequence', models.IntegerField(default=1)),
                ('date_planned_start', models.DateTimeField(help_text='Scheduled Start Date')),
                ('date_planned_finished', models.DateTimeField(help_text='Scheduled End Date')),
                ('date_start', models.DateTimeField(blank=True, help_text='Start Date', null=True)),
                ('date_finished', models.DateTimeField(blank=True, help_text='End Date', null=True)),
                ('duration_expected', models.DecimalField(decimal_places=2, default=0, help_text='Expected Duration (minutes)', max_digits=8)),
                ('duration', models.DecimalField(decimal_places=2, default=0, help_text='Real Duration (minutes)', max_digits=8)),
                ('costs_hour', models.DecimalField(decimal_places=2, default=0, help_text='Cost per Hour', max_digits=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('next_work_order_id', models.ForeignKey(blank=True, help_text='Next Work Order', null=True, on_delete=django.db.models.deletion.SET_NULL, to='manufacturing.mrpworkorder')),
                ('operation_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='manufacturing.mrproutingworkcenter')),
                ('product_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
                ('production_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='workorder_ids', to='manufacturing.mrpproduction')),
                ('workcenter_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='manufacturing.mrpworkcenter')),
            ],
            options={
                'db_table': 'mrp_workorder',
                'ordering': ['sequence'],
            },
        ),
        migrations.CreateModel(
            name='QualityPoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=128)),
                ('test_type', models.CharField(choices=[('instructions', 'Instructions'), ('pass_fail', 'Pass - Fail'), ('measure', 'Take a measure')], default='instructions', max_length=16)),
                ('note', models.TextField(blank=True, help_text='Instructions')),
                ('norm', models.DecimalField(decimal_places=4, default=0, help_text='Norm', max_digits=16)),
                ('tolerance_min', models.DecimalField(decimal_places=4, default=0, help_text='Min Tolerance', max_digits=16)),
                ('tolerance_max', models.DecimalField(decimal_places=4, default=0, help_text='Max Tolerance', max_digits=16)),
                ('norm_unit', models.CharField(blank=True, help_text='Unit of Measure', max_length=16)),
                ('sequence', models.IntegerField(default=1)),
                ('active', models.BooleanField(default=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('operation_id', models.ForeignKey(blank=True, help_text='Operation', null=True, on_delete=django.db.models.deletion.CASCADE, to='manufacturing.mrproutingworkcenter')),
                ('product_ids', models.ManyToManyField(blank=True, help_text='Products', to='accounting.productproduct')),
                ('product_tmpl_ids', models.ManyToManyField(blank=True, help_text='Product Templates', to='accounting.producttemplate')),
            ],
            options={
                'db_table': 'quality_point',
                'ordering': ['sequence'],
            },
        ),
        migrations.CreateModel(
            name='QualityCheck',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quality_state', models.CharField(choices=[('none', 'To do'), ('pass', 'Passed'), ('fail', 'Failed')], default='none', max_length=16)),
                ('measure', models.DecimalField(blank=True, decimal_places=4, help_text='Measure', max_digits=16, null=True)),
                ('measure_success', models.CharField(choices=[('none', 'No measure'), ('pass', 'Success'), ('fail', 'Failure')], default='none', max_length=16)),
                ('note', models.TextField(blank=True, help_text='Note')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('point_id', models.ForeignKey(help_text='Control Point', on_delete=django.db.models.deletion.CASCADE, to='manufacturing.qualitypoint')),
                ('product_id', models.ForeignKey(help_text='Product', on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
                ('production_id', models.ForeignKey(blank=True, help_text='Production Order', null=True, on_delete=django.db.models.deletion.CASCADE, to='manufacturing.mrpproduction')),
                ('user_id', models.ForeignKey(help_text='Responsible', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('workorder_id', models.ForeignKey(blank=True, help_text='Work Order', null=True, on_delete=django.db.models.deletion.CASCADE, to='manufacturing.mrpworkorder')),
            ],
            options={
                'db_table': 'quality_check',
            },
        ),
        migrations.AddField(
            model_name='mrproutingworkcenter',
            name='workcenter_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='manufacturing.mrpworkcenter'),
        ),
        migrations.AddField(
            model_name='mrpproduction',
            name='routing_id',
            field=models.ForeignKey(blank=True, help_text='Routing', null=True, on_delete=django.db.models.deletion.SET_NULL, to='manufacturing.mrprouting'),
        ),
        migrations.AddField(
            model_name='mrpproduction',
            name='user_id',
            field=models.ForeignKey(blank=True, help_text='Responsible', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='MrpBomLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_qty', models.DecimalField(decimal_places=4, default=1, help_text='Quantity', max_digits=16)),
                ('product_uom_id', models.IntegerField(default=1, help_text='Unit of Measure')),
                ('sequence', models.IntegerField(default=1)),
                ('manual_consumption', models.BooleanField(default=False, help_text='Manual Consumption')),
                ('bom_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bom_line_ids', to='manufacturing.mrpbom')),
                ('operation_id', models.ForeignKey(blank=True, help_text='Consumed in Operation', null=True, on_delete=django.db.models.deletion.SET_NULL, to='manufacturing.mrproutingworkcenter')),
                ('product_id', models.ForeignKey(help_text='Component', on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
            ],
            options={
                'db_table': 'mrp_bom_line',
                'ordering': ['sequence'],
            },
        ),
        migrations.AddField(
            model_name='mrpbom',
            name='routing_id',
            field=models.ForeignKey(blank=True, help_text='Routing', null=True, on_delete=django.db.models.deletion.SET_NULL, to='manufacturing.mrprouting'),
        ),
    ]
