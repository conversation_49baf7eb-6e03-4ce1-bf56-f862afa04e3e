"""
Invoicing Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import (
    AccountMoveReversal, AccountPaymentRegister, AccountInvoiceSend,
    AccountDebitNote
)


@admin.register(AccountMoveReversal)
class AccountMoveReversalAdmin(admin.ModelAdmin):
    list_display = ['reason', 'refund_method', 'date', 'move_count', 'company_id']
    list_filter = ['refund_method', 'date', 'company_id']
    search_fields = ['reason']
    filter_horizontal = ['move_ids', 'new_move_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Reversal Configuration', {
            'fields': ('refund_method', 'reason', 'date')
        }),
        ('Journal Entries', {
            'fields': ('move_ids', 'new_move_ids')
        }),
        ('Journal', {
            'fields': ('journal_id',)
        }),
    )

    readonly_fields = ['new_move_ids']

    def move_count(self, obj):
        count = obj.move_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    move_count.short_description = 'Moves'

    actions = ['reverse_moves']

    def reverse_moves(self, request, queryset):
        reversed_count = 0
        for reversal in queryset:
            moves = reversal.reverse_moves()
            reversed_count += len(moves)
        self.message_user(request, f'{reversed_count} moves reversed successfully.')
    reverse_moves.short_description = "Reverse selected moves"


@admin.register(AccountPaymentRegister)
class AccountPaymentRegisterAdmin(admin.ModelAdmin):
    list_display = ['payment_type', 'partner_type', 'amount', 'payment_date', 'journal_id', 'group_payment']
    list_filter = ['payment_type', 'partner_type', 'group_payment', 'payment_date', 'journal_id']
    search_fields = ['communication']
    filter_horizontal = ['line_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Payment Configuration', {
            'fields': ('payment_type', 'partner_type', 'source_amount', 'source_amount_currency')
        }),
        ('Payment Details', {
            'fields': ('amount', 'payment_date', 'communication')
        }),
        ('Journal and Currency', {
            'fields': ('journal_id', 'currency_id')
        }),
        ('Options', {
            'fields': ('group_payment',)
        }),
        ('Journal Items', {
            'fields': ('line_ids',)
        }),
    )

    actions = ['create_payments']

    def create_payments(self, request, queryset):
        payments_created = 0
        for register in queryset:
            payments = register.action_create_payments()
            payments_created += len(payments)
        self.message_user(request, f'{payments_created} payments created successfully.')
    create_payments.short_description = "Create payments"


@admin.register(AccountInvoiceSend)
class AccountInvoiceSendAdmin(admin.ModelAdmin):
    list_display = ['subject', 'email_from', 'is_email', 'is_print', 'printed', 'invoice_count']
    list_filter = ['is_email', 'is_print', 'printed', 'template_id']
    search_fields = ['subject', 'email_from', 'email_to']
    filter_horizontal = ['invoice_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Email Configuration', {
            'fields': ('template_id', 'email_from', 'email_to', 'email_cc', 'subject', 'body')
        }),
        ('Attachments', {
            'fields': ('attachment_ids',),
            'classes': ('collapse',)
        }),
        ('Options', {
            'fields': ('is_email', 'is_print', 'printed')
        }),
        ('Invoices', {
            'fields': ('invoice_ids',)
        }),
    )

    def invoice_count(self, obj):
        count = obj.invoice_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    invoice_count.short_description = 'Invoices'

    actions = ['send_and_print']

    def send_and_print(self, request, queryset):
        processed = 0
        for send_invoice in queryset:
            results = send_invoice.send_and_print_action()
            processed += len(results)
        self.message_user(request, f'{processed} invoice actions processed successfully.')
    send_and_print.short_description = "Send and/or print invoices"


@admin.register(AccountDebitNote)
class AccountDebitNoteAdmin(admin.ModelAdmin):
    list_display = ['reason', 'copy_lines', 'date', 'journal_id', 'move_count']
    list_filter = ['copy_lines', 'date', 'journal_id']
    search_fields = ['reason']
    filter_horizontal = ['move_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Debit Note Configuration', {
            'fields': ('copy_lines', 'reason', 'date')
        }),
        ('Journal Entries', {
            'fields': ('move_ids',)
        }),
        ('Journal', {
            'fields': ('journal_id',)
        }),
    )

    def move_count(self, obj):
        count = obj.move_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    move_count.short_description = 'Moves'

    actions = ['create_debit_notes']

    def create_debit_notes(self, request, queryset):
        created_count = 0
        for debit_note in queryset:
            moves = debit_note.create_debit()
            created_count += len(moves)
        self.message_user(request, f'{created_count} debit notes created successfully.')
    create_debit_notes.short_description = "Create debit notes"


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - Invoicing"
