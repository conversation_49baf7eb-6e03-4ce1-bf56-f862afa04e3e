"""
Project Management Module Models - Complete Odoo Project Management
Based on Odoo's Project Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime, timedelta

# Import models from other modules for integration
from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountAnalyticAccount, AccountAnalyticLine
)
from hr.models import HrEmployee


class ProjectProject(models.Model):
    """Project - Based on Odoo project.project"""

    PRIVACY_VISIBILITY = [
        ('followers', 'Invited internal users'),
        ('employees', 'All internal users'),
        ('portal', 'Invited portal users and all internal users'),
    ]

    # Basic Information
    name = models.CharField(max_length=128)
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10)
    color = models.IntegerField(default=0, help_text="Color Index")

    # Description
    description = models.TextField(blank=True)

    # Dates
    date_start = models.DateField(null=True, blank=True, help_text="Start Date")
    date = models.DateField(null=True, blank=True, help_text="Expiration Date")

    # Management
    user_id = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, help_text="Project Manager")
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL, help_text="Customer")

    # Settings
    privacy_visibility = models.CharField(max_length=16, choices=PRIVACY_VISIBILITY, default='employees')

    # Accounting Integration
    analytic_account_id = models.ForeignKey(AccountAnalyticAccount, null=True, blank=True, on_delete=models.SET_NULL, help_text="Analytic Account")

    # Favorites
    favorite_user_ids = models.ManyToManyField(User, blank=True, related_name='favorite_projects', help_text="Favorite Users")

    # Task Configuration
    allow_subtasks = models.BooleanField(default=True, help_text="Sub-tasks")
    allow_recurring_tasks = models.BooleanField(default=False, help_text="Recurring Tasks")

    # Timesheet Configuration
    allow_timesheets = models.BooleanField(default=True, help_text="Timesheets")
    timesheet_encode_uom_id = models.IntegerField(null=True, blank=True, help_text="Timesheet UoM")

    # Rating
    rating_active = models.BooleanField(default=False, help_text="Customer Ratings")
    rating_status = models.CharField(max_length=16, choices=[('stage', 'Rating when changing stage'), ('periodic', 'Periodic Rating')], default='stage')
    rating_status_period = models.CharField(max_length=16, choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('bimonthly', 'Twice a Month'), ('monthly', 'Once a Month'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly')], default='monthly')

    # Computed Fields
    task_count = models.IntegerField(default=0, help_text="Task Count")
    task_ids_count = models.IntegerField(default=0)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'project_project'

    def __str__(self):
        return self.name

    def _compute_task_count(self):
        """Compute task count"""
        self.task_count = self.task_ids.filter(active=True).count()
        self.task_ids_count = self.task_count
        self.save()

    def action_view_tasks(self):
        """Return action to view project tasks"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Tasks',
            'res_model': 'project.task',
            'view_mode': 'kanban,tree,form',
            'domain': [('project_id', '=', self.id)],
        }


class ProjectTaskType(models.Model):
    """Task Type/Stage - Based on Odoo project.task.type"""

    name = models.CharField(max_length=64)
    description = models.TextField(blank=True)
    sequence = models.IntegerField(default=1)

    # Stage Properties
    fold = models.BooleanField(default=False, help_text="Folded in Kanban")

    # Projects
    project_ids = models.ManyToManyField(ProjectProject, blank=True, help_text="Projects")

    class Meta:
        db_table = 'project_task_type'
        ordering = ['sequence', 'name']

    def __str__(self):
        return self.name


class ProjectTask(models.Model):
    """Project Task - Based on Odoo project.task"""

    PRIORITIES = [
        ('0', 'Normal'),
        ('1', 'High'),
    ]

    KANBAN_STATES = [
        ('normal', 'In Progress'),
        ('done', 'Ready'),
        ('blocked', 'Blocked'),
    ]

    # Basic Information
    name = models.CharField(max_length=128)
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10)
    priority = models.CharField(max_length=1, choices=PRIORITIES, default='0')
    kanban_state = models.CharField(max_length=16, choices=KANBAN_STATES, default='normal')

    # Description
    description = models.TextField(blank=True)

    # Project and Stage
    project_id = models.ForeignKey(ProjectProject, on_delete=models.CASCADE, related_name='task_ids')
    stage_id = models.ForeignKey(ProjectTaskType, null=True, blank=True, on_delete=models.SET_NULL, help_text="Stage")

    # Assignment
    user_ids = models.ManyToManyField(User, blank=True, help_text="Assignees")

    # Dates
    date_assign = models.DateTimeField(null=True, blank=True, help_text="Assigning Date")
    date_deadline = models.DateField(null=True, blank=True, help_text="Deadline")
    date_last_stage_update = models.DateTimeField(auto_now=True, help_text="Last Stage Update")

    # Customer
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL, help_text="Customer")

    # Parent Task (Subtasks)
    parent_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='child_ids', help_text="Parent Task")
    subtask_count = models.IntegerField(default=0)

    # Dependencies
    depend_on_ids = models.ManyToManyField('self', blank=True, symmetrical=False, related_name='dependent_ids', help_text="Blocked By")

    # Time Tracking
    planned_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Initially Planned Hours")
    effective_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Hours Spent")
    remaining_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Remaining Hours")
    progress = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text="Progress (%)")
    overtime = models.DecimalField(max_digits=8, decimal_places=2, default=0, help_text="Overtime")

    # Timesheet Integration
    timesheet_ids = models.ManyToManyField(AccountAnalyticLine, blank=True, help_text="Timesheets")

    # Tags
    tag_ids = models.CharField(max_length=256, blank=True, help_text="Tags")

    # Email and Communication
    email_from = models.EmailField(blank=True, help_text="Email")
    email_cc = models.TextField(blank=True, help_text="Watchers Emails")

    # Recurring Tasks
    recurring_task = models.BooleanField(default=False, help_text="Recurring Task")
    recurring_count = models.IntegerField(default=0, help_text="Recurrence Count")

    # Rating
    rating_last_value = models.DecimalField(max_digits=3, decimal_places=2, default=0, help_text="Last Rating")

    # Personal Stage (for My Tasks view)
    personal_stage_type_ids = models.CharField(max_length=256, blank=True, help_text="Personal Stage")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'project_task'
        ordering = ['sequence', 'id']

    def __str__(self):
        return self.name

    def action_subtask(self):
        """Create subtask"""
        subtask = ProjectTask.objects.create(
            name=f"Subtask of {self.name}",
            project_id=self.project_id,
            parent_id=self,
            user_ids=self.user_ids.all(),
            partner_id=self.partner_id,
            company_id=self.company_id,
        )
        self._compute_subtask_count()
        return subtask

    def action_assign_to_me(self, user):
        """Assign task to current user"""
        self.user_ids.add(user)
        if not self.date_assign:
            self.date_assign = datetime.now()
        self.save()

    def action_done(self):
        """Mark task as done"""
        done_stage = ProjectTaskType.objects.filter(
            project_ids=self.project_id,
            name__icontains='done'
        ).first()
        if done_stage:
            self.stage_id = done_stage
            self.kanban_state = 'done'
            self.save()
            return True
        return False

    def _compute_subtask_count(self):
        """Compute subtask count"""
        self.subtask_count = self.child_ids.filter(active=True).count()
        self.save()

    def _compute_effective_hours(self):
        """Compute effective hours from timesheets"""
        total_hours = self.timesheet_ids.aggregate(
            total=models.Sum('unit_amount')
        )['total'] or 0
        self.effective_hours = total_hours

        # Update remaining hours and progress
        if self.planned_hours > 0:
            self.remaining_hours = max(0, self.planned_hours - self.effective_hours)
            self.progress = min(100, (self.effective_hours / self.planned_hours) * 100)
        else:
            self.remaining_hours = 0
            self.progress = 100 if self.effective_hours > 0 else 0

        self.save()


class ProjectMilestone(models.Model):
    """Project Milestone - Based on Odoo project.milestone"""

    name = models.CharField(max_length=128)
    project_id = models.ForeignKey(ProjectProject, on_delete=models.CASCADE, related_name='milestone_ids')

    # Dates
    deadline = models.DateField(help_text="Deadline")

    # Status
    is_reached = models.BooleanField(default=False, help_text="Is Reached")
    reached_date = models.DateField(null=True, blank=True, help_text="Reached Date")

    # Description
    description = models.TextField(blank=True)

    # Tasks
    task_ids = models.ManyToManyField(ProjectTask, blank=True, help_text="Tasks")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'project_milestone'
        ordering = ['deadline', 'name']

    def __str__(self):
        return f"{self.project_id.name} - {self.name}"

    def action_reach(self):
        """Mark milestone as reached"""
        self.is_reached = True
        self.reached_date = date.today()
        self.save()
        return True


class ProjectUpdate(models.Model):
    """Project Update - Based on Odoo project.update"""

    STATUS = [
        ('on_track', 'On Track'),
        ('at_risk', 'At Risk'),
        ('off_track', 'Off Track'),
        ('on_hold', 'On Hold'),
    ]

    name = models.CharField(max_length=128, help_text="Title")
    project_id = models.ForeignKey(ProjectProject, on_delete=models.CASCADE, related_name='update_ids')

    # Status
    status = models.CharField(max_length=16, choices=STATUS, default='on_track')

    # Content
    description = models.TextField(help_text="Description")

    # Progress
    progress = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text="Progress (%)")

    # User
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, help_text="Author")

    # Date
    date = models.DateField(default=date.today)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'project_update'
        ordering = ['-date', '-create_date']

    def __str__(self):
        return f"{self.project_id.name} - {self.name}"
