/**
 * User Preferences Component - User Settings & Preferences
 * Following Odoo's user preferences functionality
 */
import React, { useState } from 'react';
import {
  Modal,
  Form,
  Switch,
  Select,
  Slider,
  Button,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Card,
  message,
  Radio,
  InputNumber
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useAuth } from '../auth/AuthProvider';

const { Title, Text } = Typography;
const { Option } = Select;

const UserPreferences = ({ visible, onClose }) => {
  const { user, updateUser } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const defaultPreferences = {
    theme: 'light',
    language: 'en',
    timezone: 'UTC',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    currency: 'USD',
    notifications: {
      email: true,
      browser: true,
      sound: false
    },
    dashboard: {
      autoRefresh: true,
      refreshInterval: 30,
      showWelcome: true
    },
    interface: {
      sidebarCollapsed: false,
      compactMode: false,
      showTooltips: true
    }
  };

  const currentPreferences = user?.preferences || defaultPreferences;

  const handleSave = async (values) => {
    setLoading(true);
    try {
      // Mock API call - replace with real API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update user preferences
      if (updateUser) {
        updateUser({
          ...user,
          preferences: values
        });
      }
      
      message.success('Preferences saved successfully');
      onClose();
    } catch (error) {
      message.error('Failed to save preferences');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.setFieldsValue(defaultPreferences);
    message.info('Preferences reset to default');
  };

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          <span>User Preferences</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={700}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Cancel
        </Button>,
        <Button
          key="reset"
          icon={<ReloadOutlined />}
          onClick={handleReset}
        >
          Reset to Default
        </Button>,
        <Button
          key="save"
          type="primary"
          icon={<SaveOutlined />}
          loading={loading}
          onClick={() => form.submit()}
        >
          Save Preferences
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={currentPreferences}
        onFinish={handleSave}
      >
        {/* Appearance Settings */}
        <Card size="small" title="Appearance" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Theme" name="theme">
                <Radio.Group>
                  <Radio value="light">Light</Radio>
                  <Radio value="dark">Dark</Radio>
                  <Radio value="auto">Auto</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Language" name="language">
                <Select>
                  <Option value="en">English</Option>
                  <Option value="es">Spanish</Option>
                  <Option value="fr">French</Option>
                  <Option value="de">German</Option>
                  <Option value="zh">Chinese</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Regional Settings */}
        <Card size="small" title="Regional Settings" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Timezone" name="timezone">
                <Select showSearch>
                  <Option value="UTC">UTC</Option>
                  <Option value="America/New_York">Eastern Time</Option>
                  <Option value="America/Chicago">Central Time</Option>
                  <Option value="America/Denver">Mountain Time</Option>
                  <Option value="America/Los_Angeles">Pacific Time</Option>
                  <Option value="Europe/London">London</Option>
                  <Option value="Europe/Paris">Paris</Option>
                  <Option value="Asia/Tokyo">Tokyo</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Date Format" name="dateFormat">
                <Select>
                  <Option value="MM/DD/YYYY">MM/DD/YYYY</Option>
                  <Option value="DD/MM/YYYY">DD/MM/YYYY</Option>
                  <Option value="YYYY-MM-DD">YYYY-MM-DD</Option>
                  <Option value="DD-MMM-YYYY">DD-MMM-YYYY</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Time Format" name="timeFormat">
                <Select>
                  <Option value="12h">12 Hour</Option>
                  <Option value="24h">24 Hour</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Currency" name="currency">
                <Select>
                  <Option value="USD">USD - US Dollar</Option>
                  <Option value="EUR">EUR - Euro</Option>
                  <Option value="GBP">GBP - British Pound</Option>
                  <Option value="JPY">JPY - Japanese Yen</Option>
                  <Option value="CAD">CAD - Canadian Dollar</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Notification Settings */}
        <Card size="small" title="Notifications" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Email Notifications" name={['notifications', 'email']} valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Browser Notifications" name={['notifications', 'browser']} valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Sound Alerts" name={['notifications', 'sound']} valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Dashboard Settings */}
        <Card size="small" title="Dashboard" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Auto Refresh" name={['dashboard', 'autoRefresh']} valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Show Welcome Message" name={['dashboard', 'showWelcome']} valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="Refresh Interval (seconds)" name={['dashboard', 'refreshInterval']}>
                <Slider
                  min={10}
                  max={300}
                  step={10}
                  marks={{
                    10: '10s',
                    60: '1m',
                    180: '3m',
                    300: '5m'
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Interface Settings */}
        <Card size="small" title="Interface">
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Compact Mode" name={['interface', 'compactMode']} valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Show Tooltips" name={['interface', 'showTooltips']} valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Sidebar Collapsed" name={['interface', 'sidebarCollapsed']} valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>
    </Modal>
  );
};

export default UserPreferences;
