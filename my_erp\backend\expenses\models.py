"""
Expenses Module Models - Complete Odoo Expenses Management
Based on Odoo's HR Expenses Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime, timedelta

# Import models from other modules for integration
from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountTax, ProductProduct, ProductCategory
)
from hr.models import HrEmployee


class HrExpenseCategory(models.Model):
    """Expense Category - Based on Odoo hr.expense.category"""

    name = models.CharField(max_length=64)
    active = models.BooleanField(default=True)

    # Account Configuration
    account_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, help_text="Expense Account")

    # Product Integration
    product_id = models.ForeignKey(ProductProduct, null=True, blank=True, on_delete=models.SET_NULL, help_text="Related Product")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_expense_category'

    def __str__(self):
        return self.name


class HrExpense(models.Model):
    """Expense - Based on Odoo hr.expense"""

    STATES = [
        ('draft', 'To Submit'),
        ('reported', 'Submitted'),
        ('approved', 'Approved'),
        ('done', 'Paid'),
        ('refused', 'Refused'),
    ]

    PAYMENT_MODES = [
        ('own_account', 'Employee (to reimburse)'),
        ('company_account', 'Company'),
    ]

    # Basic Information
    name = models.CharField(max_length=128, help_text="Description")
    date = models.DateField(default=date.today, help_text="Expense Date")
    employee_id = models.ForeignKey(HrEmployee, on_delete=models.CASCADE, help_text="Employee")

    # Product and Category
    product_id = models.ForeignKey(ProductProduct, on_delete=models.CASCADE, help_text="Product")
    category_id = models.ForeignKey(HrExpenseCategory, null=True, blank=True, on_delete=models.SET_NULL, help_text="Category")

    # Amount and Currency
    unit_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Unit Price")
    quantity = models.DecimalField(max_digits=16, decimal_places=4, default=1, help_text="Quantity")
    total_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Total")
    untaxed_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Untaxed Amount")
    currency_id = models.CharField(max_length=3, default='PKR', help_text="Currency")

    # Tax
    tax_ids = models.ManyToManyField(AccountTax, blank=True, help_text="Taxes")

    # Payment
    payment_mode = models.CharField(max_length=16, choices=PAYMENT_MODES, default='own_account')

    # State and Approval
    state = models.CharField(max_length=16, choices=STATES, default='draft')

    # Accounting
    account_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, help_text="Account")
    analytic_account_id = models.ForeignKey('accounting.AccountAnalyticAccount', null=True, blank=True, on_delete=models.SET_NULL, help_text="Analytic Account")

    # Reference
    reference = models.CharField(max_length=64, blank=True, help_text="Bill Reference")

    # Attachment
    attachment_number = models.IntegerField(default=0, help_text="Number of Attachments")

    # Sheet Reference
    sheet_id = models.ForeignKey('HrExpenseSheet', null=True, blank=True, on_delete=models.CASCADE, help_text="Expense Report")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_expense'

    def __str__(self):
        return f"{self.name} - {self.employee_id.name}"

    def save(self, *args, **kwargs):
        # Calculate total amount
        self.total_amount = self.unit_amount * self.quantity

        # Save first to get an ID
        super().save(*args, **kwargs)

        # Calculate untaxed amount (simplified) - only if object has been saved
        if self.pk:
            tax_amount = 0
            for tax in self.tax_ids.all():
                if tax.amount_type == 'percent':
                    tax_amount += (self.total_amount * tax.amount) / 100

            self.untaxed_amount = self.total_amount - tax_amount

            # Save again if untaxed amount changed
            if self.untaxed_amount != self.total_amount:
                super().save(update_fields=['untaxed_amount'])

    def action_submit_expenses(self):
        """Submit expense for approval"""
        if self.state == 'draft':
            self.state = 'reported'
            self.save()
            return True
        return False

    def action_approve_expenses(self):
        """Approve expense"""
        if self.state == 'reported':
            self.state = 'approved'
            self.save()
            return True
        return False

    def action_refuse_expenses(self):
        """Refuse expense"""
        if self.state in ['reported', 'approved']:
            self.state = 'refused'
            self.save()
            return True
        return False

    def action_reset_to_draft(self):
        """Reset to draft"""
        if self.state in ['refused', 'reported']:
            self.state = 'draft'
            self.save()
            return True
        return False


class HrExpenseSheet(models.Model):
    """Expense Sheet/Report - Based on Odoo hr.expense.sheet"""

    STATES = [
        ('draft', 'Draft'),
        ('submit', 'Submitted'),
        ('approve', 'Approved'),
        ('post', 'Posted'),
        ('done', 'Paid'),
        ('cancel', 'Refused'),
    ]

    # Basic Information
    name = models.CharField(max_length=128, help_text="Expense Report Summary")
    employee_id = models.ForeignKey(HrEmployee, on_delete=models.CASCADE, help_text="Employee")

    # Dates
    accounting_date = models.DateField(null=True, blank=True, help_text="Accounting Date")

    # State
    state = models.CharField(max_length=16, choices=STATES, default='draft')

    # Amounts
    total_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Total Amount")
    untaxed_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Untaxed Amount")

    # Journal and Payment
    bank_journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, help_text="Bank Journal")

    # Accounting Integration
    account_move_id = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.SET_NULL, help_text="Journal Entry")

    # Approval
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, help_text="Manager")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_expense_sheet'

    def __str__(self):
        return f"{self.name} - {self.employee_id.name}"

    def save(self, *args, **kwargs):
        # Calculate totals from related expenses
        if self.pk:
            expenses = self.hrexpense_set.all()
            self.total_amount = sum(exp.total_amount for exp in expenses)
            self.untaxed_amount = sum(exp.untaxed_amount for exp in expenses)

        super().save(*args, **kwargs)

    def action_submit_sheet(self):
        """Submit expense sheet"""
        if self.state == 'draft':
            self.state = 'submit'
            # Update related expenses
            self.hrexpense_set.update(state='reported')
            self.save()
            return True
        return False

    def approve_expense_sheets(self):
        """Approve expense sheet"""
        if self.state == 'submit':
            self.state = 'approve'
            # Update related expenses
            self.hrexpense_set.update(state='approved')
            self.save()
            return True
        return False

    def action_sheet_move_create(self):
        """Create accounting move for expense sheet"""
        if self.state == 'approve' and not self.account_move_id:
            # Create journal entry
            move = AccountMove.objects.create(
                move_type='entry',
                date=self.accounting_date or date.today(),
                ref=f"Expense: {self.name}",
                journal_id=self.bank_journal_id,
                company_id=self.company_id,
            )

            # Create move lines for each expense
            for expense in self.hrexpense_set.all():
                # Expense line (debit)
                AccountMoveLine.objects.create(
                    move_id=move,
                    account_id=expense.account_id,
                    partner_id=expense.employee_id.user_id.partner_id if expense.employee_id.user_id else None,
                    name=expense.name,
                    debit=expense.total_amount,
                    credit=0,
                    analytic_account_id=expense.analytic_account_id,
                )

                # Payable line (credit) - if employee reimbursement
                if expense.payment_mode == 'own_account':
                    payable_account = AccountAccount.objects.filter(
                        account_type='liability_payable',
                        company_id=self.company_id
                    ).first()

                    if payable_account:
                        AccountMoveLine.objects.create(
                            move_id=move,
                            account_id=payable_account,
                            partner_id=expense.employee_id.user_id.partner_id if expense.employee_id.user_id else None,
                            name=f"Reimbursement: {expense.name}",
                            debit=0,
                            credit=expense.total_amount,
                        )

            # Post the move
            move.action_post()

            self.account_move_id = move
            self.state = 'post'
            self.save()

            return move
        return None

    def action_pay_expense_sheet(self):
        """Mark expense sheet as paid"""
        if self.state == 'post':
            self.state = 'done'
            # Update related expenses
            self.hrexpense_set.update(state='done')
            self.save()
            return True
        return False

    def refuse_sheet(self, reason=''):
        """Refuse expense sheet"""
        if self.state in ['submit', 'approve']:
            self.state = 'cancel'
            # Update related expenses
            self.hrexpense_set.update(state='refused')
            self.save()
            return True
        return False


class HrExpenseRefuseWizard(models.Model):
    """Expense Refuse Wizard - Based on Odoo hr.expense.refuse.wizard"""

    reason = models.TextField(help_text="Reason")
    expense_sheet_ids = models.ManyToManyField(HrExpenseSheet, help_text="Expense Reports")

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_expense_refuse_wizard'

    def __str__(self):
        return f"Refuse Wizard - {self.reason[:50]}"

    def expense_refuse_reason(self):
        """Refuse expenses with reason"""
        for sheet in self.expense_sheet_ids.all():
            sheet.refuse_sheet(self.reason)
        return True


class HrExpenseApprovalWizard(models.Model):
    """Expense Approval Wizard - Based on Odoo expense approval"""

    expense_sheet_ids = models.ManyToManyField(HrExpenseSheet, help_text="Expense Reports")

    # Approval Settings
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, help_text="Approved By")
    approval_date = models.DateTimeField(default=datetime.now, help_text="Approval Date")

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_expense_approval_wizard'

    def __str__(self):
        return f"Approval Wizard - {self.user_id.username}"

    def approve_expense_sheets(self):
        """Approve multiple expense sheets"""
        approved_count = 0
        for sheet in self.expense_sheet_ids.all():
            if sheet.approve_expense_sheets():
                approved_count += 1
        return approved_count
