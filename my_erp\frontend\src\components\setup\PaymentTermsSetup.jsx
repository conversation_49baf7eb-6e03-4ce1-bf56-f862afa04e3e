/**
 * Payment Terms Setup Component
 */
import React from 'react';
import { Card, Typography, Alert } from 'antd';
import { CreditCardOutlined } from '@ant-design/icons';

const { Title } = Typography;

const PaymentTermsSetup = () => {
  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <CreditCardOutlined style={{ marginRight: 8 }} />
        Payment Terms Setup
      </Title>
      
      <Card>
        <Alert
          message="Coming Soon"
          description="Payment Terms Setup will be available soon."
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
};

export default PaymentTermsSetup;
