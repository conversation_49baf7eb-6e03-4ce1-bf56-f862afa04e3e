# Generated by Django 4.2.21 on 2025-07-16 02:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.RenameField(
            model_name='userprofile',
            old_name='is_super_user',
            new_name='is_company_super_user',
        ),
        migrations.AddField(
            model_name='userprofile',
            name='is_portal_user',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='user_type',
            field=models.CharField(choices=[('system_super_user', 'System Super User'), ('company_super_user', 'Company Super User'), ('company_admin', 'Company Administrator'), ('user', 'Regular User'), ('portal', 'Portal User')], default='user', max_length=20),
        ),
        migrations.CreateModel(
            name='CompanyUserMembership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_id', models.IntegerField()),
                ('role', models.CharField(choices=[('owner', 'Company Owner'), ('manager', 'Department Manager'), ('employee', 'Employee'), ('external', 'External User')], default='employee', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('can_manage_users', models.BooleanField(default=False)),
                ('can_access_all_modules', models.BooleanField(default=False)),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('added_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='added_company_users', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='company_memberships', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Company User Membership',
                'verbose_name_plural': 'Company User Memberships',
                'db_table': 'company_user_memberships',
                'unique_together': {('user', 'company_id')},
            },
        ),
    ]
