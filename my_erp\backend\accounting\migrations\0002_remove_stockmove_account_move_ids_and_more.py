# Generated by Django 4.2.21 on 2025-07-15 18:49

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('accounting', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='stockmove',
            name='account_move_ids',
        ),
        migrations.RemoveField(
            model_name='stockmove',
            name='company_id',
        ),
        migrations.RemoveField(
            model_name='stockmove',
            name='location_dest_id',
        ),
        migrations.RemoveField(
            model_name='stockmove',
            name='location_id',
        ),
        migrations.RemoveField(
            model_name='stockmove',
            name='partner_id',
        ),
        migrations.RemoveField(
            model_name='stockmove',
            name='product_id',
        ),
        migrations.RemoveField(
            model_name='stockwarehouse',
            name='company_id',
        ),
        migrations.RemoveField(
            model_name='stockwarehouse',
            name='lot_stock_id',
        ),
        migrations.RemoveField(
            model_name='stockwarehouse',
            name='partner_id',
        ),
        migrations.RemoveField(
            model_name='stockwarehouse',
            name='view_location_id',
        ),
        migrations.RemoveField(
            model_name='stockwarehouse',
            name='wh_input_stock_loc_id',
        ),
        migrations.RemoveField(
            model_name='stockwarehouse',
            name='wh_output_stock_loc_id',
        ),
        migrations.RemoveField(
            model_name='stockwarehouse',
            name='wh_pack_stock_loc_id',
        ),
        migrations.RemoveField(
            model_name='stockwarehouse',
            name='wh_qc_stock_loc_id',
        ),
        migrations.DeleteModel(
            name='StockLocation',
        ),
        migrations.DeleteModel(
            name='StockMove',
        ),
        migrations.DeleteModel(
            name='StockWarehouse',
        ),
    ]
