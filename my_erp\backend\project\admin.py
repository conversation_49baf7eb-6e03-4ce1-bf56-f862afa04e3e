"""
Project Management Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from datetime import date
from .models import ProjectProject, ProjectTaskType, ProjectTask, ProjectMilestone, ProjectUpdate


class ProjectTaskInline(admin.TabularInline):
    model = ProjectTask
    extra = 0
    fields = ['name', 'stage_id', 'priority', 'date_deadline', 'planned_hours', 'effective_hours']
    readonly_fields = ['effective_hours']


@admin.register(ProjectProject)
class ProjectProjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'user_id', 'partner_id', 'date_start', 'date', 'task_count_display', 'active']
    list_filter = ['active', 'privacy_visibility', 'allow_timesheets', 'company_id']
    search_fields = ['name', 'description', 'partner_id__name']
    inlines = [ProjectTaskInline]
    filter_horizontal = ['favorite_user_ids']
    ordering = ['-create_date', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'active', 'sequence', 'color')
        }),
        ('Description', {
            'fields': ('description',)
        }),
        ('Dates', {
            'fields': ('date_start', 'date')
        }),
        ('Management', {
            'fields': ('user_id', 'partner_id')
        }),
        ('Settings', {
            'fields': ('privacy_visibility', 'allow_subtasks', 'allow_recurring_tasks')
        }),
        ('Timesheet', {
            'fields': ('allow_timesheets', 'timesheet_encode_uom_id'),
            'classes': ('collapse',)
        }),
        ('Rating', {
            'fields': ('rating_active', 'rating_status', 'rating_status_period'),
            'classes': ('collapse',)
        }),
        ('Accounting', {
            'fields': ('analytic_account_id',),
            'classes': ('collapse',)
        }),
        ('Favorites', {
            'fields': ('favorite_user_ids',),
            'classes': ('collapse',)
        }),
    )

    def task_count_display(self, obj):
        return format_html('<span style="font-weight: bold;">{}</span>', obj.task_count)
    task_count_display.short_description = 'Tasks'


@admin.register(ProjectTaskType)
class ProjectTaskTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'sequence', 'fold', 'project_count']
    list_filter = ['fold']
    search_fields = ['name', 'description']
    filter_horizontal = ['project_ids']
    ordering = ['sequence', 'name']

    def project_count(self, obj):
        count = obj.project_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    project_count.short_description = 'Projects'


@admin.register(ProjectTask)
class ProjectTaskAdmin(admin.ModelAdmin):
    list_display = ['name', 'project_id', 'stage_id', 'priority_display', 'assignees_display', 'date_deadline', 'progress_display', 'active']
    list_filter = ['active', 'priority', 'kanban_state', 'project_id', 'stage_id', 'company_id']
    search_fields = ['name', 'description', 'project_id__name']
    filter_horizontal = ['user_ids', 'depend_on_ids', 'timesheet_ids']
    ordering = ['sequence', 'project_id', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'active', 'sequence', 'priority', 'kanban_state')
        }),
        ('Project & Stage', {
            'fields': ('project_id', 'stage_id')
        }),
        ('Assignment', {
            'fields': ('user_ids', 'date_assign')
        }),
        ('Dates', {
            'fields': ('date_deadline', 'date_last_stage_update')
        }),
        ('Customer', {
            'fields': ('partner_id',)
        }),
        ('Subtasks', {
            'fields': ('parent_id', 'subtask_count'),
            'classes': ('collapse',)
        }),
        ('Dependencies', {
            'fields': ('depend_on_ids',),
            'classes': ('collapse',)
        }),
        ('Time Tracking', {
            'fields': ('planned_hours', 'effective_hours', 'remaining_hours', 'progress', 'overtime')
        }),
        ('Timesheets', {
            'fields': ('timesheet_ids',),
            'classes': ('collapse',)
        }),
        ('Description', {
            'fields': ('description',),
            'classes': ('collapse',)
        }),
        ('Tags & Communication', {
            'fields': ('tag_ids', 'email_from', 'email_cc'),
            'classes': ('collapse',)
        }),
        ('Recurring', {
            'fields': ('recurring_task', 'recurring_count'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['effective_hours', 'subtask_count', 'date_last_stage_update', 'date_assign']

    def priority_display(self, obj):
        colors = {'0': 'green', '1': 'red'}
        labels = {'0': 'Normal', '1': 'High'}
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.priority, 'black'), labels.get(obj.priority, obj.priority)
        )
    priority_display.short_description = 'Priority'

    def assignees_display(self, obj):
        assignees = obj.user_ids.all()[:3]  # Show first 3
        names = [user.username for user in assignees]
        if obj.user_ids.count() > 3:
            names.append(f'+{obj.user_ids.count() - 3} more')
        return ', '.join(names) if names else '-'
    assignees_display.short_description = 'Assignees'

    def progress_display(self, obj):
        color = 'green' if obj.progress >= 75 else 'orange' if obj.progress >= 50 else 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color, obj.progress
        )
    progress_display.short_description = 'Progress'

    actions = ['action_assign_to_me', 'action_mark_done']

    def action_assign_to_me(self, request, queryset):
        assigned = 0
        for task in queryset:
            task.action_assign_to_me(request.user)
            assigned += 1
        self.message_user(request, f'{assigned} tasks assigned to you successfully.')
    action_assign_to_me.short_description = "Assign selected tasks to me"

    def action_mark_done(self, request, queryset):
        done = 0
        for task in queryset:
            if task.action_done():
                done += 1
        self.message_user(request, f'{done} tasks marked as done successfully.')
    action_mark_done.short_description = "Mark selected tasks as done"


@admin.register(ProjectMilestone)
class ProjectMilestoneAdmin(admin.ModelAdmin):
    list_display = ['name', 'project_id', 'deadline', 'is_reached_display', 'reached_date', 'task_count']
    list_filter = ['is_reached', 'deadline', 'company_id']
    search_fields = ['name', 'description', 'project_id__name']
    filter_horizontal = ['task_ids']
    ordering = ['deadline', 'project_id', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'project_id')
        }),
        ('Dates', {
            'fields': ('deadline', 'is_reached', 'reached_date')
        }),
        ('Description', {
            'fields': ('description',)
        }),
        ('Tasks', {
            'fields': ('task_ids',)
        }),
    )

    readonly_fields = ['reached_date']

    def is_reached_display(self, obj):
        if obj.is_reached:
            return format_html('<span style="color: green; font-weight: bold;">✓ Reached</span>')
        elif obj.deadline < date.today():
            return format_html('<span style="color: red; font-weight: bold;">✗ Overdue</span>')
        else:
            return format_html('<span style="color: orange;">⏳ Pending</span>')
    is_reached_display.short_description = 'Status'

    def task_count(self, obj):
        count = obj.task_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    task_count.short_description = 'Tasks'

    actions = ['action_mark_reached']

    def action_mark_reached(self, request, queryset):
        reached = 0
        for milestone in queryset:
            if milestone.action_reach():
                reached += 1
        self.message_user(request, f'{reached} milestones marked as reached successfully.')
    action_mark_reached.short_description = "Mark selected milestones as reached"


@admin.register(ProjectUpdate)
class ProjectUpdateAdmin(admin.ModelAdmin):
    list_display = ['name', 'project_id', 'status_display', 'progress_display', 'user_id', 'date']
    list_filter = ['status', 'date', 'company_id']
    search_fields = ['name', 'description', 'project_id__name']
    ordering = ['-date', '-create_date']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'project_id', 'user_id')
        }),
        ('Status', {
            'fields': ('status', 'progress', 'date')
        }),
        ('Description', {
            'fields': ('description',)
        }),
    )

    def status_display(self, obj):
        colors = {
            'on_track': 'green',
            'at_risk': 'orange',
            'off_track': 'red',
            'on_hold': 'gray'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.status, 'black'), obj.get_status_display()
        )
    status_display.short_description = 'Status'

    def progress_display(self, obj):
        color = 'green' if obj.progress >= 75 else 'orange' if obj.progress >= 50 else 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color, obj.progress
        )
    progress_display.short_description = 'Progress'


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - Project Management"
