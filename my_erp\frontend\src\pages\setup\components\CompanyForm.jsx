/**
 * Company Form Component - Create/Edit Company Form
 */
import React, { useEffect } from 'react';
import { Form, Row, Col } from 'antd';
import FormWrapper from '../../../components/shared/forms/FormWrapper';
import { 
  TextField, 
  TextAreaField, 
  SelectField, 
  SwitchField,
  UploadField 
} from '../../../components/shared/forms/FormField';

const CompanyForm = ({ 
  initialValues, 
  onSubmit, 
  onCancel, 
  loading = false 
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, form]);

  const handleSubmit = (values) => {
    onSubmit(values);
  };

  // Country options (simplified list)
  const countryOptions = [
    { value: 'US', label: 'United States' },
    { value: 'CA', label: 'Canada' },
    { value: 'UK', label: 'United Kingdom' },
    { value: 'AU', label: 'Australia' },
    { value: 'DE', label: 'Germany' },
    { value: 'FR', label: 'France' },
    { value: 'IN', label: 'India' },
    { value: 'CN', label: 'China' },
    { value: 'JP', label: 'Japan' },
    { value: 'BR', label: 'Brazil' },
  ];

  // Currency options
  const currencyOptions = [
    { value: 'USD', label: 'USD - US Dollar' },
    { value: 'EUR', label: 'EUR - Euro' },
    { value: 'GBP', label: 'GBP - British Pound' },
    { value: 'CAD', label: 'CAD - Canadian Dollar' },
    { value: 'AUD', label: 'AUD - Australian Dollar' },
    { value: 'JPY', label: 'JPY - Japanese Yen' },
    { value: 'INR', label: 'INR - Indian Rupee' },
    { value: 'CNY', label: 'CNY - Chinese Yuan' },
  ];

  // Timezone options (simplified)
  const timezoneOptions = [
    { value: 'UTC', label: 'UTC - Coordinated Universal Time' },
    { value: 'America/New_York', label: 'Eastern Time (US & Canada)' },
    { value: 'America/Chicago', label: 'Central Time (US & Canada)' },
    { value: 'America/Denver', label: 'Mountain Time (US & Canada)' },
    { value: 'America/Los_Angeles', label: 'Pacific Time (US & Canada)' },
    { value: 'Europe/London', label: 'London' },
    { value: 'Europe/Paris', label: 'Paris' },
    { value: 'Asia/Tokyo', label: 'Tokyo' },
    { value: 'Asia/Shanghai', label: 'Shanghai' },
    { value: 'Asia/Kolkata', label: 'Mumbai, Kolkata' },
  ];

  return (
    <FormWrapper
      title={null}
      form={form}
      onFinish={handleSubmit}
      onCancel={onCancel}
      loading={loading}
      submitText={initialValues ? 'Update Company' : 'Create Company'}
      showReset={true}
    >
      {/* Basic Information */}
      <Row gutter={16}>
        <Col span={12}>
          <TextField
            name="name"
            label="Company Name"
            required
            placeholder="Enter company name"
          />
        </Col>
        <Col span={12}>
          <TextField
            name="legal_name"
            label="Legal Name"
            placeholder="Enter legal company name"
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <TextField
            name="email"
            label="Email"
            required
            rules={[
              { type: 'email', message: 'Please enter a valid email address' }
            ]}
            placeholder="<EMAIL>"
          />
        </Col>
        <Col span={12}>
          <TextField
            name="phone"
            label="Phone"
            placeholder="Enter phone number"
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <TextField
            name="website"
            label="Website"
            placeholder="https://www.example.com"
          />
        </Col>
        <Col span={12}>
          <TextField
            name="tax_id"
            label="Tax ID / VAT Number"
            placeholder="Enter tax identification number"
          />
        </Col>
      </Row>

      {/* Address Information */}
      <Row gutter={16}>
        <Col span={24}>
          <TextAreaField
            name="address"
            label="Address"
            placeholder="Enter complete address"
            rows={3}
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <TextField
            name="city"
            label="City"
            placeholder="Enter city"
          />
        </Col>
        <Col span={8}>
          <TextField
            name="state"
            label="State/Province"
            placeholder="Enter state or province"
          />
        </Col>
        <Col span={8}>
          <TextField
            name="zip_code"
            label="ZIP/Postal Code"
            placeholder="Enter ZIP or postal code"
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <SelectField
            name="country"
            label="Country"
            required
            options={countryOptions}
            placeholder="Select country"
          />
        </Col>
        <Col span={12}>
          <SelectField
            name="currency"
            label="Currency"
            required
            options={currencyOptions}
            placeholder="Select currency"
          />
        </Col>
      </Row>

      {/* Settings */}
      <Row gutter={16}>
        <Col span={12}>
          <SelectField
            name="timezone"
            label="Timezone"
            options={timezoneOptions}
            placeholder="Select timezone"
          />
        </Col>
        <Col span={12}>
          <TextField
            name="fiscal_year_end"
            label="Fiscal Year End"
            placeholder="MM-DD (e.g., 12-31)"
          />
        </Col>
      </Row>

      {/* Logo Upload */}
      <Row gutter={16}>
        <Col span={12}>
          <UploadField
            name="logo"
            label="Company Logo"
            accept="image/*"
            listType="picture"
            maxCount={1}
          />
        </Col>
        <Col span={12}>
          <div style={{ paddingTop: '30px' }}>
            <SwitchField
              name="active"
              label="Active"
              checkedChildren="Active"
              unCheckedChildren="Inactive"
            />
          </div>
        </Col>
      </Row>

      {/* Additional Information */}
      <Row gutter={16}>
        <Col span={24}>
          <TextAreaField
            name="description"
            label="Description"
            placeholder="Enter company description"
            rows={3}
          />
        </Col>
      </Row>
    </FormWrapper>
  );
};

export default CompanyForm;
