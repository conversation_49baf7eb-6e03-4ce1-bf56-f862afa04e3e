<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!-- GENERAL LEDGER -->
    <record id="general_ledger_wizard" model="ir.ui.view">
        <field name="name">General Ledger</field>
        <field name="model">general.ledger.report.wizard</field>
        <field name="arch" type="xml">
            <form>
                <group name="main_info">
                    <field
                        name="company_id"
                        options="{'no_create': True}"
                        groups="base.group_multi_company"
                    />
                </group>
                <div invisible="not only_one_unaffected_earnings_account">
                    <group name="filters">
                        <group name="date_range">
                            <field name="date_range_id" />
                            <field name="date_from" />
                            <field name="date_to" />
                            <field name="fy_start_date" invisible="1" />
                            <field name="target_move" widget="radio" />
                        </group>
                        <group name="other_filters">
                            <field name="grouped_by" />
                            <field name="centralize" />
                            <field name="hide_account_at_0" />
                            <field name="foreign_currency" />
                            <field name="show_cost_center" />
                        </group>
                    </group>
                    <notebook>
                        <page string="Filter accounts">
                            <group name="account_filter" col="4">
                                <label for="account_ids" colspan="4" />
                                <field name="receivable_accounts_only" />
                                <field name="payable_accounts_only" />
                                <label for="account_code_from" string="From Code" />
                                <div>
                                    <div class="o_row">
                                        <field
                                            name="account_code_from"
                                            class="oe_inline"
                                            options="{'no_create': True}"
                                        />
                                        <span class="oe_inline">To</span>
                                        <field
                                            name="account_code_to"
                                            class="oe_inline"
                                            options="{'no_create': True}"
                                        />
                                    </div>
                                </div>
                                <field
                                    name="account_ids"
                                    nolabel="1"
                                    widget="many2many_tags"
                                    options="{'no_create': True}"
                                    colspan="4"
                                />
                            </group>
                        </page>
                        <page string="Filter partners">
                            <field
                                name="partner_ids"
                                nolabel="1"
                                widget="many2many_tags"
                                options="{'no_create': True}"
                            />
                        </page>
                        <page
                            string="Filter analytic accounts"
                            groups="analytic.group_analytic_accounting"
                        >
                            <field
                                name="cost_center_ids"
                                nolabel="1"
                                widget="many2many_tags"
                                options="{'no_create': True}"
                            />
                        </page>
                        <page string="Additional Filtering">
                            <style>
                                .o_domain_show_selection_button {display: none}
                            </style>
                            <field
                                name="domain"
                                widget="domain"
                                options="{'model': 'account.move.line', 'in_dialog': True}"
                                context="{'skip_search_count': 1}"
                            />
                        </page>
                    </notebook>
                </div>
                <div invisible="only_one_unaffected_earnings_account">
                    <field name="only_one_unaffected_earnings_account" invisible="1" />
                    <group />
                    <h4>
                        General Ledger can be computed only if selected company have
                        only one unaffected earnings account.
                    </h4>
                    <group />
                </div>
                <footer>
                    <div invisible="not only_one_unaffected_earnings_account">
                        <button
                        name="button_export_html"
                        string="View"
                        type="object"
                        default_focus="1"
                        class="oe_highlight"
                    />
                        or
                        <button
                        name="button_export_pdf"
                        string="Export PDF"
                        type="object"
                    />
                        or
                        <button
                        name="button_export_xlsx"
                        string="Export XLSX"
                        type="object"
                    />
                        or
                        <button string="Cancel" class="oe_link" special="cancel" />
                    </div>
                    <div invisible="only_one_unaffected_earnings_account">
                        <button string="Cancel" class="oe_link" special="cancel" />
                    </div>
                </footer>
            </form>
        </field>
    </record>
    <record id="action_general_ledger_wizard" model="ir.actions.act_window">
        <field name="name">General Ledger</field>
        <field name="res_model">general.ledger.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="general_ledger_wizard" />
        <field name="target">new</field>
    </record>
    <!--Add to res.partner action-->
    <record
        id="act_action_general_ledger_wizard_partner_relation"
        model="ir.actions.act_window"
    >
        <field name="name">General Ledger</field>
        <field name="res_model">general.ledger.report.wizard</field>
        <field name="binding_model_id" ref="base.model_res_partner" />
        <field name="view_mode">form</field>
        <field name="view_id" ref="general_ledger_wizard" />
        <field
            name="context"
            eval="{
                'default_receivable_accounts_only':1,
                'default_payable_accounts_only':1,
            }"
        />
        <field name="groups_id" eval="[(4, ref('account.group_account_manager'))]" />
        <field name="target">new</field>
    </record>
</odoo>
