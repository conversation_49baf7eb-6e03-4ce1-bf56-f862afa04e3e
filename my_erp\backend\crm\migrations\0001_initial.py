# Generated by Django 4.2.21 on 2025-07-15 19:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0002_remove_stockmove_account_move_ids_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CrmLostReason',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'crm_lost_reason',
            },
        ),
        migrations.CreateModel(
            name='CrmTeam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('use_leads', models.BooleanField(default=True, help_text='Use Leads')),
                ('use_opportunities', models.BooleanField(default=True, help_text='Use Opportunities')),
                ('invoiced_target', models.DecimalField(decimal_places=2, default=0, help_text='Monthly Revenue Target', max_digits=16)),
                ('quotation_count', models.IntegerField(default=0, help_text='Number of Quotations')),
                ('sale_order_count', models.IntegerField(default=0, help_text='Number of Sales Orders')),
                ('assignment_enabled', models.BooleanField(default=False, help_text='Lead Assignment')),
                ('assignment_auto_enabled', models.BooleanField(default=False, help_text='Automatic Assignment')),
                ('assignment_optout', models.BooleanField(default=False, help_text='Skip Auto Assignment')),
                ('domain', models.TextField(blank=True, help_text='Domain Filter')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('member_ids', models.ManyToManyField(blank=True, help_text='Team Members', related_name='crm_teams', to=settings.AUTH_USER_MODEL)),
                ('user_id', models.ForeignKey(blank=True, help_text='Team Leader', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'crm_team',
            },
        ),
        migrations.CreateModel(
            name='CrmStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('sequence', models.IntegerField(default=1)),
                ('is_won', models.BooleanField(default=False, help_text='Is Won Stage')),
                ('fold', models.BooleanField(default=False, help_text='Folded in Kanban')),
                ('requirements', models.TextField(blank=True, help_text='Requirements')),
                ('team_ids', models.ManyToManyField(blank=True, help_text='Teams', to='crm.crmteam')),
            ],
            options={
                'db_table': 'crm_stage',
                'ordering': ['sequence', 'name'],
            },
        ),
        migrations.CreateModel(
            name='CrmLead',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Subject', max_length=128)),
                ('active', models.BooleanField(default=True)),
                ('type', models.CharField(choices=[('lead', 'Lead'), ('opportunity', 'Opportunity')], default='lead', max_length=12)),
                ('priority', models.CharField(choices=[('0', 'Low'), ('1', 'Normal'), ('2', 'High'), ('3', 'Very High')], default='1', max_length=1)),
                ('partner_name', models.CharField(blank=True, help_text='Customer Name', max_length=128)),
                ('contact_name', models.CharField(blank=True, help_text='Contact Name', max_length=128)),
                ('email_from', models.EmailField(blank=True, help_text='Email', max_length=254)),
                ('phone', models.CharField(blank=True, help_text='Phone', max_length=32)),
                ('mobile', models.CharField(blank=True, help_text='Mobile', max_length=32)),
                ('website', models.URLField(blank=True, help_text='Website')),
                ('street', models.CharField(blank=True, max_length=128)),
                ('street2', models.CharField(blank=True, max_length=128)),
                ('city', models.CharField(blank=True, max_length=64)),
                ('zip', models.CharField(blank=True, max_length=16)),
                ('expected_revenue', models.DecimalField(decimal_places=2, default=0, help_text='Expected Revenue', max_digits=16)),
                ('prorated_revenue', models.DecimalField(decimal_places=2, default=0, help_text='Prorated Revenue', max_digits=16)),
                ('recurring_revenue', models.DecimalField(decimal_places=2, default=0, help_text='Recurring Revenue', max_digits=16)),
                ('recurring_plan', models.CharField(blank=True, choices=[('monthly', 'Monthly'), ('yearly', 'Yearly')], max_length=16)),
                ('probability', models.DecimalField(decimal_places=2, default=0, help_text='Success Rate (%)', max_digits=5)),
                ('automated_probability', models.DecimalField(decimal_places=2, default=0, help_text='Automated Probability', max_digits=5)),
                ('date_deadline', models.DateField(blank=True, help_text='Expected Closing', null=True)),
                ('date_closed', models.DateTimeField(blank=True, help_text='Closed Date', null=True)),
                ('date_conversion', models.DateTimeField(blank=True, help_text='Conversion Date', null=True)),
                ('date_last_stage_update', models.DateTimeField(auto_now=True, help_text='Last Stage Update')),
                ('source_id', models.IntegerField(blank=True, help_text='Source', null=True)),
                ('medium_id', models.IntegerField(blank=True, help_text='Medium', null=True)),
                ('campaign_id', models.IntegerField(blank=True, help_text='Campaign', null=True)),
                ('referred', models.CharField(blank=True, help_text='Referred By', max_length=128)),
                ('description', models.TextField(blank=True, help_text='Notes')),
                ('tag_ids', models.CharField(blank=True, help_text='Tags', max_length=256)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('country_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.rescountry')),
                ('lost_reason_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='crm.crmlostreason')),
                ('partner_id', models.ForeignKey(blank=True, help_text='Customer', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('stage_id', models.ForeignKey(blank=True, help_text='Stage', null=True, on_delete=django.db.models.deletion.SET_NULL, to='crm.crmstage')),
                ('state_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.rescountrystate')),
                ('team_id', models.ForeignKey(blank=True, help_text='Sales Team', null=True, on_delete=django.db.models.deletion.SET_NULL, to='crm.crmteam')),
                ('user_id', models.ForeignKey(blank=True, help_text='Salesperson', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'crm_lead',
                'ordering': ['-create_date', '-write_date'],
            },
        ),
        migrations.CreateModel(
            name='CrmActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('call', 'Call'), ('meeting', 'Meeting'), ('email', 'Email'), ('todo', 'To Do'), ('upload_file', 'Upload File')], default='call', max_length=16)),
                ('summary', models.CharField(help_text='Summary', max_length=128)),
                ('note', models.TextField(blank=True, help_text='Note')),
                ('date_deadline', models.DateField(help_text='Due Date')),
                ('state', models.CharField(choices=[('overdue', 'Overdue'), ('today', 'Today'), ('planned', 'Planned'), ('done', 'Done')], default='planned', max_length=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('create_uid', models.ForeignKey(help_text='Created by', on_delete=django.db.models.deletion.CASCADE, related_name='created_activities', to=settings.AUTH_USER_MODEL)),
                ('lead_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_ids', to='crm.crmlead')),
                ('user_id', models.ForeignKey(help_text='Assigned to', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'crm_activity',
                'ordering': ['date_deadline', 'create_date'],
            },
        ),
    ]
