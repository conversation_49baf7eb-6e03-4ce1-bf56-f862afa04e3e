/**
 * Company Setup Page - Create and manage companies
 */
import React, { useState, useEffect } from 'react';
import { Table, Space, Modal, message, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { PageHeader, PageContent, QuickActions } from '../../components/shared/layout/PageLayout';
import { setupAPI } from '../../services/api';
import CompanyForm from './components/CompanyForm';

const CompanySetup = () => {
  const navigate = useNavigate();
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCompany, setEditingCompany] = useState(null);
  const [formLoading, setFormLoading] = useState(false);

  // Fetch companies
  const fetchCompanies = async () => {
    setLoading(true);
    try {
      const response = await setupAPI.getCompanies();
      setCompanies(response.data || []);
    } catch (error) {
      message.error('Failed to fetch companies');
      console.error('Fetch companies error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCompanies();
  }, []);

  // Handle create company
  const handleCreate = () => {
    setEditingCompany(null);
    setModalVisible(true);
  };

  // Handle edit company
  const handleEdit = (company) => {
    setEditingCompany(company);
    setModalVisible(true);
  };

  // Handle delete company
  const handleDelete = async (id) => {
    try {
      await setupAPI.deleteCompany(id);
      message.success('Company deleted successfully');
      fetchCompanies();
    } catch (error) {
      message.error('Failed to delete company');
      console.error('Delete company error:', error);
    }
  };

  // Handle form submit
  const handleFormSubmit = async (values) => {
    setFormLoading(true);
    try {
      if (editingCompany) {
        await setupAPI.updateCompany(editingCompany.id, values);
        message.success('Company updated successfully');
      } else {
        await setupAPI.createCompany(values);
        message.success('Company created successfully');
      }
      setModalVisible(false);
      fetchCompanies();
    } catch (error) {
      message.error(`Failed to ${editingCompany ? 'update' : 'create'} company`);
      console.error('Form submit error:', error);
    } finally {
      setFormLoading(false);
    }
  };

  // Table columns
  const columns = [
    {
      title: 'Company Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Legal Name',
      dataIndex: 'legal_name',
      key: 'legal_name',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: 'Country',
      dataIndex: 'country',
      key: 'country',
    },
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency',
      render: (currency) => currency?.code || '-',
    },
    {
      title: 'Status',
      dataIndex: 'active',
      key: 'active',
      render: (active) => (
        <span style={{ 
          color: active ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {active ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <QuickActions.Edit
            onClick={() => handleEdit(record)}
            disabled={loading}
          />
          <Popconfirm
            title="Are you sure you want to delete this company?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <QuickActions.Delete disabled={loading} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const breadcrumbs = [
    { title: 'Setup', href: '/setup' },
    { title: 'Company Setup' }
  ];

  const actions = [
    {
      label: 'New Company',
      type: 'primary',
      icon: <PlusOutlined />,
      onClick: handleCreate
    }
  ];

  return (
    <>
      <PageHeader
        title="Company Setup"
        subtitle="Manage your companies and their basic information"
        breadcrumbs={breadcrumbs}
        actions={actions}
      />
      
      <PageContent>
        <Table
          columns={columns}
          dataSource={companies}
          loading={loading}
          rowKey="id"
          pagination={{
            total: companies.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} companies`,
          }}
          scroll={{ x: 1000 }}
        />
      </PageContent>

      <Modal
        title={editingCompany ? 'Edit Company' : 'Create New Company'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <CompanyForm
          initialValues={editingCompany}
          onSubmit={handleFormSubmit}
          onCancel={() => setModalVisible(false)}
          loading={formLoading}
        />
      </Modal>
    </>
  );
};

export default CompanySetup;
