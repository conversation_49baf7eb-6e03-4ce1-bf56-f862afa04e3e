/**
 * Company Setup Page - Odoo-style company management
 * Following Odoo's res.company structure and UI patterns
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Modal,
  message,
  Spin,
  Empty,
  Row,
  Col,
  Avatar,
  Typography,
  Tag,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  SettingOutlined,
  BankOutlined,
  GlobalOutlined,
  MailOutlined,
  PhoneOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { PageHeader, PageContent } from '../../components/shared/layout/PageLayout';
import { setupAPI } from '../../services/api';
import CompanyForm from './components/CompanyForm';

const { Title, Text, Paragraph } = Typography;

const CompanySetup = () => {
  const navigate = useNavigate();
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCompany, setEditingCompany] = useState(null);
  const [formLoading, setFormLoading] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);

  // Fetch companies
  const fetchCompanies = async () => {
    setLoading(true);
    try {
      const response = await setupAPI.getCompanies();
      // Handle Django REST Framework paginated response
      const companiesData = response.data?.results || response.data || [];
      setCompanies(Array.isArray(companiesData) ? companiesData : []);
    } catch (error) {
      message.error('Failed to fetch companies');
      console.error('Fetch companies error:', error);
      setCompanies([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCompanies();
  }, []);

  // Handle create company
  const handleCreate = () => {
    setEditingCompany(null);
    setModalVisible(true);
  };

  // Handle edit company (same as configure for Odoo-style)
  const handleEdit = (company) => {
    setEditingCompany(company);
    setModalVisible(true);
  };

  // Handle form submit
  const handleFormSubmit = async (values) => {
    setFormLoading(true);
    try {
      if (editingCompany) {
        const response = await setupAPI.updateCompany(editingCompany.id, values);
        message.success('Company updated successfully');
        // Update the company in the list
        setCompanies(companies.map(company =>
          company.id === editingCompany.id ? response.data : company
        ));
      } else {
        const response = await setupAPI.createCompany(values);
        message.success('Company created successfully');
        // Add the new company to the list
        setCompanies([...companies, response.data]);
      }
      setModalVisible(false);
      setEditingCompany(null);
    } catch (error) {
      message.error(`Failed to ${editingCompany ? 'update' : 'create'} company`);
      console.error('Form submit error:', error);
    } finally {
      setFormLoading(false);
    }
  };

  // Handle company configuration (Odoo-style - no delete, only edit)
  const handleConfigure = (company) => {
    setEditingCompany(company);
    setModalVisible(true);
  };

  // Handle company selection (for multi-company setups)
  const handleSelectCompany = (company) => {
    setSelectedCompany(company);
    // In a real Odoo setup, this would switch the user's context
    message.info(`Selected company: ${company.name}`);
  };



  const breadcrumbs = [
    { title: 'Setup', href: '/setup' },
    { title: 'Company Setup' }
  ];

  const actions = [
    {
      label: 'New Company',
      type: 'primary',
      icon: <PlusOutlined />,
      onClick: handleCreate
    }
  ];

  return (
    <>
      <PageHeader
        title="Companies"
        subtitle="Configure your companies and their settings"
        breadcrumbs={breadcrumbs}
        actions={actions}
      />

      <PageContent>
        <Spin spinning={loading}>
          {companies.length === 0 && !loading ? (
            <Card>
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  <Space direction="vertical" size={16}>
                    <Title level={4} type="secondary">
                      No companies configured
                    </Title>
                    <Paragraph type="secondary">
                      Create your first company to get started with the ERP system.
                      Companies are used to organize your business data and settings.
                    </Paragraph>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={handleCreate}
                      size="large"
                    >
                      Create First Company
                    </Button>
                  </Space>
                }
              />
            </Card>
          ) : (
            <Row gutter={[16, 16]}>
              {companies.map(company => (
                <Col xs={24} sm={12} lg={8} xl={6} key={company.id}>
                  <Card
                    hoverable
                    actions={[
                      <Button
                        type="text"
                        icon={<SettingOutlined />}
                        onClick={() => handleConfigure(company)}
                        key="configure"
                      >
                        Configure
                      </Button>,
                      <Button
                        type="text"
                        icon={<BankOutlined />}
                        onClick={() => handleSelectCompany(company)}
                        key="select"
                      >
                        Select
                      </Button>
                    ]}
                  >
                    <Card.Meta
                      avatar={
                        <Avatar
                          size={48}
                          icon={<BankOutlined />}
                          src={company.logo}
                          style={{ backgroundColor: '#1890ff' }}
                        />
                      }
                      title={
                        <Space direction="vertical" size={2}>
                          <Title level={5} style={{ margin: 0 }}>
                            {company.name}
                          </Title>
                          {company.legal_name && company.legal_name !== company.name && (
                            <Text type="secondary" style={{ fontSize: '11px' }}>
                              {company.legal_name}
                            </Text>
                          )}
                        </Space>
                      }
                      description={
                        <Space direction="vertical" size={6} style={{ width: '100%' }}>
                          {company.email && (
                            <Space size={4}>
                              <MailOutlined style={{ color: '#1890ff', fontSize: '12px' }} />
                              <Text style={{ fontSize: '12px' }}>{company.email}</Text>
                            </Space>
                          )}
                          {company.phone && (
                            <Space size={4}>
                              <PhoneOutlined style={{ color: '#52c41a', fontSize: '12px' }} />
                              <Text style={{ fontSize: '12px' }}>{company.phone}</Text>
                            </Space>
                          )}
                          {(company.city || company.country) && (
                            <Space size={4}>
                              <GlobalOutlined style={{ color: '#faad14', fontSize: '12px' }} />
                              <Text style={{ fontSize: '12px' }}>
                                {[company.city, company.country].filter(Boolean).join(', ')}
                              </Text>
                            </Space>
                          )}

                          <div style={{ marginTop: '8px' }}>
                            <Tag
                              color={company.is_active ? 'success' : 'error'}
                              style={{ fontSize: '10px' }}
                            >
                              {company.is_active ? 'Active' : 'Inactive'}
                            </Tag>
                          </div>
                        </Space>
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          )}
        </Spin>
      </PageContent>

      <Modal
        title={editingCompany ? 'Edit Company' : 'Create New Company'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <CompanyForm
          initialValues={editingCompany}
          onSubmit={handleFormSubmit}
          onCancel={() => setModalVisible(false)}
          loading={formLoading}
        />
      </Modal>
    </>
  );
};

export default CompanySetup;
