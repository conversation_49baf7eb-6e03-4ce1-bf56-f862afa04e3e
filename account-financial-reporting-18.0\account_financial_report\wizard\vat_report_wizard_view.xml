<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="vat_report_wizard" model="ir.ui.view">
        <field name="name">vat_report_wizard_view</field>
        <field name="model">vat.report.wizard</field>
        <field name="arch" type="xml">
            <form string="VAT Report Options">
                <group name="main_info">
                    <field
                        name="company_id"
                        options="{'no_create': True}"
                        groups="base.group_multi_company"
                    />
                </group>
                <group name="filters">
                    <group name="date_range">
                        <field name="date_range_id" />
                        <field name="date_from" />
                        <field name="date_to" />
                    </group>
                </group>
                <group name="other_filters">
                    <field name="target_move" widget="radio" />
                    <field name="based_on" widget="radio" />
                    <field name="tax_detail" />
                </group>
                <footer>
                    <button
                    name="button_export_html"
                    string="View"
                    type="object"
                    default_focus="1"
                    class="oe_highlight"
                />
                    or
                    <button
                    name="button_export_pdf"
                    string="Export PDF"
                    type="object"
                />
                    or
                    <button
                    name="button_export_xlsx"
                    string="Export XLSX"
                    type="object"
                />
                    or
                    <button string="Cancel" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>
    <record id="action_vat_report_wizard" model="ir.actions.act_window">
        <field name="name">VAT Report</field>
        <field name="res_model">vat.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="vat_report_wizard" />
        <field name="target">new</field>
    </record>
</odoo>
