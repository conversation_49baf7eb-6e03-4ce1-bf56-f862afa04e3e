# Generated by Django 4.2.21 on 2025-07-16 04:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('setup', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='IncomeTaxConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Income tax name (e.g., Corporate Income Tax)', max_length=100)),
                ('rate', models.DecimalField(decimal_places=2, help_text='Tax rate percentage', max_digits=5)),
                ('tax_year_start', models.DateField(help_text='Tax year start date')),
                ('tax_year_end', models.DateField(help_text='Tax year end date')),
                ('min_income', models.DecimalField(decimal_places=2, default=0, help_text='Minimum income for this bracket', max_digits=15)),
                ('max_income', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum income for this bracket (null for unlimited)', max_digits=15, null=True)),
                ('calculation_method', models.CharField(choices=[('flat', 'Flat Rate'), ('progressive', 'Progressive'), ('slab', 'Slab System')], default='flat', max_length=20)),
                ('standard_deduction', models.DecimalField(decimal_places=2, default=0, help_text='Standard deduction amount', max_digits=15)),
                ('personal_exemption', models.DecimalField(decimal_places=2, default=0, help_text='Personal exemption amount', max_digits=15)),
                ('advance_tax_required', models.BooleanField(default=False, help_text='Whether advance tax payments are required')),
                ('quarterly_filing', models.BooleanField(default=False, help_text='Whether quarterly filing is required')),
                ('annual_filing_deadline', models.DateField(blank=True, help_text='Annual tax filing deadline', null=True)),
                ('is_default', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='income_tax_configurations', to='setup.companysetup')),
            ],
            options={
                'ordering': ['min_income'],
                'unique_together': {('company', 'name')},
            },
        ),
    ]
