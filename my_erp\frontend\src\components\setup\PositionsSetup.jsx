/**
 * Employee Positions Setup Component
 */
import React from 'react';
import { Card, Typography, Alert } from 'antd';
import { UserOutlined } from '@ant-design/icons';

const { Title } = Typography;

const PositionsSetup = () => {
  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <UserOutlined style={{ marginRight: 8 }} />
        Employee Positions Setup
      </Title>
      
      <Card>
        <Alert
          message="Coming Soon"
          description="Employee Positions Setup will be available soon."
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
};

export default PositionsSetup;
