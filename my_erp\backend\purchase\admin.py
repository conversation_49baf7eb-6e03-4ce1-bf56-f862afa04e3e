"""
Purchase Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from .models import PurchaseOrder, PurchaseOrderLine


class PurchaseOrderLineInline(admin.TabularInline):
    model = PurchaseOrderLine
    extra = 1
    fields = ['product_id', 'name', 'product_qty', 'price_unit', 'price_subtotal', 'qty_received', 'qty_invoiced']
    readonly_fields = ['price_subtotal', 'qty_received', 'qty_invoiced']


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ['name', 'date_order', 'partner_id', 'user_id', 'state', 'invoice_status', 'amount_total_display']
    list_filter = ['state', 'invoice_status', 'date_order', 'company_id']
    search_fields = ['name', 'partner_id__name', 'partner_ref', 'origin']
    inlines = [PurchaseOrderLineInline]
    readonly_fields = ['amount_untaxed', 'amount_tax', 'amount_total', 'invoice_count']
    ordering = ['-date_order', '-name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'origin', 'partner_ref', 'state')
        }),
        ('Vendor Information', {
            'fields': ('partner_id', 'dest_address_id')
        }),
        ('Purchase Information', {
            'fields': ('user_id', 'date_order', 'date_approve', 'date_planned')
        }),
        ('Accounting', {
            'fields': ('payment_term_id', 'fiscal_position_id')
        }),
        ('Amounts', {
            'fields': ('amount_untaxed', 'amount_tax', 'amount_total', 'currency_id')
        }),
        ('Invoice Status', {
            'fields': ('invoice_status', 'invoice_count')
        }),
        ('Other', {
            'fields': ('notes', 'receipt_reminder_email', 'reminder_date_before_receipt'),
            'classes': ('collapse',)
        }),
    )

    def amount_total_display(self, obj):
        return format_html(
            '<span style="font-weight: bold; color: {};">{} {:,.2f}</span>',
            'red' if obj.amount_total > 0 else 'black',
            obj.currency_id,
            obj.amount_total
        )
    amount_total_display.short_description = 'Total Amount'

    actions = ['action_confirm_orders', 'action_cancel_orders']

    def action_confirm_orders(self, request, queryset):
        confirmed = 0
        for order in queryset:
            if order.button_confirm():
                confirmed += 1
        self.message_user(request, f'{confirmed} orders confirmed successfully.')
    action_confirm_orders.short_description = "Confirm selected orders"

    def action_cancel_orders(self, request, queryset):
        cancelled = 0
        for order in queryset:
            if order.button_cancel():
                cancelled += 1
        self.message_user(request, f'{cancelled} orders cancelled successfully.')
    action_cancel_orders.short_description = "Cancel selected orders"


@admin.register(PurchaseOrderLine)
class PurchaseOrderLineAdmin(admin.ModelAdmin):
    list_display = ['order_id', 'product_id', 'name', 'product_qty', 'price_unit', 'price_subtotal', 'invoice_status']
    list_filter = ['invoice_status', 'order_id__state', 'company_id']
    search_fields = ['order_id__name', 'product_id__product_tmpl_id__name', 'name']
    readonly_fields = ['price_subtotal', 'price_total', 'price_tax', 'qty_received', 'qty_invoiced', 'qty_to_invoice']
    ordering = ['-order_id__date_order', 'sequence']

    fieldsets = (
        ('Order Information', {
            'fields': ('order_id', 'sequence', 'name')
        }),
        ('Product Information', {
            'fields': ('product_id', 'product_template_id', 'product_qty', 'product_uom')
        }),
        ('Pricing', {
            'fields': ('price_unit', 'price_subtotal', 'price_tax', 'price_total')
        }),
        ('Quantities', {
            'fields': ('qty_received', 'qty_invoiced', 'qty_to_invoice')
        }),
        ('Taxes & Analytics', {
            'fields': ('taxes_id', 'account_analytic_id')
        }),
        ('Dates', {
            'fields': ('date_planned',)
        }),
        ('Invoice Status', {
            'fields': ('invoice_status',)
        }),
    )

    filter_horizontal = ['taxes_id', 'invoice_lines']


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - Purchase Module"
