===========
Tax Balance
===========

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:64ca11d7fe7f11f6c190de1e151439ed0190985be72df053a4d8c4b11a6c9330
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Mature-brightgreen.png
    :target: https://odoo-community.org/page/development-status
    :alt: Mature
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Faccount--financial--reporting-lightgray.png?logo=github
    :target: https://github.com/OCA/account-financial-reporting/tree/18.0/account_tax_balance
    :alt: OCA/account-financial-reporting
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/account-financial-reporting-18-0/account-financial-reporting-18-0-account_tax_balance
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/account-financial-reporting&target_branch=18.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module allows to compute tax balances within a certain date range.
It depends on date_range module and exposes 'compute' methods that can
be called by other modules (like localization ones).

**Table of contents**

.. contents::
   :local:

Usage
=====

Accounting --> Reporting --> Taxes Balance

Select the company, the date range, the target moves and 'open taxes'

|image1|

.. |image1| image:: https://raw.githubusercontent.com/account_tax_balance/static/description/tax_balance.png

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/account-financial-reporting/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/account-financial-reporting/issues/new?body=module:%20account_tax_balance%0Aversion:%2018.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Agile Business Group
* Therp BV
* Tecnativa
* ACSONE SA/NV

Contributors
------------

- Lorenzo Battistini <<EMAIL>>
- Giovanni Capalbo <<EMAIL>>
- Tecnativa - Antonio Espinosa
- Tecnativa - Pedro M. Baeza
- ACSONE SA/NV - Stéphane Bidoul
- Andrea Stirpe <<EMAIL>>
- Iván Antón <<EMAIL>>
- `Sygel <https://www.sygel.es>`__:

  - Valentin Vinagre

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/account-financial-reporting <https://github.com/OCA/account-financial-reporting/tree/18.0/account_tax_balance>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
