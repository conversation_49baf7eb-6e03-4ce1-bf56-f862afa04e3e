#!/usr/bin/env python
"""
Count registered admin models
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from django.contrib import admin
from django.apps import apps

def count_admin_models():
    print("="*60)
    print("📊 ADMIN MODELS COUNT")
    print("="*60)
    
    # Get all models from accounting app
    all_models = list(apps.get_app_config('accounting').get_models())
    
    # Get registered admin models
    registered_models = []
    for model in all_models:
        if model in admin.site._registry:
            registered_models.append(model)
    
    print(f"📊 Total models in accounting app: {len(all_models)}")
    print(f"✅ Registered in admin: {len(registered_models)}")
    print(f"❌ Missing from admin: {len(all_models) - len(registered_models)}")
    
    print("\n" + "="*60)
    print("✅ REGISTERED ADMIN MODELS")
    print("="*60)
    
    for i, model in enumerate(registered_models, 1):
        print(f"{i:2d}. {model.__name__}")
    
    # Find missing models
    missing_models = [model for model in all_models if model not in admin.site._registry]
    
    if missing_models:
        print("\n" + "="*60)
        print("❌ MISSING FROM ADMIN")
        print("="*60)
        for i, model in enumerate(missing_models, 1):
            print(f"{i:2d}. {model.__name__}")
    
    print("\n" + "="*60)
    print("🎯 SUMMARY")
    print("="*60)
    
    if len(missing_models) == 0:
        print("🎉 ALL MODELS ARE REGISTERED IN ADMIN!")
        print(f"✅ {len(registered_models)}/51 models visible in admin interface")
    else:
        print(f"⚠️ {len(missing_models)} models are missing from admin")
        print(f"✅ {len(registered_models)}/51 models visible in admin interface")
    
    return len(registered_models), len(missing_models)

if __name__ == '__main__':
    count_admin_models()
