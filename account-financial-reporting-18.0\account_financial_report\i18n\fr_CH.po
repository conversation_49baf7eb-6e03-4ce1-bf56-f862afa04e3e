# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_financial_report
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2019-12-24 12:05+0000\n"
"Last-Translator: Martronic SA <<EMAIL>>\n"
"Language-Team: none\n"
"Language: fr_CH\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 3.9.1\n"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "&gt; 120 d."
msgstr "&gt; 120 j."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "1 - 30 d."
msgstr "1 - 30 j."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
msgid "10"
msgstr "10"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "31 - 60 d."
msgstr "31 - 60 j."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "61 - 90 d."
msgstr "61 - 90 j."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "91 - 120 d."
msgstr "91 - 120 j."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "<b>Taxes summary</b>"
msgstr "<b> Synth??se des taxes</b>"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid ""
"<i class=\"fa fa-exclamation-triangle mr-3\"/>\n"
"                    Duplicate amounts may be shown because more than one "
"analytical account may be defined in the journal items."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Intervals configuration</span>"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid "<span class=\"oe_inline\">To</span>"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_abstract_report
msgid "Abstract Report"
msgstr "R??sum?? du rapport"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_financial_report_abstract_wizard
msgid "Abstract Wizard"
msgstr "R??sum?? du rapport"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_abstract_report_xlsx
msgid "Abstract XLSX Account Financial Report"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model,name:account_financial_report.model_account_account
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Account"
msgstr "Compte"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__account_age_report_config_id
msgid "Account Age Report Config"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_code_from
#, fuzzy
msgid "Account Code From"
msgstr "Code du compte"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_code_to
#, fuzzy
msgid "Account Code To"
msgstr "Code du compte"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_group
msgid "Account Group"
msgstr "Groupe de compte"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Account Name"
msgstr "Nom du compte"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Account at 0 filter"
msgstr "Le compte a 0 filtre"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
msgid "Account balance at 0 filter"
msgstr "Filtrer les soldes de compte ?? 0"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__account_ids
msgid "Accounts"
msgstr "Comptes"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__centralize
msgid "Activate centralization"
msgstr "Activer la centralisation"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Additional Filtering"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.action_aged_partner_report_configuration
msgid "Age Partner Report Configuration"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 120\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 120 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 30\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 30 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 60\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 60 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 90\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 90 d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_aged_partner_balance_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_aged_partner_balance_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_aged_partner_balance_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_aged_partner_balance_wizard
msgid "Aged Partner Balance"
msgstr "Balance ??g??e partenaire"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_base
msgid "Aged Partner Balance -"
msgstr "Balance ??g??e partenaire -"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_aged_partner_balance
#, fuzzy
msgid "Aged Partner Balance Report"
msgstr "Balance ??g??e partenaire -"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_aged_partner_balance_report_wizard
msgid "Aged Partner Balance Wizard"
msgstr "Assistant balance ??g??e partenaire"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_aged_partner_balance_xlsx
#, fuzzy
msgid "Aged Partner Balance XLSL Report"
msgstr "Balance ??g??e partenaire XLSX"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_aged_partner_balance_xlsx
msgid "Aged Partner Balance XLSX"
msgstr "Balance ??g??e partenaire XLSX"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "All"
msgstr "Tous"

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__aged_partner_balance_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__open_items_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__target_move__all
msgid "All Entries"
msgstr "Toutes les ??critures"

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__aged_partner_balance_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__open_items_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__target_move__posted
msgid "All Posted Entries"
msgstr "??critures comptabilis??es seulement"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "All entries"
msgstr "Toutes les ??critures"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "All posted entries"
msgstr "Toutes les ??critures post??es"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Amount Cur."
msgstr "Montant Devise"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Amount Currency"
msgstr "Montant Devise"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Amount cur."
msgstr "Montant Devise"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_move_line__analytic_account_ids
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__grouped_by__analytic_account
#, fuzzy
msgid "Analytic Account"
msgstr "Filtrer les comptes"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Analytic Distribution"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Balance"
msgstr "Solde"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Base Amount"
msgstr "Montant de base"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Balance"
msgstr "Solde de base"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Credit"
msgstr "Cr??dit de base"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Debit"
msgstr "D??bit de base"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__based_on
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Based On"
msgstr "Bas?? sur"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Based on"
msgstr "Bas??e sur"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Cancel"
msgstr "Annuler"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "Centralize filter"
msgstr "Filtre de centralisation"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_account__centralized
msgid "Centralized"
msgstr "Centralis??"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__group_child_ids
msgid "Child Groups"
msgstr "Groupes enfants"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Code"
msgstr "Code"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_account_financial_report_abstract_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__company_id
msgid "Company"
msgstr "Soci??t??"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__compute_account_ids
msgid "Compute accounts"
msgstr "Calculer les comptes"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "Configurations"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__create_uid
msgid "Created by"
msgstr "Cr???? par"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__create_date
msgid "Created on"
msgstr "Cr???? le"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Credit"
msgstr "Cr??dit"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Cumul cur."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Cumul. Bal."
msgstr "Cumuler bal."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur."
msgstr "Courant"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur. Original"
msgstr "Courant original"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur. Residual"
msgstr "Courant r??siduel"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Currency"
msgstr "Devise"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid "Current"
msgstr "Courant"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Date"
msgstr "Date"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__date_at
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__date_at
msgid "Date At"
msgstr "Date au"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_from
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Date From"
msgstr "Date du"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_to
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_to
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Date To"
msgstr "Date au"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
msgid "Date at filter"
msgstr "Date au filtre"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Date from"
msgstr "Date du"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_range_id
msgid "Date range"
msgstr "Plage de date"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Date range filter"
msgstr "Filtre de plage de dates"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Date to"
msgstr "Date au"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Debit"
msgstr "D??bit"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Description"
msgstr "Description"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__tax_detail
msgid "Detail Taxes"
msgstr "D??tail des taxes"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__display_name
msgid "Display Name"
msgstr "Nom affich??"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__foreign_currency
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__foreign_currency
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__foreign_currency
msgid ""
"Display foreign currency for move lines, unless account currency is not "
"setup through chart of accounts will display initial and final balance in "
"that currency."
msgstr ""
"Afficher les devises ??trang??res pour les ??critures comptables, ?? moins "
"que la monnaie du compte comptable ne soit pas d??finie dans le plan "
"comptable affichera les balances initiale et finale dans cette monnaie."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__hide_parent_hierarchy_level
msgid "Do not display parent levels"
msgstr "Ne pas afficher les niveaux parent"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Due\n"
"                        date"
msgstr ""
"??ch??ance\n"
"                        date"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#, fuzzy
msgid ""
"Due\n"
"                    date"
msgstr ""
"??ch??ance\n"
"                        date"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
msgid "Due date"
msgstr "Date d'??ch??ance"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_to
msgid "End Date"
msgstr "Date de fin"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_to
msgid "End date"
msgstr "Date de fin"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_ending_cumul
msgid ""
"Ending\n"
"                        balance"
msgstr ""
"Cl??ture\n"
"                        solde"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_aged_partner_balance_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__account_code_to
msgid "Ending account in a range"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Ending balance"
msgstr "Solde de cl??ture"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
#, fuzzy
msgid ""
"Ending balance\n"
"                        cur."
msgstr ""
"Cl??ture\n"
"                        solde"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Entries sorted by"
msgstr "??criture tri??es par"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Entry"
msgstr "??criture"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Entry number"
msgstr "Num??ro d'??criture"

#. module: account_financial_report
#. odoo-javascript
#: code:addons/account_financial_report/static/src/xml/report.xml:0
msgid "Export"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Export PDF"
msgstr "Export PDF"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Export XLSX"
msgstr "Export XLSX"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Filter accounts"
msgstr "Filtrer les comptes"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#, fuzzy
msgid "Filter analytic accounts"
msgstr "Filtrer les ??tiquettes analytiques"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__cost_center_ids
msgid "Filter cost centers"
msgstr "Filtrer les centres de co??ts"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_journal_ids
msgid "Filter journals"
msgstr "Filtrer les journaux"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__partner_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Filter partners"
msgstr "Filtrer les partenaires"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__foreign_currency
msgid "Foreign Currency"
msgstr "Devise ??trang??re"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#, fuzzy
msgid "From Code"
msgstr "Code"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "From:"
msgstr "Du:"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "From: %(date_from)s To: %(date_to)s"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__complete_code
#, fuzzy
msgid "Full Code"
msgstr "Code"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__complete_name
#, fuzzy
msgid "Full Name"
msgstr "Nom"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__fy_start_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__fy_start_date
msgid "Fy Start Date"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.act_action_general_ledger_wizard_partner_relation
#: model:ir.actions.act_window,name:account_financial_report.action_general_ledger_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_general_ledger_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_general_ledger_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_general_ledger_wizard
msgid "General Ledger"
msgstr "Grand livre"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_base
msgid "General Ledger -"
msgstr "Grand livre"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_general_ledger
#, fuzzy
msgid "General Ledger Report"
msgstr "Assistant de rapport Grand livre"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_general_ledger_report_wizard
msgid "General Ledger Report Wizard"
msgstr "Assistant de rapport Grand livre"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_general_ledger_xlsx
#, fuzzy
msgid "General Ledger XLSL Report"
msgstr "Grand livre XLSX"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_general_ledger_xlsx
msgid "General Ledger XLSX"
msgstr "Grand livre XLSX"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid ""
"General Ledger can be computed only if selected company have\n"
"                        only one unaffected earnings account."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__group_option
msgid "Group entries by"
msgstr "Grouper les entr??es par"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__grouped_by
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__grouped_by
msgid "Grouped By"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid ""
"Here you can set the intervals that will appear on the Aged Partner Balance."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Hide"
msgstr "Cacher"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__hide_account_at_0
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__hide_account_at_0
msgid "Hide account ending balance at 0"
msgstr "Cacher les compte avec un solde ?? 0"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__hide_account_at_0
msgid "Hide accounts at 0"
msgstr "Cacher les comptes ?? 0"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_hierarchy_level
msgid "Hierarchy Levels to display"
msgstr "Niveaux de hi??rarchie ?? afficher"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__id
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__id
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_account_account__centralized
msgid ""
"If flagged, no details will be displayed in the General Ledger report (the "
"webkit one only), only centralized amounts per period."
msgstr ""
"Si coch??, aucun d??tail ne sera affich?? dans les rapport du Grand Livre "
"(uniquement le webkit), seulement les montants centralis??s par p??riode."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__inferior_limit
msgid "Inferior Limit"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/models/account_age_report_configuration.py:0
msgid "Inferior Limit must be greather than zero"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
#, fuzzy
msgid ""
"Initial\n"
"                        balance cur."
msgstr ""
"Solde\n"
"                    initial"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid ""
"Initial\n"
"                    balance"
msgstr ""
"Solde\n"
"                    initial"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Initial balance"
msgstr "Solde initial"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__age_partner_config_id
#: model:ir.model.fields,field_description:account_financial_report.field_res_config_settings__age_partner_config_id
msgid "Intervals configuration"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__journal_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Journal"
msgstr "Journal"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_move_line
msgid "Journal Item"
msgstr "??criture comptable"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__domain
msgid "Journal Items Domain"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_journal_ledger_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_journal_ledger_wizard_html
#: model:ir.ui.menu,name:account_financial_report.menu_journal_ledger_wizard
msgid "Journal Ledger"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_base
msgid "Journal Ledger -"
msgstr "Livre des Journaux -"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_journal_ledger
#, fuzzy
msgid "Journal Ledger Report"
msgstr "Livre des Journaux -"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_journal_ledger_report_wizard
msgid "Journal Ledger Report Wizard"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_journal_ledger_xlsx
msgid "Journal Ledger XLSX"
msgstr "Livre des Journaux XLSX"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_journal_ledger_xlsx
#, fuzzy
msgid "Journal Ledger XLSX Report"
msgstr "Livre des Journaux XLSX"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__journal_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Journals"
msgstr "Journaux"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__write_uid
msgid "Last Updated by"
msgstr "Derni??re mise ?? jour par"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__write_date
msgid "Last Updated on"
msgstr "Derni??re mise ?? jour le"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__level
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Level"
msgstr "Niveau"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "Level %s"
msgstr "Niveau %s"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__limit_hierarchy_level
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Limit hierarchy levels"
msgstr "Limiter les niveaux de hi??rarchie"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__line_ids
msgid "Line"
msgstr "Ligne"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger.py:0
#: code:addons/account_financial_report/report/open_items.py:0
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid "Missing Partner"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_age_report_configuration_line
msgid "Model to set interval lines for Age partner balance report"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_age_report_configuration
msgid "Model to set intervals for Age partner balance report"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__move_target
msgid "Move Target"
msgstr "Mouvement cible"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal
msgid "Moves"
msgstr "Mouvements"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/models/account_age_report_configuration.py:0
msgid "Must complete Configuration Lines"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__name
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__name
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Name"
msgstr "Nom"

#. module: account_financial_report
#: model:ir.model.constraint,message:account_financial_report.constraint_account_age_report_configuration_line_unique_name_config_combination
msgid "Name must be unique per report configuration"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Net"
msgstr "Net"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "No"
msgstr "Non"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "No group"
msgstr "Sans groupe"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "No limit"
msgstr "Sans limite"

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__
msgid "None"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Not Posted"
msgstr "Non comptabilis??"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "Not due"
msgstr "Non ??chu"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "OCA Aged Report Configuration"
msgstr ""

#. module: account_financial_report
#: model:ir.ui.menu,name:account_financial_report.menu_oca_reports
msgid "OCA accounting reports"
msgstr "Rapports de comptabilit?? OCA"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid "Older"
msgstr "Plus ancien"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__only_one_unaffected_earnings_account
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__only_one_unaffected_earnings_account
msgid "Only One Unaffected Earnings Account"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_open_items_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_open_items_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_open_items_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_open_items_wizard
msgid "Open Items"
msgstr "Postes ouverts"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_base
msgid "Open Items -"
msgstr "Postes ouverts -"

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.act_action_open_items_wizard_partner_relation
msgid "Open Items Partner"
msgstr "Postes ouverts du partenaire"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_open_items
#, fuzzy
msgid "Open Items Report"
msgstr "Dialogue du rapport des postes ouverts"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_open_items_report_wizard
msgid "Open Items Report Wizard"
msgstr "Dialogue du rapport des postes ouverts"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_open_items_xlsx
msgid "Open Items XLSX"
msgstr "Postes ouverts XLSX"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_open_items_xlsx
#, fuzzy
msgid "Open Items XLSX Report"
msgstr "Postes ouverts XLSX"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Options"
msgstr "Options"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Original"
msgstr "Original"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Partner"
msgstr "Partenaire"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_partner_ending_cumul
msgid ""
"Partner\n"
"                    cumul aged balance"
msgstr ""
"Partenaire\n"
"                    cumul balance ??g??e"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
msgid "Partner Initial balance"
msgstr "Solde initial du partenaire"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Partner cumul aged balance"
msgstr "Balance ??g??e cumul??e du partenaire"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_ending_cumul
msgid "Partner ending balance"
msgstr "Solde de cl??ture du partenaire"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Partner initial balance"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__partners
msgid "Partners"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__payable_accounts_only
msgid "Payable Accounts Only"
msgstr "Comptes cr??diteurs uniquement"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_account_ending_cumul
msgid "Percents"
msgstr "Pourcentages"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Period balance"
msgstr "Solde de la p??riode"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Periods"
msgstr "P??riodes"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Posted"
msgstr "Comptabilis??"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Rec."
msgstr "R??c."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__receivable_accounts_only
msgid "Receivable Accounts Only"
msgstr "Comptes d??biteurs uniquement"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid ""
"Ref -\n"
"                        Label"
msgstr ""
"Ref -\n"
"                        Libell??"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#, fuzzy
msgid ""
"Ref -\n"
"                    Label"
msgstr ""
"Ref -\n"
"                        Libell??"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Ref - Label"
msgstr "Ref - Libell??"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Residual"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Sequence"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Show"
msgstr "Afficher"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__show_cost_center
#, fuzzy
msgid "Show Analytic Account"
msgstr "Afficher les ??tiquettes analytiques"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__with_auto_sequence
msgid "Show Auto Sequence"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__show_move_line_details
msgid "Show Move Line Details"
msgstr "Afficher le d??tail des ??critures"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__show_partner_details
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_partner_details
msgid "Show Partner Details"
msgstr "Afficher le d??tails du partenaire"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__foreign_currency
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__foreign_currency
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__foreign_currency
msgid "Show foreign currency"
msgstr "Afficher les devises ??trang??res"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_hierarchy
msgid "Show hierarchy"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__sort_option
msgid "Sort entries by"
msgstr "Trier les ??critures par"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_from
msgid "Start Date"
msgstr "Date de d??but"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_from
msgid "Start date"
msgstr "Date de d??but"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_aged_partner_balance_report_wizard__account_code_from
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__account_code_from
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__account_code_from
msgid "Starting account in a range"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "TOTAL"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Tags"
msgstr "??tiquettes"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__target_move
msgid "Target Moves"
msgstr "??critures cibl??es"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Target moves filter"
msgstr "Filtre sur les ??critures cible"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Tax"
msgstr "Taxe"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Tax Amount"
msgstr "Montant de la taxe"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Balance"
msgstr "Solde des taxes"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Credit"
msgstr "Cr??dit de taxe"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Debit"
msgstr "D??bit de taxe"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__based_on__taxgroups
msgid "Tax Groups"
msgstr "Groupes de taxe"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
msgid "Tax Initial balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__based_on__taxtags
msgid "Tax Tags"
msgstr "??tiquettes de taxe"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
msgid "Tax ending balance"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Tax initial balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Taxes"
msgstr "Taxes"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/general_ledger_wizard.py:0
msgid ""
"The Company in the General Ledger Report Wizard and in Date Range must be "
"the same."
msgstr ""
"La soci??t?? dans le dialogue du rapport du Grand livre et dans la plage de "
"dates doit ??tre la m??me."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/trial_balance_wizard.py:0
msgid ""
"The Company in the Trial Balance Report Wizard and in Date Range must be the "
"same."
msgstr ""
"La soci??t?? dans le rapport de balance g??n??rale et dans la plage de dates "
"doivent ??tre les m??mes."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/vat_report_wizard.py:0
msgid ""
"The Company in the Vat Report Wizard and in Date Range must be the same."
msgstr ""
"La soci??t?? dans le dialogue du rapport des taxes et dans la plage de dates "
"doit ??tre la m??me."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/trial_balance_wizard.py:0
msgid "The hierarchy level to filter on must be greater than 0."
msgstr "Le niveau de hi??rarchie ?? filtrer doit ??tre plus rand que 0."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid ""
"There is a problem in the structure of the account groups. You may need to "
"create some child group of %s."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__domain
msgid "This domain will be used to select specific domain for Journal Items"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
#, fuzzy
msgid "To"
msgstr "Au:"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "To:"
msgstr "Au:"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_account_ending_cumul
msgid "Total"
msgstr "Total"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_trial_balance_wizard
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_html
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_trial_balance_wizard
msgid "Trial Balance"
msgstr "Balance g??n??rale"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_base
msgid "Trial Balance -"
msgstr "Balance g??n??rale -"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_trial_balance
#, fuzzy
msgid "Trial Balance Report"
msgstr "Dialogue du rapport de balance g??n??rale"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_trial_balance_report_wizard
msgid "Trial Balance Report Wizard"
msgstr "Dialogue du rapport de balance g??n??rale"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_xlsx
msgid "Trial Balance XLSX"
msgstr "Balance g??n??rale XLSX"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_trial_balance_xlsx
#, fuzzy
msgid "Trial Balance XLSX Report"
msgstr "Balance g??n??rale XLSX"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid ""
"Trial Balance can be computed only if selected company have only\n"
"                        one unaffected earnings account."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__unaffected_earnings_account
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__unaffected_earnings_account
msgid "Unaffected Earnings Account"
msgstr "Compte des B??n??fices/Pertes ?? reporter"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__hide_account_at_0
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__hide_account_at_0
msgid ""
"Use this filter to hide an account or a partner with an ending balance at 0. "
"If partners are filtered, debits and credits totals will not match the trial "
"balance."
msgstr ""
"Utiliser ce filtre pour cacher un compte ou un partenaire avec un solde de "
"cl??ture ?? 0. Si les partenaires sont filtr??s, les totaux des d??bits et "
"cr??dits ne correspondront pas ?? la balance g??n??rale."

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__show_hierarchy
msgid "Use when your account groups are hierarchical"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.action_vat_report_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_vat_report_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_vat_report_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_vat_report_wizard
msgid "VAT Report"
msgstr "Rapport TVA"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "VAT Report -"
msgstr "Rapport TVA -"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "VAT Report Options"
msgstr "Options du rapport TVA"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_vat_report_wizard
msgid "VAT Report Wizard"
msgstr "Dialogue du rapport TVA"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_vat_report_xlsx
msgid "VAT Report XLSX"
msgstr "Rapport TVA XLSX"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#, fuzzy
msgid "Vat Report"
msgstr "Rapport TVA"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_vat_report
#, fuzzy
msgid "Vat Report Report"
msgstr "Options du rapport TVA"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_vat_report_xlsx
#, fuzzy
msgid "Vat Report XLSX Report"
msgstr "Rapport TVA XLSX"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "View"
msgstr "Voir"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__hide_account_at_0
msgid ""
"When this option is enabled, the trial balance will not display accounts "
"that have initial balance = debit = credit = end balance = 0"
msgstr ""
"Lorsque cette options est activ??e, la balance g??n??rale n'affichera pas "
"les comptes qui ont balance initiale = d??bit = cr??dit = solde de cl??ture "
"= 0"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__with_account_name
msgid "With Account Name"
msgstr "Avec le nom du compte"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid "Without analytic account"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "Yes"
msgstr "Oui"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger.py:0
msgid "future"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "or"
msgstr "ou"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_print_journal_ledger_wizard_qweb
msgid "ournal Ledger"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal
msgid "to"
msgstr "??"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 16.21%;"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 23.24%;"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 23.78%;"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 31.35%;"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 38.92%;"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 8.11%;"
msgstr ""

#~ msgid ""
#~ "Age ??? 120\n"
#~ "                        d."
#~ msgstr "??? 120 jours"

#, python-format
#~ msgid "Age ??? 120 d."
#~ msgstr "??? 120 jours"

#~ msgid ""
#~ "Age ??? 30\n"
#~ "                        d."
#~ msgstr "??? 30 jours"

#, python-format
#~ msgid "Age ??? 30 d."
#~ msgstr "??? 30 j."

#~ msgid ""
#~ "Age ??? 60\n"
#~ "                        d."
#~ msgstr "??? 60 jours"

#, python-format
#~ msgid "Age ??? 60 d."
#~ msgstr "??? 60 j."

#~ msgid ""
#~ "Age ??? 90\n"
#~ "                        d."
#~ msgstr "??? 90 j."

#, python-format
#~ msgid "Age ??? 90 d."
#~ msgstr "??? 90 j."

#~ msgid "Last Modified on"
#~ msgstr "Derni??re modification le"

#~ msgid "Filter analytic tags"
#~ msgstr "Filtrer les ??tiquettes analytiques"

#, python-format
#~ msgid "Show analytic tags"
#~ msgstr "Afficher les ??tiquettes analytiques"

#~ msgid "Child Accounts"
#~ msgstr "Comptes enfants"

#~ msgid "Computed Accounts"
#~ msgstr "Comptes calcul??s"

#~ msgid ""
#~ "Computed Accounts: Use when the account group have codes\n"
#~ "        that represent prefixes of the actual accounts.\n"
#~ "\n"
#~ "        Child Accounts: Use when your account groups are hierarchical.\n"
#~ "\n"
#~ "        No hierarchy: Use to display just the accounts, without any "
#~ "grouping.\n"
#~ "        "
#~ msgstr ""
#~ "Comptes calcul??s: ?? utiliser lorsque les groupes de comptes ont des "
#~ "codes\n"
#~ "        qui repr??sentent des pr??fixes des comptes actuels.\n"
#~ "\n"
#~ "        Comptes enfants: ?? utiliser lorsque vos groupes de comptes sont "
#~ "hi??rarchis??s.\n"
#~ "\n"
#~ "        Pas de hi??rarchie: ?? utiliser pour n'afficher que les comptes "
#~ "sans aucun groupement.\n"
#~ "        "

#~ msgid "Hierarchy On"
#~ msgstr "Hi??rarchiser sur"

#~ msgid "No hierarchy"
#~ msgstr "Sans hi??rarchie"

#~ msgid "From: %s To: %s"
#~ msgstr "Du: %s au: %s"

#~ msgid "Not only one unaffected earnings account"
#~ msgstr "Pas un seul compte de gains non affect??"

#~ msgid "<span class=\"fa fa-download\"/> Export"
#~ msgstr "Exporter"

#~ msgid "<span class=\"fa fa-print\"/> Print"
#~ msgstr "Imprimer"

#~ msgid ""
#~ "General Ledger can be computed only if selected company have only one "
#~ "unaffected earnings account."
#~ msgstr ""
#~ "La comptabilit?? g??n??rale ne peut ??tre calcul??e que si l'entreprise "
#~ "s??lectionn??e a un seul compte de r??sultat non affect??."

#~ msgid ""
#~ "Trial Balance can be computed only if selected company have only one "
#~ "unaffected earnings account."
#~ msgstr ""
#~ "La balance g??n??rale peut ??tre calcul??e uniquement si la soci??t?? s??"
#~ "lectionn??e n'a qu'un seul compte de b??n??fices/pertes ?? reporter."

#~ msgid ""
#~ "Cost\n"
#~ "                            center"
#~ msgstr ""
#~ "Co??t\n"
#~ "                            centre"

#~ msgid "Cost center"
#~ msgstr "Centre de co??t"

#~ msgid "Account ID"
#~ msgstr "ID du compte"

#~ msgid "Account Type"
#~ msgstr "Type de Compte"

#~ msgid "Age 120 Days"
#~ msgstr "120 jours"

#~ msgid "Age 30 Days"
#~ msgstr "30 jours"

#~ msgid "Age 60 Days"
#~ msgstr "60 jours"

#~ msgid "Age 90 Days"
#~ msgstr "90 jours"

#~ msgid "Amount Residual"
#~ msgstr "Montant r??siduel"

#~ msgid "Amount Residual Currency"
#~ msgstr "Montant r??siduel Devise"

#~ msgid "Amount Total Due"
#~ msgstr "Total montant d??"

#~ msgid "Amount Total Due Currency"
#~ msgstr "Total montant d?? Devise"

#~ msgid "Centralize"
#~ msgstr "Centraliser"

#~ msgid "Centralized Entries"
#~ msgstr "??critures centralis??es"

#~ msgid "Child accounts"
#~ msgstr "Comptes enfants"

#~ msgid "Company Currency"
#~ msgstr "Devise de la Soci??t??"

#~ msgid "Cost Center"
#~ msgstr "Centre de co??t"

#~ msgid "Cumul Age 120 Days"
#~ msgstr "Cumul?? sur 120 jours"

#~ msgid "Cumul Age 30 Days"
#~ msgstr "Cumul?? sur 30 jours"

#~ msgid "Cumul Age 60 Days"
#~ msgstr "Cumul?? sur 60 jours"

#~ msgid "Cumul Age 90 Days"
#~ msgstr "Cumul?? sur 90 jours"

#~ msgid "Cumul Amount Residual"
#~ msgstr "Cumuler le montant r??siduel"

#~ msgid "Cumul Balance"
#~ msgstr "Cumuler Solde"

#~ msgid "Cumul Current"
#~ msgstr "Cumuler le courant"

#~ msgid "Cumul Older"
#~ msgstr "Cumuler plus ancien"

#~ msgid "Currency Name"
#~ msgstr "Nom de la devise"

#~ msgid "Date Due"
#~ msgstr "Date d'??ch??ance"

#~ msgid "Ending blance cur."
#~ msgstr "Solde de cl??ture mon."

#~ msgid "Filter Analytic Tag"
#~ msgstr "Filtrer les ??tiquettes analytiques"

#~ msgid "Filter Cost Center"
#~ msgstr "Filtrer les centres de co??ts"

#~ msgid "Filter Journal"
#~ msgstr "Filtrer les journaux"

#~ msgid "Filter Partner"
#~ msgstr "Filtrer les partenaires"

#~ msgid "Final Amount Residual"
#~ msgstr "Montant r??siduel final"

#~ msgid "Final Amount Residual Currency"
#~ msgstr "Montant r??siduel final devise"

#~ msgid "Final Amount Total Due"
#~ msgstr "Montant total d?? final"

#~ msgid "Final Amount Total Due Currency"
#~ msgstr "Montant total d?? final en devise"

#~ msgid "Final Balance"
#~ msgstr "Balance finale"

#~ msgid "Final Balance Foreign Currency"
#~ msgstr "Balance finale devise ??trang??re"

#~ msgid "Final Credit"
#~ msgstr "Cr??dit final"

#~ msgid "Final Debit"
#~ msgstr "D??bit final"

#~ msgid "Group Option"
#~ msgstr "Option de groupe"

#~ msgid "Hide Account At 0"
#~ msgstr "Cacher les comptes ?? 0"

#~ msgid "Hide Line"
#~ msgstr "Cacher la ligne"

#~ msgid "Initial Balance"
#~ msgstr "Solde initial"

#~ msgid "Initial Balance Foreign Currency"
#~ msgstr "Solde initial devise ??trang??re"

#~ msgid "Initial Credit"
#~ msgstr "Cr??dit initial"

#~ msgid "Initial Debit"
#~ msgstr "D??bit initial"

#~ msgid "Initial blance cur."
#~ msgstr "Solde initial dev."

#~ msgid "Label"
#~ msgstr "Libell??"

#~ msgid "Matching Number"
#~ msgstr "Correspond au num??ro"

#~ msgid "Move"
#~ msgstr "Mouvement"

#~ msgid "Move Line"
#~ msgstr "Ligne d'??criture"

#~ msgid "No partner allocated"
#~ msgstr "Sans partenaire allou??"

#~ msgid "Only Posted Moves"
#~ msgstr "Uniquement les ??critures comptabilis??es"

#~ msgid "Parent"
#~ msgstr "Parent"

#~ msgid "Partner ID"
#~ msgstr "ID du partenaire"

#~ msgid "Percent Age 120 Days"
#~ msgstr "Pourcentage ?? 120 jours"

#~ msgid "Percent Age 30 Days"
#~ msgstr "Pourcentage ?? 30 jours"

#~ msgid "Percent Age 60 Days"
#~ msgstr "Pourcentage ?? 60 jours"

#~ msgid "Percent Age 90 Days"
#~ msgstr "Pourcentage ?? 90 jours"

#~ msgid "Percent Current"
#~ msgstr "Pourcentage actuel"

#~ msgid "Percent Older"
#~ msgstr "Pourcentage ancien"

#~ msgid "Period Balance"
#~ msgstr "Solde de la p??riode"

#~ msgid "Report"
#~ msgstr "Rapport"

#~ msgid "Show Cost Center"
#~ msgstr "Afficher les centres de co??ts"

#~ msgid "Sort Option"
#~ msgstr "Option de tri"

#~ msgid "Tax Code"
#~ msgstr "Code de t axe"

#~ msgid "Tax Detail"
#~ msgstr "D??tail de taxe"

#~ msgid "Tax ID"
#~ msgstr "ID de taxe"

#~ msgid "Taxes Description"
#~ msgstr "Description des taxes"

#~ msgid "Taxgroup"
#~ msgstr "Groupe de taxe"

#~ msgid "Taxtag"
#~ msgstr "??tiquette de taxe"

#~ msgid "Taxtags"
#~ msgstr "??tiquettes de taxe"
