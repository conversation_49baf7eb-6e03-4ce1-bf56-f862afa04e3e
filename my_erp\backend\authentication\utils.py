"""
Authentication Utilities - User Type Detection & Permissions
Following Odoo's user hierarchy model
"""
from django.contrib.auth.models import User


def get_user_type(user):
    """
    Determine user type following Odoo hierarchy
    
    Returns:
        str: User type ('system_super_user', 'company_super_user', 'company_admin', 'user', 'portal')
    """
    if not user or not user.is_authenticated:
        return 'anonymous'
    
    # 1. System Super User (Django superuser)
    if user.is_superuser:
        return 'system_super_user'
    
    # Get or create user profile
    try:
        profile = user.profile
    except:
        # Create profile if it doesn't exist
        from .models import UserProfile
        profile = UserProfile.objects.create(user=user)
    
    # 2. Company Super User
    if profile.is_company_super_user:
        return 'company_super_user'
    
    # 3. Portal User
    if profile.is_portal_user:
        return 'portal'
    
    # 4. Company Admin (Django staff)
    if user.is_staff:
        return 'company_admin'
    
    # 5. Regular User (default)
    return 'user'


def get_user_permissions(user):
    """
    Get user permissions based on their type and company memberships
    
    Returns:
        dict: Permission matrix for the user
    """
    user_type = get_user_type(user)
    
    base_permissions = {
        'companies': {
            'create': False,
            'read': [],
            'update': [],
            'delete': [],
            'switch': []
        },
        'users': {
            'create': False,
            'read': [],
            'update': [],
            'delete': [],
            'manage_all': False
        },
        'modules': {
            'access_all': False,
            'configure': [],
            'install': False
        }
    }
    
    if user_type == 'system_super_user':
        # System super user has all permissions
        return {
            'companies': {
                'create': True,
                'read': 'ALL',
                'update': 'ALL',
                'delete': 'ALL',
                'switch': 'ALL'
            },
            'users': {
                'create': True,
                'read': 'ALL',
                'update': 'ALL',
                'delete': 'ALL',
                'manage_all': True
            },
            'modules': {
                'access_all': True,
                'configure': 'ALL',
                'install': True
            }
        }
    
    elif user_type == 'company_super_user':
        # Company super user has full access within their companies
        user_companies = get_user_companies(user)
        return {
            'companies': {
                'create': False,
                'read': user_companies,
                'update': user_companies,
                'delete': [],
                'switch': user_companies
            },
            'users': {
                'create': 'WITHIN_COMPANIES',
                'read': 'WITHIN_COMPANIES',
                'update': 'WITHIN_COMPANIES',
                'delete': 'WITHIN_COMPANIES',
                'manage_all': 'WITHIN_COMPANIES'
            },
            'modules': {
                'access_all': 'WITHIN_COMPANIES',
                'configure': 'WITHIN_COMPANIES',
                'install': False
            }
        }
    
    elif user_type == 'company_admin':
        # Company admin has limited permissions within their companies
        user_companies = get_user_companies(user)
        return {
            'companies': {
                'create': False,
                'read': user_companies,
                'update': [],
                'delete': [],
                'switch': user_companies
            },
            'users': {
                'create': 'LIMITED',
                'read': 'LIMITED',
                'update': 'LIMITED',
                'delete': 'LIMITED',
                'manage_all': False
            },
            'modules': {
                'access_all': False,
                'configure': 'ASSIGNED_MODULES',
                'install': False
            }
        }
    
    elif user_type == 'portal':
        # Portal user has very limited access
        return {
            'companies': {
                'create': False,
                'read': 'PORTAL_ONLY',
                'update': [],
                'delete': [],
                'switch': []
            },
            'users': {
                'create': False,
                'read': 'SELF_ONLY',
                'update': 'SELF_ONLY',
                'delete': False,
                'manage_all': False
            },
            'modules': {
                'access_all': False,
                'configure': [],
                'install': False
            }
        }
    
    # Regular user - group-based permissions
    return base_permissions


def get_user_companies(user):
    """
    Get list of companies the user has access to
    
    Returns:
        list: Company IDs the user can access
    """
    if not user or not user.is_authenticated:
        return []
    
    user_type = get_user_type(user)
    
    if user_type == 'system_super_user':
        # System super user can access all companies
        from accounting.models import ResCompany
        return list(ResCompany.objects.values_list('id', flat=True))
    
    # Get companies from memberships
    try:
        company_ids = list(user.company_memberships.filter(is_active=True).values_list('company_id', flat=True))
        return company_ids
    except:
        return []


def can_user_manage_user(manager_user, target_user):
    """
    Check if manager_user can manage target_user
    
    Args:
        manager_user: User who wants to manage
        target_user: User to be managed
    
    Returns:
        bool: True if manager can manage target
    """
    manager_type = get_user_type(manager_user)
    target_type = get_user_type(target_user)
    
    # System super user can manage anyone
    if manager_type == 'system_super_user':
        return True
    
    # Nobody can manage system super user except another system super user
    if target_type == 'system_super_user':
        return False
    
    # Company super user can manage users within their companies
    if manager_type == 'company_super_user':
        manager_companies = set(get_user_companies(manager_user))
        target_companies = set(get_user_companies(target_user))
        
        # Must share at least one company
        if manager_companies.intersection(target_companies):
            # Can manage company admins, regular users, and portal users
            return target_type in ['company_admin', 'user', 'portal']
    
    # Company admin can manage regular users and portal users within their companies
    if manager_type == 'company_admin':
        manager_companies = set(get_user_companies(manager_user))
        target_companies = set(get_user_companies(target_user))
        
        # Must share at least one company
        if manager_companies.intersection(target_companies):
            # Can only manage regular users and portal users
            return target_type in ['user', 'portal']
    
    return False


def get_user_display_info(user):
    """
    Get user display information including type and permissions
    
    Returns:
        dict: User display information
    """
    user_type = get_user_type(user)
    
    type_labels = {
        'system_super_user': 'System Super User',
        'company_super_user': 'Company Super User',
        'company_admin': 'Company Administrator',
        'user': 'User',
        'portal': 'Portal User'
    }
    
    type_descriptions = {
        'system_super_user': 'Complete system access across all companies',
        'company_super_user': 'Full access within assigned companies',
        'company_admin': 'Administrative access within assigned companies',
        'user': 'Standard user with group-based permissions',
        'portal': 'External user with limited access'
    }
    
    return {
        'user_type': user_type,
        'type_label': type_labels.get(user_type, 'Unknown'),
        'type_description': type_descriptions.get(user_type, 'Unknown user type'),
        'companies': get_user_companies(user),
        'permissions': get_user_permissions(user)
    }
