#!/usr/bin/env python
"""
Test script for Withholding Tax API
"""
import requests
import json

def test_withholding_tax_api():
    base_url = 'http://localhost:8000/api'
    
    # Login first
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    print("🔐 Testing login...")
    login_response = requests.post(f'{base_url}/auth/login/', json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print(login_response.text)
        return
    
    token = login_response.json()['token']
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    print("✅ Login successful!")
    
    # Test GET withholding tax configurations
    print("\n📋 Testing GET withholding tax configurations...")
    get_response = requests.get(f'{base_url}/setup/withholding-tax-configurations/?company=1', headers=headers)
    print(f"Status: {get_response.status_code}")

    if get_response.status_code == 200:
        print("✅ GET request successful!")
        data = get_response.json()
        print(f"Found {len(data)} withholding tax configurations")
    else:
        print(f"❌ GET failed: {get_response.text}")
        return
    
    # Test POST - Create withholding tax configuration
    print("\n➕ Testing POST - Create withholding tax configuration...")
    withholding_tax_data = {
        'company': 1,
        'name': 'Professional Services TDS',
        'code': 'TDS194J',
        'rate': 10.0,
        'tax_type': 'professional_services',
        'threshold_amount': 30000,
        'exemption_limit': 0,
        'applicable_to': 'vendors',
        'calculation_base': 'gross_amount',
        'requires_certificate': True,
        'quarterly_return_required': True,
        'challan_required': True,
        'payment_due_date': 7,
        'return_due_date': 31,
        'effective_from': '2024-01-01',
        'is_active': True,
        'is_default': True
    }
    
    create_response = requests.post(f'{base_url}/setup/withholding-tax-configurations/',
                                  json=withholding_tax_data, headers=headers)
    print(f"Status: {create_response.status_code}")
    
    if create_response.status_code == 201:
        print("✅ CREATE successful!")
        created_tax = create_response.json()
        print(f"Created: {created_tax['name']} - {created_tax['rate']}%")
        tax_id = created_tax['id']
        
        # Test withholding tax calculation
        print(f"\n🧮 Testing withholding tax calculation for ID {tax_id}...")
        calc_data = {'payment_amount': 50000, 'cumulative_amount': 0}
        calc_response = requests.post(f'{base_url}/setup/withholding-tax-configurations/{tax_id}/calculate_withholding_tax/',
                                    json=calc_data, headers=headers)

        if calc_response.status_code == 200:
            print("✅ Withholding tax calculation successful!")
            calc_result = calc_response.json()
            print(f"Payment Amount: ${calc_result['payment_amount']:,}")
            print(f"Withholding Tax: ${calc_result['withholding_tax_amount']:,.2f}")
            print(f"Net Payment: ${calc_result['net_payment']:,.2f}")
            print(f"Effective Rate: {calc_result['effective_rate']:.2f}%")
            print(f"Is Applicable: {calc_result['is_applicable']}")
        else:
            print(f"❌ Withholding tax calculation failed: {calc_response.text}")
        
        # Test UPDATE
        print(f"\n✏️ Testing UPDATE for ID {tax_id}...")
        update_data = {
            'name': 'Updated Corporate Income Tax 2024',
            'rate': 30.0
        }
        update_response = requests.patch(f'{base_url}/setup/withholding-tax-configurations/{tax_id}/',
                                       json=update_data, headers=headers)
        
        if update_response.status_code == 200:
            print("✅ UPDATE successful!")
            updated_tax = update_response.json()
            print(f"Updated: {updated_tax['name']} - {updated_tax['rate']}%")
        else:
            print(f"❌ UPDATE failed: {update_response.text}")
        
        # Test DELETE
        print(f"\n🗑️ Testing DELETE for ID {tax_id}...")
        delete_response = requests.delete(f'{base_url}/setup/withholding-tax-configurations/{tax_id}/', headers=headers)
        
        if delete_response.status_code == 204:
            print("✅ DELETE successful!")
        else:
            print(f"❌ DELETE failed: {delete_response.text}")
            
    else:
        print(f"❌ CREATE failed: {create_response.text}")
    
    print("\n🎉 Withholding Tax API testing completed!")

if __name__ == '__main__':
    test_withholding_tax_api()
