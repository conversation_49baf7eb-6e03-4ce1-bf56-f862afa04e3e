#!/usr/bin/env python
"""
Test script for Income Tax API
"""
import requests
import json

def test_income_tax_api():
    base_url = 'http://localhost:8000/api'
    
    # Login first
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    print("🔐 Testing login...")
    login_response = requests.post(f'{base_url}/auth/login/', json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print(login_response.text)
        return
    
    token = login_response.json()['token']
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    print("✅ Login successful!")
    
    # Test GET income tax configurations
    print("\n📋 Testing GET income tax configurations...")
    get_response = requests.get(f'{base_url}/setup/income-tax-configurations/?company=1', headers=headers)
    print(f"Status: {get_response.status_code}")
    
    if get_response.status_code == 200:
        print("✅ GET request successful!")
        data = get_response.json()
        print(f"Found {len(data)} income tax configurations")
    else:
        print(f"❌ GET failed: {get_response.text}")
        return
    
    # Test POST - Create income tax configuration
    print("\n➕ Testing POST - Create income tax configuration...")
    income_tax_data = {
        'company': 1,
        'name': 'Corporate Income Tax 2024',
        'rate': 25.0,
        'tax_year_start': '2024-01-01',
        'tax_year_end': '2024-12-31',
        'min_income': 0,
        'calculation_method': 'flat',
        'standard_deduction': 12000,
        'personal_exemption': 4000,
        'advance_tax_required': False,
        'quarterly_filing': True,
        'is_active': True,
        'is_default': True
    }
    
    create_response = requests.post(f'{base_url}/setup/income-tax-configurations/', 
                                  json=income_tax_data, headers=headers)
    print(f"Status: {create_response.status_code}")
    
    if create_response.status_code == 201:
        print("✅ CREATE successful!")
        created_tax = create_response.json()
        print(f"Created: {created_tax['name']} - {created_tax['rate']}%")
        tax_id = created_tax['id']
        
        # Test tax calculation
        print(f"\n🧮 Testing tax calculation for ID {tax_id}...")
        calc_data = {'income_amount': 100000}
        calc_response = requests.post(f'{base_url}/setup/income-tax-configurations/{tax_id}/calculate_tax/', 
                                    json=calc_data, headers=headers)
        
        if calc_response.status_code == 200:
            print("✅ Tax calculation successful!")
            calc_result = calc_response.json()
            print(f"Income: ${calc_result['income_amount']:,}")
            print(f"Tax: ${calc_result['tax_amount']:,.2f}")
            print(f"Effective Rate: {calc_result['effective_rate']:.2f}%")
        else:
            print(f"❌ Tax calculation failed: {calc_response.text}")
        
        # Test UPDATE
        print(f"\n✏️ Testing UPDATE for ID {tax_id}...")
        update_data = {
            'name': 'Updated Corporate Income Tax 2024',
            'rate': 30.0
        }
        update_response = requests.patch(f'{base_url}/setup/income-tax-configurations/{tax_id}/', 
                                       json=update_data, headers=headers)
        
        if update_response.status_code == 200:
            print("✅ UPDATE successful!")
            updated_tax = update_response.json()
            print(f"Updated: {updated_tax['name']} - {updated_tax['rate']}%")
        else:
            print(f"❌ UPDATE failed: {update_response.text}")
        
        # Test DELETE
        print(f"\n🗑️ Testing DELETE for ID {tax_id}...")
        delete_response = requests.delete(f'{base_url}/setup/income-tax-configurations/{tax_id}/', headers=headers)
        
        if delete_response.status_code == 204:
            print("✅ DELETE successful!")
        else:
            print(f"❌ DELETE failed: {delete_response.text}")
            
    else:
        print(f"❌ CREATE failed: {create_response.text}")
    
    print("\n🎉 Income Tax API testing completed!")

if __name__ == '__main__':
    test_income_tax_api()
