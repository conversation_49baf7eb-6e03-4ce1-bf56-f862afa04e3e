/**
 * Error Boundary Component - Professional Error Handling
 * Catches JavaScript errors anywhere in the component tree
 */
import React from 'react';
import { Result, Button, Typography, Card, Space, Alert } from 'antd';
import { 
  BugOutlined, 
  ReloadOutlined, 
  HomeOutlined,
  WarningOutlined 
} from '@ant-design/icons';

const { Paragraph, Text } = Typography;

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { 
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error Boundary caught an error:', error, errorInfo);
    }

    // In production, you would send this to your error reporting service
    // Example: Sentry, LogRocket, Bugsnag, etc.
    this.logErrorToService(error, errorInfo);
  }

  logErrorToService = (error, errorInfo) => {
    // Example error logging service integration
    try {
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorId: this.state.errorId,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: localStorage.getItem('userId') || 'anonymous'
      };

      // Send to your error tracking service
      // fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorData)
      // });

      console.error('Error logged:', errorData);
    } catch (loggingError) {
      console.error('Failed to log error:', loggingError);
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null 
    });
  };

  render() {
    if (this.state.hasError) {
      const isDevelopment = process.env.NODE_ENV === 'development';
      
      return (
        <div style={{ 
          padding: '50px', 
          minHeight: '100vh', 
          background: '#f0f2f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Card style={{ maxWidth: 800, width: '100%' }}>
            <Result
              status="error"
              icon={<BugOutlined style={{ color: '#ff4d4f' }} />}
              title="Oops! Something went wrong"
              subTitle="We're sorry, but something unexpected happened. Our team has been notified."
              extra={[
                <Space key="actions" wrap>
                  <Button 
                    type="primary" 
                    icon={<ReloadOutlined />}
                    onClick={this.handleReload}
                  >
                    Reload Page
                  </Button>
                  <Button 
                    icon={<HomeOutlined />}
                    onClick={this.handleGoHome}
                  >
                    Go to Dashboard
                  </Button>
                  <Button 
                    type="dashed"
                    onClick={this.handleRetry}
                  >
                    Try Again
                  </Button>
                </Space>
              ]}
            />

            {/* Error ID for support */}
            <Alert
              message="Error Reference"
              description={
                <Space direction="vertical" size="small">
                  <Text>
                    If you need support, please provide this error ID: 
                    <Text code copyable style={{ marginLeft: 8 }}>
                      {this.state.errorId}
                    </Text>
                  </Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    Time: {new Date().toLocaleString()}
                  </Text>
                </Space>
              }
              type="info"
              showIcon
              style={{ marginTop: 24 }}
            />

            {/* Development Error Details */}
            {isDevelopment && this.state.error && (
              <Card 
                title={
                  <Space>
                    <WarningOutlined style={{ color: '#faad14' }} />
                    Development Error Details
                  </Space>
                }
                style={{ marginTop: 24 }}
                size="small"
              >
                <Alert
                  message="Error Message"
                  description={
                    <Text code style={{ whiteSpace: 'pre-wrap' }}>
                      {this.state.error.message}
                    </Text>
                  }
                  type="error"
                  style={{ marginBottom: 16 }}
                />
                
                {this.state.error.stack && (
                  <Alert
                    message="Stack Trace"
                    description={
                      <Text code style={{ 
                        whiteSpace: 'pre-wrap', 
                        fontSize: '11px',
                        display: 'block',
                        maxHeight: '200px',
                        overflow: 'auto'
                      }}>
                        {this.state.error.stack}
                      </Text>
                    }
                    type="warning"
                    style={{ marginBottom: 16 }}
                  />
                )}
                
                {this.state.errorInfo && this.state.errorInfo.componentStack && (
                  <Alert
                    message="Component Stack"
                    description={
                      <Text code style={{ 
                        whiteSpace: 'pre-wrap', 
                        fontSize: '11px',
                        display: 'block',
                        maxHeight: '200px',
                        overflow: 'auto'
                      }}>
                        {this.state.errorInfo.componentStack}
                      </Text>
                    }
                    type="info"
                  />
                )}
              </Card>
            )}

            {/* Help Information */}
            <Card 
              title="Need Help?" 
              style={{ marginTop: 24 }}
              size="small"
            >
              <Paragraph>
                <Text strong>What you can do:</Text>
              </Paragraph>
              <ul>
                <li>Try reloading the page - this often resolves temporary issues</li>
                <li>Check your internet connection</li>
                <li>Clear your browser cache and cookies</li>
                <li>Try using a different browser</li>
                <li>Contact support if the problem persists</li>
              </ul>
              
              <Paragraph style={{ marginTop: 16 }}>
                <Text strong>Contact Support:</Text>
              </Paragraph>
              <Space direction="vertical" size="small">
                <Text>Email: <EMAIL></Text>
                <Text>Phone: +****************</Text>
                <Text>
                  Include the error ID above when contacting support for faster resolution.
                </Text>
              </Space>
            </Card>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
