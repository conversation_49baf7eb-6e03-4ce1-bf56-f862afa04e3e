"""
CRM Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from datetime import date
from .models import CrmTeam, CrmStage, CrmLostReason, CrmLead, CrmActivity


@admin.register(CrmTeam)
class CrmTeamAdmin(admin.ModelAdmin):
    list_display = ['name', 'user_id', 'use_leads', 'use_opportunities', 'invoiced_target_display', 'active']
    list_filter = ['active', 'use_leads', 'use_opportunities', 'company_id']
    search_fields = ['name', 'user_id__username']
    filter_horizontal = ['member_ids']
    ordering = ['sequence', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'active', 'sequence', 'color')
        }),
        ('Team Management', {
            'fields': ('user_id', 'member_ids')
        }),
        ('CRM Configuration', {
            'fields': ('use_leads', 'use_opportunities')
        }),
        ('Targets', {
            'fields': ('invoiced_target', 'quotation_count', 'sale_order_count')
        }),
        ('Lead Assignment', {
            'fields': ('assignment_enabled', 'assignment_auto_enabled', 'assignment_optout'),
            'classes': ('collapse',)
        }),
        ('Advanced', {
            'fields': ('domain',),
            'classes': ('collapse',)
        }),
    )

    def invoiced_target_display(self, obj):
        return format_html(
            '<span style="font-weight: bold; color: green;">${:,.2f}</span>',
            obj.invoiced_target
        )
    invoiced_target_display.short_description = 'Monthly Target'


@admin.register(CrmStage)
class CrmStageAdmin(admin.ModelAdmin):
    list_display = ['name', 'sequence', 'is_won', 'fold', 'team_count']
    list_filter = ['is_won', 'fold']
    search_fields = ['name', 'requirements']
    filter_horizontal = ['team_ids']
    ordering = ['sequence', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'sequence')
        }),
        ('Stage Properties', {
            'fields': ('is_won', 'fold')
        }),
        ('Configuration', {
            'fields': ('requirements', 'team_ids')
        }),
    )

    def team_count(self, obj):
        count = obj.team_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    team_count.short_description = 'Teams'


@admin.register(CrmLostReason)
class CrmLostReasonAdmin(admin.ModelAdmin):
    list_display = ['name', 'active']
    list_filter = ['active']
    search_fields = ['name']
    ordering = ['name']


class CrmActivityInline(admin.TabularInline):
    model = CrmActivity
    extra = 0
    fields = ['activity_type', 'summary', 'date_deadline', 'user_id', 'state']
    readonly_fields = ['state']


@admin.register(CrmLead)
class CrmLeadAdmin(admin.ModelAdmin):
    list_display = ['name', 'partner_name', 'type', 'stage_id', 'user_id', 'team_id', 'expected_revenue_display', 'probability_display', 'priority', 'active']
    list_filter = ['type', 'priority', 'active', 'stage_id', 'team_id', 'user_id', 'country_id', 'company_id']
    search_fields = ['name', 'partner_name', 'contact_name', 'email_from', 'phone', 'mobile']
    inlines = [CrmActivityInline]
    ordering = ['-create_date', '-write_date']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'type', 'priority', 'active')
        }),
        ('Contact Information', {
            'fields': ('partner_id', 'partner_name', 'contact_name', 'email_from', 'phone', 'mobile', 'website')
        }),
        ('Address', {
            'fields': ('street', 'street2', 'city', 'state_id', 'zip', 'country_id'),
            'classes': ('collapse',)
        }),
        ('Sales Information', {
            'fields': ('user_id', 'team_id', 'stage_id')
        }),
        ('Opportunity Details', {
            'fields': ('expected_revenue', 'prorated_revenue', 'recurring_revenue', 'recurring_plan', 'probability', 'date_deadline')
        }),
        ('Dates', {
            'fields': ('date_closed', 'date_conversion', 'date_last_stage_update'),
            'classes': ('collapse',)
        }),
        ('Marketing', {
            'fields': ('source_id', 'medium_id', 'campaign_id', 'referred'),
            'classes': ('collapse',)
        }),
        ('Lost Information', {
            'fields': ('lost_reason_id',),
            'classes': ('collapse',)
        }),
        ('Description', {
            'fields': ('description', 'tag_ids'),
            'classes': ('collapse',)
        }),
        ('System', {
            'fields': ('currency_id',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['prorated_revenue', 'date_closed', 'date_conversion', 'date_last_stage_update']

    def expected_revenue_display(self, obj):
        if obj.expected_revenue:
            return format_html(
                '<span style="font-weight: bold; color: green;">{} {:,.2f}</span>',
                obj.currency_id, obj.expected_revenue
            )
        return '-'
    expected_revenue_display.short_description = 'Expected Revenue'

    def probability_display(self, obj):
        color = 'green' if obj.probability >= 75 else 'orange' if obj.probability >= 50 else 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.0f}%</span>',
            color, obj.probability
        )
    probability_display.short_description = 'Probability'

    actions = ['action_convert_to_opportunity', 'action_create_partner', 'action_set_won', 'action_set_lost']

    def action_convert_to_opportunity(self, request, queryset):
        converted = 0
        for lead in queryset.filter(type='lead'):
            if lead.convert_to_opportunity():
                converted += 1
        self.message_user(request, f'{converted} leads converted to opportunities successfully.')
    action_convert_to_opportunity.short_description = "Convert leads to opportunities"

    def action_create_partner(self, request, queryset):
        created = 0
        for lead in queryset.filter(partner_id__isnull=True):
            if lead.create_partner():
                created += 1
        self.message_user(request, f'{created} partners created successfully.')
    action_create_partner.short_description = "Create partners from leads"

    def action_set_won(self, request, queryset):
        won = 0
        for lead in queryset.filter(type='opportunity'):
            if lead.action_set_won():
                won += 1
        self.message_user(request, f'{won} opportunities marked as won successfully.')
    action_set_won.short_description = "Mark opportunities as won"

    def action_set_lost(self, request, queryset):
        lost = 0
        for lead in queryset.filter(type='opportunity'):
            if lead.action_set_lost():
                lost += 1
        self.message_user(request, f'{lost} opportunities marked as lost successfully.')
    action_set_lost.short_description = "Mark opportunities as lost"


@admin.register(CrmActivity)
class CrmActivityAdmin(admin.ModelAdmin):
    list_display = ['summary', 'lead_id', 'activity_type', 'date_deadline', 'user_id', 'state_display', 'create_uid']
    list_filter = ['activity_type', 'state', 'date_deadline', 'user_id', 'company_id']
    search_fields = ['summary', 'note', 'lead_id__name', 'lead_id__partner_name']
    ordering = ['date_deadline', 'create_date']

    fieldsets = (
        ('Activity Information', {
            'fields': ('activity_type', 'summary', 'note')
        }),
        ('Schedule', {
            'fields': ('date_deadline', 'state')
        }),
        ('Assignment', {
            'fields': ('user_id', 'create_uid')
        }),
        ('Related', {
            'fields': ('lead_id',)
        }),
    )

    readonly_fields = ['state', 'create_uid']

    def state_display(self, obj):
        colors = {
            'overdue': 'red',
            'today': 'orange',
            'planned': 'blue',
            'done': 'green'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.state, 'black'), obj.get_state_display()
        )
    state_display.short_description = 'State'

    actions = ['action_mark_done']

    def action_mark_done(self, request, queryset):
        done = 0
        for activity in queryset:
            if activity.action_done():
                done += 1
        self.message_user(request, f'{done} activities marked as done successfully.')
    action_mark_done.short_description = "Mark activities as done"

    def save_model(self, request, obj, form, change):
        if not change:  # New object
            obj.create_uid = request.user
        super().save_model(request, obj, form, change)


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - CRM Module"
