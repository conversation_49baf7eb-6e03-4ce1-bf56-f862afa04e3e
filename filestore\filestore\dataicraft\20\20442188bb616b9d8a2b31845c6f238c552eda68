{"version": 12, "sheets": [{"id": "a7cd7db1-9407-4895-82f2-7657102c7688", "name": "Dashboard", "colNumber": 7, "rowNumber": 57, "rows": {"6": {"size": 40}, "18": {"size": 40}, "19": {"size": 40}, "31": {"size": 40}, "32": {"size": 40}, "44": {"size": 40}, "45": {"size": 40}}, "cols": {"0": {"size": 225}, "1": {"size": 150}, "2": {"size": 100}, "3": {"size": 50}, "4": {"size": 225}, "5": {"size": 150}, "6": {"size": 100}}, "merges": [], "cells": {"A7": {"style": 1, "content": "[Invoiced by Month](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[\"&\",[\"state\",\"not in\",[\"draft\",\"cancel\"]],\"|\",[\"move_type\",\"=\",\"out_invoice\"],[\"move_type\",\"=\",\"out_refund\"]],\"context\":{\"group_by\":[\"invoice_date\"],\"graph_measure\":\"price_subtotal\",\"graph_mode\":\"line\",\"graph_groupbys\":[\"invoice_date:month\"]},\"modelName\":\"account.invoice.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Invoices Analysis\",\"positional\":true})", "border": 1}, "A19": {"style": 1, "content": "[Top Invoices](odoo://view/{\"viewType\":\"list\",\"action\":{\"domain\":[[\"move_type\",\"=\",\"out_invoice\"]],\"context\":{\"group_by\":[]},\"modelName\":\"account.move\",\"views\":[[false,\"list\"],[false,\"kanban\"],[false,\"form\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Invoices\"})", "border": 1}, "A20": {"style": 2, "content": "=_t(\"Reference\")", "border": 2}, "A21": {"style": 3, "format": 1, "content": "=ODOO.LIST(1,1,\"name\")"}, "A22": {"style": 4, "format": 1, "content": "=ODOO.LIST(1,2,\"name\")"}, "A23": {"style": 3, "format": 1, "content": "=ODOO.LIST(1,3,\"name\")"}, "A24": {"style": 4, "format": 1, "content": "=ODOO.LIST(1,4,\"name\")"}, "A25": {"style": 3, "format": 1, "content": "=ODOO.LIST(1,5,\"name\")"}, "A26": {"style": 4, "format": 1, "content": "=ODOO.LIST(1,6,\"name\")"}, "A27": {"style": 3, "format": 1, "content": "=ODOO.LIST(1,7,\"name\")"}, "A28": {"style": 4, "format": 1, "content": "=ODOO.LIST(1,8,\"name\")"}, "A29": {"style": 3, "format": 1, "content": "=ODOO.LIST(1,9,\"name\")"}, "A30": {"style": 5, "content": "=ODOO.LIST(1,10,\"name\")"}, "A31": {"style": 6}, "A32": {"style": 1, "content": "[Top Countries](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"state\",\"not in\",[\"draft\",\"cancel\"]],\"&\",[\"country_id\",\"!=\",false],[\"price_subtotal\",\">=\",0]],\"context\":{\"group_by\":[\"country_id\"],\"pivot_measures\":[\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"country_id\"]},\"modelName\":\"account.invoice.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"list\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Invoices Analysis\"})", "border": 1}, "A33": {"style": 7, "content": "=_t(\"Country\")", "border": 2}, "A34": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(2,\"#country_id\",1)"}, "A35": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(2,\"#country_id\",2)"}, "A36": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(2,\"#country_id\",3)"}, "A37": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(2,\"#country_id\",4)"}, "A38": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(2,\"#country_id\",5)"}, "A39": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(2,\"#country_id\",6)"}, "A40": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(2,\"#country_id\",7)"}, "A41": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(2,\"#country_id\",8)"}, "A42": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(2,\"#country_id\",9)"}, "A43": {"style": 10}, "A44": {"style": 11, "content": "=ODOO.PIVOT.HEADER(2,\"#country_id\",10)"}, "A45": {"style": 1, "content": "[Top Products](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"state\",\"not in\",[\"draft\",\"cancel\"]],\"&\",[\"product_id\",\"!=\",false],[\"price_subtotal\",\">=\",0]],\"context\":{\"group_by\":[\"product_id\"],\"pivot_measures\":[\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"product_id\"]},\"modelName\":\"account.invoice.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"list\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Invoices Analysis\"})", "border": 1}, "A46": {"style": 7, "content": "=_t(\"Product\")", "border": 2}, "A47": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",1)"}, "A48": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",2)"}, "A49": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",3)"}, "A50": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",4)"}, "A51": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",5)"}, "A52": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",6)"}, "A53": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",7)"}, "A54": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",8)"}, "A55": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",9)"}, "A56": {"style": 9, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",10)"}, "B19": {"style": 12, "border": 1}, "B20": {"style": 13, "content": "=_t(\"Salesperson\")", "border": 2}, "B21": {"style": 14, "format": 1, "content": "=ODOO.LIST(1,1,\"user_id\")"}, "B22": {"content": "=ODOO.LIST(1,2,\"user_id\")"}, "B23": {"style": 15, "content": "=ODOO.LIST(1,3,\"user_id\")"}, "B24": {"content": "=ODOO.LIST(1,4,\"user_id\")"}, "B25": {"style": 15, "content": "=ODOO.LIST(1,5,\"user_id\")"}, "B26": {"content": "=ODOO.LIST(1,6,\"user_id\")"}, "B27": {"style": 15, "content": "=ODOO.LIST(1,7,\"user_id\")"}, "B28": {"content": "=ODOO.LIST(1,8,\"user_id\")"}, "B29": {"style": 15, "content": "=ODOO.LIST(1,9,\"user_id\")"}, "B30": {"content": "=ODOO.LIST(1,10,\"user_id\")"}, "B31": {"style": 6}, "B32": {"style": 12, "border": 1}, "B33": {"style": 16, "content": "=_t(\"Amount\")", "border": 2}, "B34": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#country_id\",1)"}, "B35": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#country_id\",2)"}, "B36": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#country_id\",3)"}, "B37": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#country_id\",4)"}, "B38": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#country_id\",5)"}, "B39": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#country_id\",6)"}, "B40": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#country_id\",7)"}, "B41": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#country_id\",8)"}, "B42": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#country_id\",9)"}, "B43": {"format": 1}, "B44": {"format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#country_id\",10)"}, "B45": {"style": 12, "border": 1}, "B46": {"style": 16, "content": "=_t(\"Amount\")", "border": 2}, "B47": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#product_id\",1)"}, "B48": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#product_id\",2)"}, "B49": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#product_id\",3)"}, "B50": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#product_id\",4)"}, "B51": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#product_id\",5)"}, "B52": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#product_id\",6)"}, "B53": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#product_id\",7)"}, "B54": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#product_id\",8)"}, "B55": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#product_id\",9)"}, "B56": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#product_id\",10)"}, "C19": {"style": 12, "border": 1}, "C20": {"style": 13, "content": "=_t(\"Status\")", "border": 2}, "C21": {"style": 14, "format": 1, "content": "=ODOO.LIST(1,1,\"payment_state\")"}, "C22": {"content": "=ODOO.LIST(1,2,\"payment_state\")"}, "C23": {"style": 15, "content": "=ODOO.LIST(1,3,\"payment_state\")"}, "C24": {"content": "=ODOO.LIST(1,4,\"payment_state\")"}, "C25": {"style": 15, "content": "=ODOO.LIST(1,5,\"payment_state\")"}, "C26": {"content": "=ODOO.LIST(1,6,\"payment_state\")"}, "C27": {"style": 15, "content": "=ODOO.LIST(1,7,\"payment_state\")"}, "C28": {"content": "=ODOO.LIST(1,8,\"payment_state\")"}, "C29": {"style": 15, "content": "=ODOO.LIST(1,9,\"payment_state\")"}, "C30": {"content": "=ODOO.LIST(1,10,\"payment_state\")"}, "C31": {"style": 6}, "C32": {"style": 12, "border": 1}, "C33": {"style": 16, "content": "=_t(\"Ratio\")", "border": 2}, "C34": {"style": 17, "format": 2, "content": "=iferror(if(B34,B34/ODOO.PIVOT(2,\"price_subtotal\"),\"\"),\"\")"}, "C35": {"style": 6, "format": 2, "content": "=iferror(if(B35,B35/ODOO.PIVOT(2,\"price_subtotal\"),\"\"),\"\")"}, "C36": {"style": 17, "format": 2, "content": "=iferror(if(B36,B36/ODOO.PIVOT(2,\"price_subtotal\"),\"\"),\"\")"}, "C37": {"style": 6, "format": 2, "content": "=iferror(if(B37,B37/ODOO.PIVOT(2,\"price_subtotal\"),\"\"),\"\")"}, "C38": {"style": 17, "format": 2, "content": "=iferror(if(B38,B38/ODOO.PIVOT(2,\"price_subtotal\"),\"\"),\"\")"}, "C39": {"style": 6, "format": 2, "content": "=iferror(if(B39,B39/ODOO.PIVOT(2,\"price_subtotal\"),\"\"),\"\")"}, "C40": {"style": 17, "format": 2, "content": "=iferror(if(B40,B40/ODOO.PIVOT(2,\"price_subtotal\"),\"\"),\"\")"}, "C41": {"style": 6, "format": 2, "content": "=iferror(if(B41,B41/ODOO.PIVOT(2,\"price_subtotal\"),\"\"),\"\")"}, "C42": {"style": 17, "format": 2, "content": "=iferror(if(B42,B42/ODOO.PIVOT(2,\"price_subtotal\"),\"\"),\"\")"}, "C43": {"format": 3}, "C44": {"content": "=iferror(if(B44,B44/ODOO.PIVOT(2,\"price_subtotal\"),\"\"),\"\")"}, "C45": {"style": 12, "border": 1}, "C46": {"style": 16, "content": "=_t(\"Ratio\")", "border": 2}, "C47": {"style": 17, "format": 2, "content": "=iferror(if(B47,B47/ODOO.PIVOT(3,\"price_subtotal\"),\"\"))"}, "C48": {"style": 6, "format": 2, "content": "=iferror(if(B48,B48/ODOO.PIVOT(3,\"price_subtotal\"),\"\"))"}, "C49": {"style": 17, "format": 2, "content": "=iferror(if(B49,B49/ODOO.PIVOT(3,\"price_subtotal\"),\"\"))"}, "C50": {"style": 6, "format": 2, "content": "=iferror(if(B50,B50/ODOO.PIVOT(3,\"price_subtotal\"),\"\"))"}, "C51": {"style": 17, "format": 2, "content": "=iferror(if(B51,B51/ODOO.PIVOT(3,\"price_subtotal\"),\"\"))"}, "C52": {"style": 6, "format": 2, "content": "=iferror(if(B52,B52/ODOO.PIVOT(3,\"price_subtotal\"),\"\"))"}, "C53": {"style": 17, "format": 2, "content": "=iferror(if(B53,B53/ODOO.PIVOT(3,\"price_subtotal\"),\"\"))"}, "C54": {"style": 6, "format": 2, "content": "=iferror(if(B54,B54/ODOO.PIVOT(3,\"price_subtotal\"),\"\"))"}, "C55": {"style": 17, "format": 2, "content": "=iferror(if(B55,B55/ODOO.PIVOT(3,\"price_subtotal\"),\"\"))"}, "C56": {"style": 6, "format": 2, "content": "=iferror(if(B56,B56/ODOO.PIVOT(3,\"price_subtotal\"),\"\"))"}, "D20": {"style": 13, "content": "=_t(\"Customer\")", "border": 2}, "D21": {"style": 14, "format": 1, "content": "=ODOO.LIST(1,1,\"partner_id\")"}, "D22": {"content": "=ODOO.LIST(1,2,\"partner_id\")"}, "D23": {"style": 15, "content": "=ODOO.LIST(1,3,\"partner_id\")"}, "D24": {"content": "=ODOO.LIST(1,4,\"partner_id\")"}, "D25": {"style": 15, "content": "=ODOO.LIST(1,5,\"partner_id\")"}, "D26": {"content": "=ODOO.LIST(1,6,\"partner_id\")"}, "D27": {"style": 15, "content": "=ODOO.LIST(1,7,\"partner_id\")"}, "D28": {"content": "=ODOO.LIST(1,8,\"partner_id\")"}, "D29": {"style": 15, "content": "=ODOO.LIST(1,9,\"partner_id\")"}, "D30": {"content": "=ODOO.LIST(1,10,\"partner_id\")"}, "D31": {"style": 6}, "D33": {"style": 6}, "D34": {"style": 6}, "D35": {"style": 6}, "D36": {"style": 6}, "D37": {"style": 6}, "D38": {"style": 6}, "D39": {"style": 6}, "D40": {"style": 6}, "D41": {"style": 6}, "D42": {"style": 6}, "D46": {"style": 6}, "D47": {"style": 6}, "D48": {"style": 6}, "D49": {"style": 6}, "D50": {"style": 6}, "D51": {"style": 6}, "D52": {"style": 6}, "D53": {"style": 6}, "D54": {"style": 6}, "D55": {"style": 6}, "D56": {"style": 6}, "E19": {"style": 1, "border": 1}, "E20": {"style": 13, "border": 2}, "E21": {"style": 14, "format": 1}, "E23": {"style": 15}, "E25": {"style": 15}, "E27": {"style": 15}, "E29": {"style": 15}, "E31": {"style": 6}, "E32": {"style": 1, "content": "[Top Categories](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"state\",\"not in\",[\"draft\",\"cancel\"]],\"&\",[\"product_categ_id\",\"!=\",false],[\"price_subtotal\",\">=\",0]],\"context\":{\"group_by\":[\"product_categ_id\"],\"pivot_measures\":[\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"product_categ_id\"]},\"modelName\":\"account.invoice.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"list\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Invoices Analysis\"})", "border": 1}, "E33": {"style": 7, "content": "=_t(\"Top Categories\")", "border": 2}, "E34": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(1,\"#product_categ_id\",1)"}, "E35": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(1,\"#product_categ_id\",2)"}, "E36": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(1,\"#product_categ_id\",3)"}, "E37": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(1,\"#product_categ_id\",4)"}, "E38": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(1,\"#product_categ_id\",5)"}, "E39": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(1,\"#product_categ_id\",6)"}, "E40": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(1,\"#product_categ_id\",7)"}, "E41": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(1,\"#product_categ_id\",8)"}, "E42": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(1,\"#product_categ_id\",9)"}, "E43": {"style": 10}, "E44": {"style": 11, "content": "=ODOO.PIVOT.HEADER(1,\"#product_categ_id\",10)"}, "E45": {"style": 1, "content": "[Top Salespeople](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"state\",\"not in\",[\"draft\",\"cancel\"]],\"&\",[\"invoice_user_id\",\"!=\",false],[\"price_subtotal\",\">=\",0]],\"context\":{\"group_by\":[\"invoice_user_id\"],\"pivot_measures\":[\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"invoice_user_id\"]},\"modelName\":\"account.invoice.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"list\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Invoices Analysis\"})", "border": 1}, "E46": {"style": 7, "content": "=_t(\"Salesperson\")", "border": 2}, "E47": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(4,\"#invoice_user_id\",1)"}, "E48": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(4,\"#invoice_user_id\",2)"}, "E49": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(4,\"#invoice_user_id\",3)"}, "E50": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(4,\"#invoice_user_id\",4)"}, "E51": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(4,\"#invoice_user_id\",5)"}, "E52": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(4,\"#invoice_user_id\",6)"}, "E53": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(4,\"#invoice_user_id\",7)"}, "E54": {"style": 9, "format": 1, "content": "=ODOO.PIVOT.HEADER(4,\"#invoice_user_id\",8)"}, "E55": {"style": 8, "format": 1, "content": "=ODOO.PIVOT.HEADER(4,\"#invoice_user_id\",9)"}, "E56": {"style": 9, "content": "=ODOO.PIVOT.HEADER(4,\"#invoice_user_id\",10)"}, "F20": {"style": 13, "content": "=_t(\"Date\")", "border": 2}, "F21": {"style": 15, "content": "=ODOO.LIST(1,1,\"invoice_date\")"}, "F22": {"content": "=ODOO.LIST(1,2,\"invoice_date\")"}, "F23": {"style": 15, "content": "=ODOO.LIST(1,3,\"invoice_date\")"}, "F24": {"content": "=ODOO.LIST(1,4,\"invoice_date\")"}, "F25": {"style": 15, "content": "=ODOO.LIST(1,5,\"invoice_date\")"}, "F26": {"content": "=ODOO.LIST(1,6,\"invoice_date\")"}, "F27": {"style": 15, "content": "=ODOO.LIST(1,7,\"invoice_date\")"}, "F28": {"content": "=ODOO.LIST(1,8,\"invoice_date\")"}, "F29": {"style": 15, "content": "=ODOO.LIST(1,9,\"invoice_date\")"}, "F30": {"content": "=ODOO.LIST(1,10,\"invoice_date\")"}, "F31": {"style": 6}, "F32": {"style": 12, "border": 1}, "F33": {"style": 16, "content": "=_t(\"Amount\")", "border": 2}, "F34": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_categ_id\",1)"}, "F35": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_categ_id\",2)"}, "F36": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_categ_id\",3)"}, "F37": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_categ_id\",4)"}, "F38": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_categ_id\",5)"}, "F39": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_categ_id\",6)"}, "F40": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_categ_id\",7)"}, "F41": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_categ_id\",8)"}, "F42": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_categ_id\",9)"}, "F43": {"format": 1}, "F44": {"format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_categ_id\",10)"}, "F45": {"style": 12, "border": 1}, "F46": {"style": 16, "content": "=_t(\"Amount\")", "border": 2}, "F47": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#invoice_user_id\",1)"}, "F48": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#invoice_user_id\",2)"}, "F49": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#invoice_user_id\",3)"}, "F50": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#invoice_user_id\",4)"}, "F51": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#invoice_user_id\",5)"}, "F52": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#invoice_user_id\",6)"}, "F53": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#invoice_user_id\",7)"}, "F54": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#invoice_user_id\",8)"}, "F55": {"style": 17, "format": 1, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#invoice_user_id\",9)"}, "F56": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#invoice_user_id\",10)"}, "G20": {"style": 18, "content": "=_t(\"Amount\")", "border": 2}, "G21": {"style": 17, "content": "=ODOO.LIST(1,1,\"amount_untaxed_signed\")"}, "G22": {"style": 6, "content": "=ODOO.LIST(1,2,\"amount_untaxed_signed\")"}, "G23": {"style": 17, "content": "=ODOO.LIST(1,3,\"amount_untaxed_signed\")"}, "G24": {"style": 6, "content": "=ODOO.LIST(1,4,\"amount_untaxed_signed\")"}, "G25": {"style": 17, "content": "=ODOO.LIST(1,5,\"amount_untaxed_signed\")"}, "G26": {"style": 6, "content": "=ODOO.LIST(1,6,\"amount_untaxed_signed\")"}, "G27": {"style": 17, "content": "=ODOO.LIST(1,7,\"amount_untaxed_signed\")"}, "G28": {"style": 6, "content": "=ODOO.LIST(1,8,\"amount_untaxed_signed\")"}, "G29": {"style": 17, "content": "=ODOO.LIST(1,9,\"amount_untaxed_signed\")"}, "G30": {"style": 6, "content": "=ODOO.LIST(1,10,\"amount_untaxed_signed\")"}, "G31": {"style": 6}, "G32": {"style": 12, "border": 1}, "G33": {"style": 16, "content": "=_t(\"Ratio\")", "border": 2}, "G34": {"style": 17, "format": 2, "content": "=iferror(if(F34,F34/ODOO.PIVOT(1,\"price_subtotal\"),\"\"),\"\")"}, "G35": {"style": 6, "format": 2, "content": "=iferror(if(F35,F35/ODOO.PIVOT(1,\"price_subtotal\"),\"\"),\"\")"}, "G36": {"style": 17, "format": 2, "content": "=iferror(if(F36,F36/ODOO.PIVOT(1,\"price_subtotal\"),\"\"),\"\")"}, "G37": {"style": 6, "format": 2, "content": "=iferror(if(F37,F37/ODOO.PIVOT(1,\"price_subtotal\"),\"\"),\"\")"}, "G38": {"style": 17, "format": 2, "content": "=iferror(if(F38,F38/ODOO.PIVOT(1,\"price_subtotal\"),\"\"),\"\")"}, "G39": {"style": 6, "format": 2, "content": "=iferror(if(F39,F39/ODOO.PIVOT(1,\"price_subtotal\"),\"\"),\"\")"}, "G40": {"style": 17, "format": 2, "content": "=iferror(if(F40,F40/ODOO.PIVOT(1,\"price_subtotal\"),\"\"),\"\")"}, "G41": {"style": 6, "format": 2, "content": "=iferror(if(F41,F41/ODOO.PIVOT(1,\"price_subtotal\"),\"\"),\"\")"}, "G42": {"style": 17, "format": 2, "content": "=iferror(if(F42,F42/ODOO.PIVOT(1,\"price_subtotal\"),\"\"),\"\")"}, "G43": {"format": 3}, "G44": {"content": "=iferror(if(F44,F44/ODOO.PIVOT(1,\"price_subtotal\"),\"\"),\"\")"}, "G45": {"style": 12, "border": 1}, "G46": {"style": 16, "content": "=_t(\"Ratio\")", "border": 2}, "G47": {"style": 17, "format": 2, "content": "=iferror(if(F47,F47/ODOO.PIVOT(4,\"price_subtotal\"),\"\"),\"\")"}, "G48": {"style": 6, "format": 2, "content": "=iferror(if(F48,F48/ODOO.PIVOT(4,\"price_subtotal\"),\"\"),\"\")"}, "G49": {"style": 17, "format": 2, "content": "=iferror(if(F49,F49/ODOO.PIVOT(4,\"price_subtotal\"),\"\"),\"\")"}, "G50": {"style": 6, "format": 2, "content": "=iferror(if(F50,F50/ODOO.PIVOT(4,\"price_subtotal\"),\"\"),\"\")"}, "G51": {"style": 17, "format": 2, "content": "=iferror(if(F51,F51/ODOO.PIVOT(4,\"price_subtotal\"),\"\"),\"\")"}, "G52": {"style": 6, "format": 2, "content": "=iferror(if(F52,F52/ODOO.PIVOT(4,\"price_subtotal\"),\"\"),\"\")"}, "G53": {"style": 17, "format": 2, "content": "=iferror(if(F53,F53/ODOO.PIVOT(4,\"price_subtotal\"),\"\"),\"\")"}, "G54": {"style": 6, "format": 2, "content": "=iferror(if(F54,F54/ODOO.PIVOT(4,\"price_subtotal\"),\"\"),\"\")"}, "G55": {"style": 17, "format": 2, "content": "=iferror(if(F55,F55/ODOO.PIVOT(4,\"price_subtotal\"),\"\"),\"\")"}, "G56": {"style": 6, "format": 2, "content": "=iferror(if(F56,F56/ODOO.PIVOT(4,\"price_subtotal\"),\"\"),\"\")"}, "A8": {"border": 2}, "B7": {"border": 1}, "B8": {"border": 2}, "C7": {"border": 1}, "C8": {"border": 2}, "D7": {"border": 1}, "D8": {"border": 2}, "D19": {"border": 1}, "E7": {"border": 1}, "E8": {"border": 2}, "F7": {"border": 1}, "F8": {"border": 2}, "F19": {"border": 1}, "G7": {"border": 1}, "G8": {"border": 2}, "G19": {"border": 1}}, "conditionalFormats": [], "figures": [{"id": "5ea5dd7f-9f83-4482-a2bb-2ec72ab35912", "x": 0, "y": 178, "width": 1000, "height": 221, "tag": "chart", "data": {"title": "", "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["invoice_date:month"], "measure": "price_subtotal", "order": null, "resModel": "account.invoice.report"}, "searchParams": {"comparison": null, "context": {"group_by": ["invoice_date"], "group_by_no_leaf": 1}, "domain": ["&", ["state", "not in", ["draft", "cancel"]], "|", ["move_type", "=", "out_invoice"], ["move_type", "=", "out_refund"]], "groupBy": ["invoice_date"], "orderBy": []}, "type": "odoo_line", "verticalAxisPosition": "left"}}, {"id": "1aeea7b2-900b-4067-b8ad-3e4772c54028", "x": 0, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": "Invoiced", "type": "scorecard", "baseline": "Data!C11", "baselineDescr": "unpaid", "keyValue": "Data!C1"}}, {"id": "bdfb27d0-5902-4a2a-9b7e-514a6625578c", "x": 210, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": "Average Invoice", "type": "scorecard", "baseline": "Data!C3", "baselineDescr": "Invoices", "keyValue": "Data!C2"}}, {"id": "b1673523-d139-47fb-b5ea-9e4f969aacb6", "x": 420, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": "DSO", "type": "scorecard", "baselineDescr": "in current year", "keyValue": "Data!C10"}}], "areGridLinesVisible": true, "isVisible": true}, {"id": "685cb5c3-4acb-45eb-8000-99e1af15b3ed", "name": "Data", "colNumber": 26, "rowNumber": 107, "rows": {"3": {"size": 23}}, "cols": {"0": {"size": 141.8818359375}, "1": {"size": 127.6884765625}}, "merges": [], "cells": {"A1": {"content": "=_t(\"KPI - Income\")"}, "A2": {"content": "=_t(\"KPI - Average Invoice\")"}, "A3": {"content": "=_t(\"KPI - Invoice Count\")"}, "A4": {"content": "=_t(\"Current year\")"}, "A5": {"content": "=_t(\"Receivable\")"}, "A6": {"content": "=_t(\"Income\")"}, "A7": {"content": "=_t(\"COGS\")"}, "A8": {"content": "=_t(\"Revenue\")"}, "A9": {"content": "=_t(\"# days\")"}, "A10": {"content": "=_t(\"KPI - DSO\")"}, "A11": {"content": "=_t(\"KPI - Unpaid Invoices\")"}, "B1": {"content": "=ODOO.PIVOT(5,\"price_subtotal\")"}, "B2": {"content": "=IFERROR(ODOO.PIVOT(6,\"price_subtotal\")/B3)"}, "B3": {"format": 1, "content": "=ODOO.PIVOT(6,\"move_id\")"}, "B4": {"content": "=YEAR(TODAY())"}, "B5": {"content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP(\"asset_receivable\"),$B$4)"}, "B6": {"content": "=-ODOO.BALANCE(ODOO.ACCOUNT.GROUP(\"income\"),$B$4)"}, "B7": {"content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP(\"expense_direct_cost\"),$B$4)"}, "B8": {"content": "=B6-B7"}, "B9": {"content": "365"}, "B10": {"format": 5, "content": "=ROUND(IFERROR(B5/B8*B9))"}, "B11": {"content": "=ODOO.PIVOT(7,\"price_subtotal\")"}, "C1": {"style": 15, "content": "=FORMAT.LARGE.NUMBER(B1)"}, "C2": {"style": 15, "content": "=FORMAT.LARGE.NUMBER(B2)"}, "C3": {"style": 15, "content": "=FORMAT.LARGE.NUMBER(B3)"}, "C10": {"style": 15, "content": "=CONCATENATE(FORMAT.LARGE.NUMBER(B10),_t(\" days\"))"}, "C11": {"style": 15, "content": "=FORMAT.LARGE.NUMBER(B11)"}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true, "isVisible": true}], "entities": {}, "styles": {"1": {"textColor": "#01666b", "bold": true, "fontSize": 16}, "2": {"textColor": "#000000", "bold": true, "fillColor": "#ffffff", "fontSize": 10}, "3": {"fillColor": "#f8f9fa", "fontSize": 10, "textColor": "#01666b"}, "4": {"fontSize": 10, "textColor": "#01666b"}, "5": {"textColor": "#01666b"}, "6": {"fontSize": 10}, "7": {"textColor": "#000000", "bold": true, "fontSize": 10}, "8": {"fillColor": "#f8f9fa", "fontSize": 10, "textColor": "#741b47"}, "9": {"fontSize": 10, "textColor": "#741b47"}, "10": {"textColor": "#741b47"}, "11": {"textColor": "#000000"}, "12": {"bold": true, "fontSize": 16}, "13": {"bold": true, "fillColor": "#ffffff", "fontSize": 10}, "14": {"textColor": "#000000", "fillColor": "#f8f9fa", "fontSize": 10}, "15": {"fillColor": "#f8f9fa"}, "16": {"bold": true, "align": "right", "fontSize": 10}, "17": {"fillColor": "#f8f9fa", "fontSize": 10}, "18": {"bold": true, "align": "right", "fillColor": "#ffffff", "fontSize": 10}}, "formats": {"1": "#,##0", "2": "0%", "3": "[$$]#,##0", "4": "[$$]#,##0.00", "5": "#,##0.00"}, "borders": {"1": {"bottom": ["thin", "#000"]}, "2": {"top": ["thin", "#000"]}}, "revisionId": "START_REVISION", "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ","}}, "chartOdooMenusReferences": {"5ea5dd7f-9f83-4482-a2bb-2ec72ab35912": "account.menu_finance", "1aeea7b2-900b-4067-b8ad-3e4772c54028": "account.menu_action_move_out_invoice_type", "bdfb27d0-5902-4a2a-9b7e-514a6625578c": "account.menu_action_account_invoice_report_all", "b1673523-d139-47fb-b5ea-9e4f969aacb6": "account.menu_action_account_invoice_report_all"}, "odooVersion": 4, "lists": {"1": {"columns": ["name", "invoice_partner_display_name", "invoice_date", "invoice_date_due", "activity_ids", "amount_untaxed_signed", "amount_total_signed", "amount_total_in_currency_signed", "payment_state", "state"], "domain": ["&", ["state", "not in", ["draft", "cancel"]], ["move_type", "=", "out_invoice"]], "model": "account.move", "context": {"default_move_type": "out_invoice"}, "orderBy": [{"name": "amount_total_signed", "asc": false}], "id": "1", "name": "Invoices by Total Signed"}}, "listNextId": 2, "pivots": {"1": {"colGroupBys": [], "context": {"group_by": ["invoice_date"], "group_by_no_leaf": 1}, "domain": ["&", ["state", "not in", ["draft", "cancel"]], "&", ["product_categ_id", "!=", false], ["price_subtotal", ">=", 0]], "id": "1", "measures": [{"field": "price_subtotal"}], "model": "account.invoice.report", "rowGroupBys": ["product_categ_id"], "name": "Top Categories", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "2": {"colGroupBys": [], "context": {"group_by": ["invoice_date"], "group_by_no_leaf": 1}, "domain": ["&", ["state", "not in", ["draft", "cancel"]], "&", ["country_id", "!=", false], ["price_subtotal", ">=", 0]], "id": "2", "measures": [{"field": "price_subtotal"}], "model": "account.invoice.report", "rowGroupBys": ["country_id"], "name": "Top Countries", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "3": {"colGroupBys": [], "context": {"group_by": ["invoice_date"], "group_by_no_leaf": 1}, "domain": ["&", ["state", "not in", ["draft", "cancel"]], "&", ["product_id", "!=", false], ["price_subtotal", ">=", 0]], "id": "3", "measures": [{"field": "price_subtotal"}], "model": "account.invoice.report", "rowGroupBys": ["product_id"], "name": "Top Products", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "4": {"colGroupBys": [], "context": {"group_by": ["invoice_date"], "group_by_no_leaf": 1}, "domain": ["&", ["state", "not in", ["draft", "cancel"]], "&", ["invoice_user_id", "!=", false], ["price_subtotal", ">=", 0]], "id": "4", "measures": [{"field": "price_subtotal"}], "model": "account.invoice.report", "rowGroupBys": ["invoice_user_id"], "name": "Top Salespeople", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "5": {"colGroupBys": [], "context": {"group_by": ["invoice_date"], "group_by_no_leaf": 1}, "domain": ["&", ["state", "not in", ["draft", "cancel"]], "|", ["move_type", "=", "out_invoice"], ["move_type", "=", "out_refund"]], "id": "5", "measures": [{"field": "price_subtotal"}], "model": "account.invoice.report", "rowGroupBys": [], "name": "KPI - Income", "sortedColumn": null}, "6": {"colGroupBys": [], "context": {"group_by": ["invoice_date"], "group_by_no_leaf": 1}, "domain": ["&", ["state", "not in", ["draft", "cancel"]], "|", ["move_type", "=", "out_invoice"], ["move_type", "=", "out_refund"]], "id": "6", "measures": [{"field": "move_id"}, {"field": "price_subtotal"}], "model": "account.invoice.report", "rowGroupBys": [], "name": "KPI - Average Invoice", "sortedColumn": null}, "7": {"colGroupBys": [], "context": {"group_by": ["invoice_date"], "group_by_no_leaf": 1}, "domain": ["&", ["state", "not in", ["draft", "cancel"]], "&", "|", ["move_type", "=", "out_invoice"], ["move_type", "=", "out_refund"], ["payment_state", "=", "not_paid"]], "id": "7", "measures": [{"field": "price_subtotal"}], "model": "account.invoice.report", "rowGroupBys": [], "name": "KPI - Unpaid Invoices", "sortedColumn": null}}, "pivotNextId": 8, "globalFilters": [{"id": "757a1b4b-e339-4879-beb6-9851050387cf", "type": "date", "label": "Period", "defaultValue": "last_three_months", "rangeType": "relative", "defaultsToCurrentPeriod": true, "pivotFields": {"1": {"field": "invoice_date", "type": "date", "offset": 0}, "2": {"field": "invoice_date", "type": "date", "offset": 0}, "3": {"field": "invoice_date", "type": "date", "offset": 0}, "4": {"field": "invoice_date", "type": "date", "offset": 0}, "5": {"field": "invoice_date", "type": "date", "offset": 0}, "6": {"field": "invoice_date", "type": "date", "offset": 0}, "7": {"field": "invoice_date", "type": "date", "offset": 0}}, "listFields": {"1": {"field": "date", "type": "date", "offset": 0}}, "graphFields": {"5ea5dd7f-9f83-4482-a2bb-2ec72ab35912": {"field": "invoice_date", "type": "date", "offset": 0}}}, {"id": "8051befe-619f-4fe7-b788-d34584297bad", "type": "relation", "label": "Country", "modelName": "res.country", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "country_id", "type": "many2one"}, "2": {"field": "country_id", "type": "many2one"}, "3": {"field": "country_id", "type": "many2one"}, "4": {"field": "country_id", "type": "many2one"}, "5": {"field": "country_id", "type": "many2one"}, "6": {"field": "country_id", "type": "many2one"}, "7": {"field": "country_id", "type": "many2one"}}, "listFields": {"1": {"field": "partner_id.country_id", "type": "many2one"}}, "graphFields": {"5ea5dd7f-9f83-4482-a2bb-2ec72ab35912": {"field": "country_id", "type": "many2one"}}}, {"id": "17277380-12d8-4a83-b133-3532e5618c43", "type": "relation", "label": "Product Category", "modelName": "product.category", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "product_categ_id", "type": "many2one"}, "2": {"field": "product_categ_id", "type": "many2one"}, "3": {"field": "product_categ_id", "type": "many2one"}, "4": {"field": "product_categ_id", "type": "many2one"}, "5": {"field": "product_categ_id", "type": "many2one"}, "6": {"field": "product_categ_id", "type": "many2one"}, "7": {"field": "product_categ_id", "type": "many2one"}}, "listFields": {"1": {"field": "invoice_line_ids.product_id.categ_id", "type": "many2one"}}, "graphFields": {"5ea5dd7f-9f83-4482-a2bb-2ec72ab35912": {"field": "product_categ_id", "type": "many2one"}}}, {"id": "accd0cbe-12c9-4cab-93a1-afa5080dd635", "type": "relation", "label": "Product", "modelName": "product.product", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "product_id", "type": "many2one"}, "2": {"field": "product_id", "type": "many2one"}, "3": {"field": "product_id", "type": "many2one"}, "4": {"field": "product_id", "type": "many2one"}, "5": {"field": "product_id", "type": "many2one"}, "6": {"field": "product_id", "type": "many2one"}, "7": {"field": "product_id", "type": "many2one"}}, "listFields": {"1": {"field": "invoice_line_ids.product_id", "type": "many2one"}}, "graphFields": {"5ea5dd7f-9f83-4482-a2bb-2ec72ab35912": {"field": "product_id", "type": "many2one"}}}, {"id": "02acc7f7-b282-4ce9-bf38-6abfb72be6aa", "type": "relation", "label": "Salesperson", "modelName": "res.users", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "invoice_user_id", "type": "many2one"}, "2": {"field": "invoice_user_id", "type": "many2one"}, "3": {"field": "invoice_user_id", "type": "many2one"}, "4": {"field": "invoice_user_id", "type": "many2one"}, "5": {"field": "invoice_user_id", "type": "many2one"}, "6": {"field": "invoice_user_id", "type": "many2one"}, "7": {"field": "invoice_user_id", "type": "many2one"}}, "listFields": {"1": {"field": "invoice_user_id", "type": "many2one"}}, "graphFields": {"5ea5dd7f-9f83-4482-a2bb-2ec72ab35912": {"field": "invoice_user_id", "type": "many2one"}}}]}