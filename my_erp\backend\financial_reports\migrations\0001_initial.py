# Generated by Django 4.2.21 on 2025-07-15 20:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0003_delete_ircron'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountAgeReportConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='Default Age Configuration', max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'account_age_report_configuration',
            },
        ),
        migrations.CreateModel(
            name='VatReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Report Name', max_length=128)),
                ('date_from', models.DateField(help_text='Date From')),
                ('date_to', models.DateField(help_text='Date To')),
                ('only_posted_moves', models.BooleanField(default=True, help_text='Only Posted Entries')),
                ('show_move_line_details', models.BooleanField(default=False, help_text='Show Move Line Details')),
                ('hide_account_at_0', models.BooleanField(default=True, help_text='Hide Accounts at 0')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('based_on', models.CharField(choices=[('taxtags', 'Tax Tags'), ('taxgroups', 'Tax Groups')], default='taxtags', max_length=16)),
                ('tax_detail', models.BooleanField(default=True, help_text='Show Tax Detail')),
                ('account_ids', models.ManyToManyField(blank=True, help_text='Accounts', to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_ids', models.ManyToManyField(blank=True, help_text='Journals', to='accounting.accountjournal')),
                ('partner_ids', models.ManyToManyField(blank=True, help_text='Partners', to='accounting.respartner')),
            ],
            options={
                'db_table': 'vat_report',
            },
        ),
        migrations.CreateModel(
            name='TrialBalanceReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Report Name', max_length=128)),
                ('date_from', models.DateField(help_text='Date From')),
                ('date_to', models.DateField(help_text='Date To')),
                ('only_posted_moves', models.BooleanField(default=True, help_text='Only Posted Entries')),
                ('show_move_line_details', models.BooleanField(default=False, help_text='Show Move Line Details')),
                ('hide_account_at_0', models.BooleanField(default=True, help_text='Hide Accounts at 0')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('show_partner_details', models.BooleanField(default=False, help_text='Show Partner Details')),
                ('limit_hierarchy_level', models.BooleanField(default=False, help_text='Limit Hierarchy Level')),
                ('hierarchy_level', models.IntegerField(default=1, help_text='Hierarchy Level')),
                ('show_hierarchy_level', models.IntegerField(default=1, help_text='Show Hierarchy Level')),
                ('account_ids', models.ManyToManyField(blank=True, help_text='Accounts', to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_ids', models.ManyToManyField(blank=True, help_text='Journals', to='accounting.accountjournal')),
                ('partner_ids', models.ManyToManyField(blank=True, help_text='Partners', to='accounting.respartner')),
            ],
            options={
                'db_table': 'trial_balance_report',
            },
        ),
        migrations.CreateModel(
            name='OpenItemsReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Report Name', max_length=128)),
                ('date_from', models.DateField(help_text='Date From')),
                ('date_to', models.DateField(help_text='Date To')),
                ('only_posted_moves', models.BooleanField(default=True, help_text='Only Posted Entries')),
                ('show_move_line_details', models.BooleanField(default=False, help_text='Show Move Line Details')),
                ('hide_account_at_0', models.BooleanField(default=True, help_text='Hide Accounts at 0')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_type', models.CharField(choices=[('receivable', 'Receivable'), ('payable', 'Payable')], default='receivable', max_length=16)),
                ('target_move', models.CharField(choices=[('posted', 'Posted'), ('all', 'All')], default='posted', max_length=16)),
                ('account_ids', models.ManyToManyField(blank=True, help_text='Accounts', to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_ids', models.ManyToManyField(blank=True, help_text='Journals', to='accounting.accountjournal')),
                ('partner_ids', models.ManyToManyField(blank=True, help_text='Partners', to='accounting.respartner')),
            ],
            options={
                'db_table': 'open_items_report',
            },
        ),
        migrations.CreateModel(
            name='JournalLedgerReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Report Name', max_length=128)),
                ('date_from', models.DateField(help_text='Date From')),
                ('date_to', models.DateField(help_text='Date To')),
                ('only_posted_moves', models.BooleanField(default=True, help_text='Only Posted Entries')),
                ('show_move_line_details', models.BooleanField(default=False, help_text='Show Move Line Details')),
                ('hide_account_at_0', models.BooleanField(default=True, help_text='Hide Accounts at 0')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('sort_option', models.CharField(choices=[('move_name', 'Entry Number'), ('date', 'Date')], default='move_name', max_length=16)),
                ('group_option', models.CharField(choices=[('journal', 'Journal'), ('none', 'None')], default='journal', max_length=16)),
                ('account_ids', models.ManyToManyField(blank=True, help_text='Accounts', to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_ids', models.ManyToManyField(blank=True, help_text='Journals', to='accounting.accountjournal')),
                ('partner_ids', models.ManyToManyField(blank=True, help_text='Partners', to='accounting.respartner')),
            ],
            options={
                'db_table': 'journal_ledger_report',
            },
        ),
        migrations.CreateModel(
            name='GeneralLedgerReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Report Name', max_length=128)),
                ('date_from', models.DateField(help_text='Date From')),
                ('date_to', models.DateField(help_text='Date To')),
                ('only_posted_moves', models.BooleanField(default=True, help_text='Only Posted Entries')),
                ('show_move_line_details', models.BooleanField(default=False, help_text='Show Move Line Details')),
                ('hide_account_at_0', models.BooleanField(default=True, help_text='Hide Accounts at 0')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('centralize', models.BooleanField(default=True, help_text='Centralize')),
                ('show_analytic_tags', models.BooleanField(default=False, help_text='Show Analytic Tags')),
                ('account_ids', models.ManyToManyField(blank=True, help_text='Accounts', to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_ids', models.ManyToManyField(blank=True, help_text='Journals', to='accounting.accountjournal')),
                ('partner_ids', models.ManyToManyField(blank=True, help_text='Partners', to='accounting.respartner')),
            ],
            options={
                'db_table': 'general_ledger_report',
            },
        ),
        migrations.CreateModel(
            name='DateRange',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('type_id', models.CharField(choices=[('month', 'Month'), ('quarter', 'Quarter'), ('year', 'Year'), ('custom', 'Custom')], default='month', max_length=16)),
                ('date_start', models.DateField(help_text='Start Date')),
                ('date_end', models.DateField(help_text='End Date')),
                ('active', models.BooleanField(default=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'date_range',
            },
        ),
        migrations.CreateModel(
            name='AgedPartnerBalanceReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Report Name', max_length=128)),
                ('date_from', models.DateField(help_text='Date From')),
                ('date_to', models.DateField(help_text='Date To')),
                ('only_posted_moves', models.BooleanField(default=True, help_text='Only Posted Entries')),
                ('hide_account_at_0', models.BooleanField(default=True, help_text='Hide Accounts at 0')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_type', models.CharField(choices=[('receivable', 'Receivable'), ('payable', 'Payable')], default='receivable', max_length=16)),
                ('show_move_line_details', models.BooleanField(default=True, help_text='Show Move Line Details')),
                ('account_ids', models.ManyToManyField(blank=True, help_text='Accounts', to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_ids', models.ManyToManyField(blank=True, help_text='Journals', to='accounting.accountjournal')),
                ('partner_ids', models.ManyToManyField(blank=True, help_text='Partners', to='accounting.respartner')),
            ],
            options={
                'db_table': 'aged_partner_balance_report',
            },
        ),
        migrations.CreateModel(
            name='AccountAgeReportConfigurationLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Interval Name', max_length=64)),
                ('days_from', models.IntegerField(help_text='Days From')),
                ('days_to', models.IntegerField(help_text='Days To')),
                ('sequence', models.IntegerField(default=1)),
                ('configuration_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='financial_reports.accountagereportconfiguration')),
            ],
            options={
                'db_table': 'account_age_report_configuration_line',
                'ordering': ['sequence'],
            },
        ),
    ]
