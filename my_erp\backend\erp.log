Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 2541
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2592
Not Found: /
"GET / HTTP/1.1" 404 2541
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 2541
"GET /admin HTTP/1.1" 301 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4177
"GET /static/admin/css/base.css HTTP/1.1" 200 21310
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
"GET /static/admin/css/login.css HTTP/1.1" 200 958
"GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 7705
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
"GET /admin/accounting/accountjournal/ HTTP/1.1" 200 9774
"GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
"GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
"GET /static/admin/js/core.js HTTP/1.1" 200 5682
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
"GET /static/admin/js/actions.js HTTP/1.1" 200 7872
"GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
"GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
"GET /static/admin/img/search.svg HTTP/1.1" 200 458
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" **********
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" **********
"GET /static/admin/js/filters.js HTTP/1.1" 200 978
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 9950
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /api/accounting/api/ HTTP/1.1" 200 6233
"GET /static/rest_framework/css/prettify.css HTTP/1.1" 200 817
"GET /static/rest_framework/css/bootstrap-tweaks.css HTTP/1.1" 200 3385
"GET /static/rest_framework/css/bootstrap.min.css HTTP/1.1" **********
"GET /static/rest_framework/css/default.css HTTP/1.1" 200 1152
"GET /static/rest_framework/js/ajax-form.js HTTP/1.1" 200 3597
"GET /static/rest_framework/js/csrf.js HTTP/1.1" 200 1719
"GET /static/rest_framework/js/jquery-3.5.1.min.js HTTP/1.1" 200 89476
"GET /static/rest_framework/js/bootstrap.min.js HTTP/1.1" 200 39680
"GET /static/rest_framework/js/default.js HTTP/1.1" 200 1268
"GET /static/rest_framework/js/prettify-min.js HTTP/1.1" 200 13632
"GET /static/rest_framework/img/grid.png HTTP/1.1" 200 1458
"GET /admin/accounting/respartner/ HTTP/1.1" 200 9817
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/auth/group/ HTTP/1.1" 200 8808
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/auth/user/ HTTP/1.1" 200 12013
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
"GET /admin/accounting/respartner/ HTTP/1.1" 200 9817
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountaccount/ HTTP/1.1" 200 11326
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountjournal/ HTTP/1.1" 200 9774
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 9950
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/respartner/ HTTP/1.1" 200 9817
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountjournal/ HTTP/1.1" 200 9774
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountjournal/add/ HTTP/1.1" 200 16077
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/css/forms.css HTTP/1.1" 200 9090
"GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11921
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /admin/accounting/accountaccount/ HTTP/1.1" 200 11326
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountaccount/add/ HTTP/1.1" 200 16760
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /api/accounting/api/companies/ HTTP/1.1" 200 25312
"GET /static/rest_framework/fonts/glyphicons-halflings-regular.woff2 HTTP/1.1" 200 18028
"GET /api/accounting/api/accounts/ HTTP/1.1" 200 26574
"GET /api/accounting/api/partners/ HTTP/1.1" 200 29398
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 11763
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
Internal Server Error: /admin/accounting/accountaccount/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: AccountAccount has no field named 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "D:\odoo_erp\my_erp\backend\accounting\admin.py", line 34, in balance_display
    balance = obj.balance
  File "D:\odoo_erp\my_erp\backend\accounting\models.py", line 197, in balance
    lines = self.move_line_ids.filter(move_id__state='posted')
AttributeError: 'AccountAccount' object has no attribute 'move_line_ids'
"GET /admin/accounting/accountaccount/ HTTP/1.1" **********
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 11763
"GET /admin/accounting/accountjournal/ HTTP/1.1" 200 13941
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
Internal Server Error: /admin/accounting/accountaccount/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: AccountAccount has no field named 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "D:\odoo_erp\my_erp\backend\accounting\admin.py", line 34, in balance_display
    balance = obj.balance
  File "D:\odoo_erp\my_erp\backend\accounting\models.py", line 197, in balance
    lines = self.move_line_ids.filter(move_id__state='posted')
AttributeError: 'AccountAccount' object has no attribute 'move_line_ids'
"GET /admin/accounting/accountaccount/ HTTP/1.1" 500 405214
Internal Server Error: /admin/accounting/accountaccount/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: AccountAccount has no field named 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "D:\odoo_erp\my_erp\backend\accounting\admin.py", line 34, in balance_display
    balance = obj.balance
  File "D:\odoo_erp\my_erp\backend\accounting\models.py", line 197, in balance
    lines = self.move_line_ids.filter(move_id__state='posted')
AttributeError: 'AccountAccount' object has no attribute 'move_line_ids'
"GET /admin/accounting/accountaccount/ HTTP/1.1" 500 405351
"GET /admin/accounting/accountjournal/ HTTP/1.1" 200 13941
D:\odoo_erp\my_erp\backend\erp_backend\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/accounting/respartner/ HTTP/1.1" 200 14799
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 9816
"GET /api/accounting/api/companies/ HTTP/1.1" 200 655
"GET /api/accounting/api/accounts/ HTTP/1.1" 200 3581
"GET /api/accounting/api/partners/ HTTP/1.1" 200 3154
"GET /api/accounting/api/journals/ HTTP/1.1" 200 1381
"GET /api/accounting/api/ HTTP/1.1" 200 6233
"GET /api/accounting/api/accounts/ HTTP/1.1" 200 26574
"GET /api/accounting/api/journals/ HTTP/1.1" 200 18974
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 11108
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
"GET /static/admin/css/base.css HTTP/1.1" 200 21310
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 11108
"GET /admin/ HTTP/1.1" 200 11108
Internal Server Error: /admin/accounting/accountaccount/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: AccountAccount has no field named 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "D:\odoo_erp\my_erp\backend\accounting\admin.py", line 38, in balance_display
    return format_html(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\utils\html.py", line 112, in format_html
    return mark_safe(format_string.format(*args_safe, **kwargs_safe))
ValueError: Unknown format code 'f' for object of type 'SafeString'
"GET /admin/accounting/accountaccount/ HTTP/1.1" 500 450586
"GET /admin/ HTTP/1.1" 200 11108
"GET /admin/accounting/accountanalyticaccount/ HTTP/1.1" 200 14562
"GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
"GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
"GET /static/admin/js/core.js HTTP/1.1" 200 5682
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
"GET /static/admin/js/actions.js HTTP/1.1" 200 7872
"GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" **********
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
"GET /static/admin/js/filters.js HTTP/1.1" 200 978
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" **********
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
Internal Server Error: /admin/accounting/accountaccount/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: AccountAccount has no field named 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "D:\odoo_erp\my_erp\backend\accounting\admin.py", line 38, in balance_display
    return format_html(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\utils\html.py", line 112, in format_html
    return mark_safe(format_string.format(*args_safe, **kwargs_safe))
ValueError: Unknown format code 'f' for object of type 'SafeString'
"GET /admin/accounting/accountaccount/ HTTP/1.1" 500 450620
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/accounting/accountaccount/ HTTP/1.1" 200 23385
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 20184
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 20184
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 29334
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 30806
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4195
"GET /static/admin/css/login.css HTTP/1.1" 200 958
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4198
"POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 31656
Forbidden (CSRF token from POST incorrect.): /admin/login/
"POST /admin/login/?next=/admin/ HTTP/1.1" 403 2518
"GET /admin/accounting/stockwarehouse/ HTTP/1.1" 200 26299
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/stockwarehouse/add/ HTTP/1.1" 200 42422
"GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/css/forms.css HTTP/1.1" 200 9090
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11921
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /admin/ HTTP/1.1" 200 31656
"GET /admin/sales/saleorderline/ HTTP/1.1" 200 27057
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/ HTTP/1.1" 200 31656
"GET /admin/sales/saleorder/ HTTP/1.1" 200 28199
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/ HTTP/1.1" 200 31656
"GET /admin/sales/salesteam/ HTTP/1.1" 200 29595
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/ HTTP/1.1" 200 31656
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 33771
Watching for file changes with StatReloader
"GET /admin/login HTTP/1.1" 301 0
"GET /admin/login/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 37865
"GET /admin/accounting/rescountry/ HTTP/1.1" 200 30337
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\sales\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 39848
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 45937
"GET /admin/auth/user/ HTTP/1.1" 200 39389
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 50443
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 54847
"GET /admin/financial_reports/accountagereportconfiguration/ HTTP/1.1" 200 45080
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/financial_reports/accountagereportconfiguration/1/change/ HTTP/1.1" 200 59616
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /static/admin/css/forms.css HTTP/1.1" 200 9090
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11921
"GET /static/admin/js/inlines.js HTTP/1.1" 200 15526
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /static/admin/img/icon-unknown.svg HTTP/1.1" 200 655
"POST /admin/financial_reports/accountagereportconfiguration/1/change/ HTTP/1.1" 302 0
"GET /admin/financial_reports/accountagereportconfiguration/ HTTP/1.1" 200 45360
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\expenses\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 64837
"GET /admin/ HTTP/1.1" 200 64837
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
"OPTIONS /api/accounting/api/accounts/ HTTP/1.1" 200 0
Forbidden: /api/accounting/api/accounts/
"POST /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\erp_backend\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\migrations\__init__.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Forbidden: /api/setup/countries/
"GET /api/setup/countries/ HTTP/1.1" 403 58
D:\odoo_erp\my_erp\backend\setup\admin.py changed, reloading.
D:\odoo_erp\my_erp\backend\setup\admin.py changed, reloading.
D:\odoo_erp\my_erp\backend\setup\admin.py changed, reloading.
D:\odoo_erp\my_erp\backend\setup\admin.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 66787
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 53920
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/setup/companysetup/ HTTP/1.1" 200 54402
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/timezones/ HTTP/1.1" 200 1210
"GET /api/setup/companies/ HTTP/1.1" 200 52
Watching for file changes with StatReloader
Forbidden: /api/setup/countries/
"GET /api/setup/countries/ HTTP/1.1" 403 58
- Broken pipe from ('127.0.0.1', 56596)
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"OPTIONS /api/api/accounts/ HTTP/1.1" 200 0
"OPTIONS /api/api/accounts/ HTTP/1.1" 200 0
Not Found: /api/api/accounts/
"GET /api/api/accounts/ HTTP/1.1" 404 2885
Not Found: /api/api/accounts/
"GET /api/api/accounts/ HTTP/1.1" 404 2885
Not Found: /api/api/accounts/
"GET /api/api/accounts/ HTTP/1.1" 404 2885
Not Found: /api/api/accounts/
"GET /api/api/accounts/ HTTP/1.1" 404 2885
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 54469
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/setup/companysetup/ HTTP/1.1" 200 54951
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
D:\odoo_erp\my_erp\backend\erp_backend\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/setup/companysetup/ HTTP/1.1" 200 54951
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\authentication\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\authentication\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\authentication\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\authentication\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\authentication\migrations\0001_initial.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/setup/companysetup/ HTTP/1.1" 200 56875
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 58
"GET /api/auth/check/ HTTP/1.1" 200 23
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 78
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 78
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 78
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 78
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 78
D:\odoo_erp\my_erp\backend\authentication\views.py changed, reloading.
Watching for file changes with StatReloader
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 78
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 78
D:\odoo_erp\my_erp\backend\authentication\serializers.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 394
"POST /api/auth/login/ HTTP/1.1" 200 394
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 52
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 78
D:\odoo_erp\my_erp\backend\authentication\views.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 394
D:\odoo_erp\my_erp\backend\authentication\views.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/logout/ HTTP/1.1" 200 52
"POST /api/auth/login/ HTTP/1.1" 200 394
D:\odoo_erp\my_erp\backend\authentication\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\authentication\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\authentication\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\authentication\views.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\authentication\views.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 771
"OPTIONS /api/setup/companies/ HTTP/1.1" 200 0
"OPTIONS /api/setup/companies/ HTTP/1.1" 200 0
"GET /api/setup/companies/ HTTP/1.1" 200 52
"GET /api/setup/companies/ HTTP/1.1" 200 52
"GET /api/setup/companies/ HTTP/1.1" 200 52
"GET /api/setup/companies/ HTTP/1.1" 200 52
"POST /api/auth/login/ HTTP/1.1" 200 771
"POST /api/auth/login/ HTTP/1.1" 200 771
Forbidden: /api/setup/companies/
"GET /api/setup/companies/ HTTP/1.1" 403 58
"POST /api/auth/login/ HTTP/1.1" 200 771
Forbidden: /api/setup/companies/
"GET /api/setup/companies/ HTTP/1.1" 403 58
Forbidden: /api/setup/companies/
"GET /api/setup/companies/ HTTP/1.1" 403 58
"POST /api/auth/login/ HTTP/1.1" 200 771
"GET /api/setup/companies/ HTTP/1.1" 200 52
Forbidden: /api/setup/companies/
"GET /api/setup/companies/ HTTP/1.1" 403 58
"GET /api/setup/companies/ HTTP/1.1" 200 52
"GET /api/setup/companies/ HTTP/1.1" 200 52
"GET /api/setup/companies/ HTTP/1.1" 200 52
"GET /api/setup/companies/ HTTP/1.1" 200 52
"GET /api/setup/companies/ HTTP/1.1" 200 52
"GET /api/setup/companies/ HTTP/1.1" 200 52
"POST /api/auth/login/ HTTP/1.1" 200 771
"GET /api/setup/companies/ HTTP/1.1" 200 52
Forbidden: /api/setup/companies/
"GET /api/setup/companies/ HTTP/1.1" 403 58
Bad Request: /api/setup/companies/
"POST /api/setup/companies/ HTTP/1.1" 400 283
"POST /api/auth/login/ HTTP/1.1" 200 771
"POST /api/setup/companies/ HTTP/1.1" 201 820
"GET /api/setup/companies/ HTTP/1.1" 200 282
"GET /api/setup/companies/ HTTP/1.1" 200 282
"OPTIONS /api/setup/companies/1/ HTTP/1.1" 200 0
Bad Request: /api/setup/companies/1/
"PUT /api/setup/companies/1/ HTTP/1.1" 400 128
Bad Request: /api/setup/companies/
"POST /api/setup/companies/ HTTP/1.1" 400 161
"GET /api/setup/companies/ HTTP/1.1" 200 282
"GET /api/setup/companies/ HTTP/1.1" 200 282
Bad Request: /api/setup/companies/
"POST /api/setup/companies/ HTTP/1.1" 400 161
"GET /api/setup/companies/ HTTP/1.1" 200 282
"GET /api/setup/companies/ HTTP/1.1" 200 282
"GET /api/setup/companies/ HTTP/1.1" 200 282
"POST /api/setup/companies/ HTTP/1.1" 201 817
"GET /api/setup/companies/ HTTP/1.1" 200 510
"GET /admin/setup/companysetup/ HTTP/1.1" 200 60058
"GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
"OPTIONS /api/setup/income-taxes/ HTTP/1.1" 200 0
"OPTIONS /api/setup/income-taxes/ HTTP/1.1" 200 0
Not Found: /api/setup/income-taxes/
"GET /api/setup/income-taxes/ HTTP/1.1" 404 15456
Not Found: /api/setup/income-taxes/
"GET /api/setup/income-taxes/ HTTP/1.1" 404 15456
"GET /api/setup/companies/ HTTP/1.1" 200 510
"GET /api/setup/companies/ HTTP/1.1" 200 510
"POST /api/setup/companies/ HTTP/1.1" 201 826
"GET /api/setup/companies/ HTTP/1.1" 200 747
"OPTIONS /api/setup/companies/2/ HTTP/1.1" 200 0
"PUT /api/setup/companies/2/ HTTP/1.1" 200 817
"GET /api/setup/companies/ HTTP/1.1" 200 747
"GET /admin/accounting/rescountry/ HTTP/1.1" 200 53481
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/rescountry/ HTTP/1.1" 200 53481
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/rescurrency/ HTTP/1.1" 200 53898
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /api/setup/companies/ HTTP/1.1" 200 747
"GET /api/setup/companies/ HTTP/1.1" 200 747
"GET /api/setup/companies/ HTTP/1.1" 200 747
"GET /api/setup/companies/ HTTP/1.1" 200 747
"GET /api/setup/companies/ HTTP/1.1" 200 747
"GET /api/setup/companies/ HTTP/1.1" 200 747
"OPTIONS /api/setup/countries/ HTTP/1.1" 200 0
"OPTIONS /api/setup/currencies/ HTTP/1.1" 200 0
"OPTIONS /api/setup/countries/ HTTP/1.1" 200 0
"OPTIONS /api/setup/currencies/ HTTP/1.1" 200 0
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /admin/accounting/rescurrency/ HTTP/1.1" 200 53898
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/rescountry/ HTTP/1.1" 200 53481
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountfiscalpositionaccount/ HTTP/1.1" 200 53623
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 56393
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/rescompany/add/ HTTP/1.1" 200 67074
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/calendar.js HTTP/1.1" 200 8466
"GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
"GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
"GET /admin/accounting/accountaccount/ HTTP/1.1" 200 65618
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountaccount/add/ HTTP/1.1" 200 61389
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountaccount/add/ HTTP/1.1" 200 61389
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/rescurrency/ HTTP/1.1" 200 53898
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/rescurrency/ HTTP/1.1" 200 53898
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /api/setup/companies/ HTTP/1.1" 200 747
"GET /api/setup/companies/ HTTP/1.1" 200 747
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"POST /api/setup/companies/ HTTP/1.1" 201 817
"GET /api/setup/companies/ HTTP/1.1" 200 975
"GET /admin/accounting/rescurrency/ HTTP/1.1" 200 53898
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
"GET /static/admin/css/base.css HTTP/1.1" 200 21310
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
"GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
"GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" **********
"GET /static/admin/js/core.js HTTP/1.1" 200 5682
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
"GET /static/admin/js/actions.js HTTP/1.1" 200 7872
"GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
"GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
"GET /static/admin/img/search.svg HTTP/1.1" 200 458
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" **********
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/admin/js/filters.js HTTP/1.1" 200 978
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2933
"OPTIONS /api/setup/companies/ HTTP/1.1" 200 0
"OPTIONS /api/setup/companies/ HTTP/1.1" 200 0
"GET /api/setup/companies/ HTTP/1.1" 200 975
"OPTIONS /api/setup/countries/ HTTP/1.1" 200 0
"OPTIONS /api/setup/currencies/ HTTP/1.1" 200 0
"OPTIONS /api/setup/countries/ HTTP/1.1" 200 0
"OPTIONS /api/setup/currencies/ HTTP/1.1" 200 0
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /admin/accounting/rescurrency/ HTTP/1.1" 200 53898
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/setup/country/ HTTP/1.1" 200 60427
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
"GET /admin/setup/currency/ HTTP/1.1" 200 60317
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
D:\odoo_erp\my_erp\backend\hr\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 55765
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
Watching for file changes with StatReloader
"GET /admin/accounting/accounttax/ HTTP/1.1" 200 57432
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"POST /api/auth/login/ HTTP/1.1" 200 771
"GET /api/setup/companies/ HTTP/1.1" 200 975
Forbidden: /api/setup/companies/
"GET /api/setup/companies/ HTTP/1.1" 403 58
"GET /api/setup/companies/ HTTP/1.1" 200 975
"GET /api/setup/companies/ HTTP/1.1" 200 975
"GET /api/setup/companies/ HTTP/1.1" 200 975
"GET /api/setup/companies/ HTTP/1.1" 200 975
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"POST /api/setup/companies/ HTTP/1.1" 201 817
"GET /api/setup/companies/ HTTP/1.1" 200 1203
"GET /admin/accounting/accounttax/ HTTP/1.1" 200 57432
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/setup/companysetup/ HTTP/1.1" 200 61260
"GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"OPTIONS /api/setup/companies/3/ HTTP/1.1" 200 0
"PUT /api/setup/companies/3/ HTTP/1.1" 200 826
"GET /api/setup/companies/ HTTP/1.1" 200 1203
"GET /api/setup/companies/ HTTP/1.1" 200 1203
"GET /api/setup/companies/ HTTP/1.1" 200 1203
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"PUT /api/setup/companies/3/ HTTP/1.1" 200 826
"GET /api/setup/companies/ HTTP/1.1" 200 1203
"POST /api/auth/login/ HTTP/1.1" 200 771
"GET /api/setup/companies/ HTTP/1.1" 200 1203
Forbidden: /api/setup/companies/
"GET /api/setup/companies/ HTTP/1.1" 403 58
D:\odoo_erp\my_erp\backend\setup\serializers.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\views.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 771
"GET /api/setup/companies/ HTTP/1.1" 200 1288
Forbidden: /api/setup/companies/
"GET /api/setup/companies/ HTTP/1.1" 403 58
"GET /api/setup/companies/ HTTP/1.1" 200 1288
"GET /api/setup/companies/ HTTP/1.1" 200 1288
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"OPTIONS /api/setup/companies/5/ HTTP/1.1" 200 0
"PUT /api/setup/companies/5/ HTTP/1.1" 200 817
"GET /api/setup/companies/ HTTP/1.1" 200 1288
"GET /api/setup/companies/ HTTP/1.1" 200 1288
"GET /api/setup/companies/ HTTP/1.1" 200 1288
"POST /api/auth/login/ HTTP/1.1" 200 771
"PATCH /api/setup/companies/1/ HTTP/1.1" 200 821
"GET /api/setup/companies/ HTTP/1.1" 200 1289
"GET /api/setup/companies/ HTTP/1.1" 200 1289
"GET /api/setup/companies/ HTTP/1.1" 200 1289
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"PUT /api/setup/companies/5/ HTTP/1.1" 200 817
"GET /api/setup/companies/ HTTP/1.1" 200 1289
"GET /admin/setup/companysetup/ HTTP/1.1" 200 61260
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/setup/companysetup/ HTTP/1.1" 200 61260
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/setup/companysetup/ HTTP/1.1" 200 61260
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/setup/companysetup/ HTTP/1.1" 200 61260
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /api/setup/companies/ HTTP/1.1" 200 1289
"GET /api/setup/companies/ HTTP/1.1" 200 1289
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"PUT /api/setup/companies/5/ HTTP/1.1" 200 817
"GET /api/setup/companies/ HTTP/1.1" 200 1289
"GET /admin/setup/companysetup/ HTTP/1.1" 200 61260
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /api/setup/companies/ HTTP/1.1" 200 1289
"GET /api/setup/companies/ HTTP/1.1" 200 1289
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"POST /api/setup/companies/ HTTP/1.1" 201 805
"GET /api/setup/companies/ HTTP/1.1" 200 1522
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"GET /api/setup/currencies/ HTTP/1.1" 200 2391
"GET /api/setup/countries/ HTTP/1.1" 200 2385
"OPTIONS /api/setup/companies/6/ HTTP/1.1" 200 0
"PUT /api/setup/companies/6/ HTTP/1.1" 200 805
"GET /api/setup/companies/ HTTP/1.1" 200 1522
"GET /api/setup/companies/ HTTP/1.1" 200 1522
"GET /api/setup/companies/ HTTP/1.1" 200 1522
"GET /admin/setup/companysetup/ HTTP/1.1" 200 61866
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/setup/companysetup/add/ HTTP/1.1" 200 78927
"GET /static/admin/js/calendar.js HTTP/1.1" 200 8466
"GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
"GET /static/admin/css/forms.css HTTP/1.1" 200 9090
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11921
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
"GET /admin/setup/companysetup/ HTTP/1.1" 200 61866
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"OPTIONS /api/setup/income-taxes/ HTTP/1.1" 200 0
"OPTIONS /api/setup/income-taxes/ HTTP/1.1" 200 0
Not Found: /api/setup/income-taxes/
"GET /api/setup/income-taxes/ HTTP/1.1" 404 15456
Not Found: /api/setup/income-taxes/
"GET /api/setup/income-taxes/ HTTP/1.1" 404 15456
D:\odoo_erp\my_erp\backend\setup\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\serializers.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\serializers.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\views.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\views.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\views.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\admin.py changed, reloading.
Watching for file changes with StatReloader
Not Found: /api/setup/income-taxes/
"GET /api/setup/income-taxes/ HTTP/1.1" 404 17300
Not Found: /api/setup/income-taxes/
"GET /api/setup/income-taxes/ HTTP/1.1" 404 17300
"OPTIONS /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 0
"OPTIONS /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 0
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 52
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 52
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 52
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 52
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 52
Watching for file changes with StatReloader
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 52
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 52
"POST /api/auth/login/ HTTP/1.1" 200 771
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 52
"POST /api/setup/income-tax-configurations/ HTTP/1.1" 201 471
Not Found: /api/setup/income-tax-configurations/1/calculate_tax/
"POST /api/setup/income-tax-configurations/1/calculate_tax/ HTTP/1.1" 404 23
Not Found: /api/setup/income-tax-configurations/1/
"PATCH /api/setup/income-tax-configurations/1/ HTTP/1.1" 404 23
Not Found: /api/setup/income-tax-configurations/1/
"DELETE /api/setup/income-tax-configurations/1/ HTTP/1.1" 404 23
"OPTIONS /api/setup/income-tax-configurations/ HTTP/1.1" 200 0
Bad Request: /api/setup/income-tax-configurations/
"POST /api/setup/income-tax-configurations/ HTTP/1.1" 400 179
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 523
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 523
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 523
"GET /api/setup/income-tax-configurations/?company=1 HTTP/1.1" 200 523
Bad Request: /api/setup/income-tax-configurations/
"POST /api/setup/income-tax-configurations/ HTTP/1.1" 400 179
D:\odoo_erp\my_erp\backend\setup\models.py changed, reloading.
D:\odoo_erp\my_erp\backend\setup\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/accounting/accounttax/ HTTP/1.1" 200 57806
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"OPTIONS /api/setup/withholding-tax-configurations/ HTTP/1.1" 200 0
Bad Request: /api/setup/withholding-tax-configurations/
"POST /api/setup/withholding-tax-configurations/ HTTP/1.1" 400 179
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 69738
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"OPTIONS /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 0
"OPTIONS /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 0
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 52
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4196
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 52
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 52
"GET /admin/ HTTP/1.1" 200 69738
- Broken pipe from ('127.0.0.1', 60326)
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 52
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 52
Bad Request: /api/setup/withholding-tax-configurations/
"POST /api/setup/withholding-tax-configurations/ HTTP/1.1" 400 179
"POST /api/auth/login/ HTTP/1.1" 200 771
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 52
"POST /api/setup/withholding-tax-configurations/ HTTP/1.1" 201 575
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /admin/ HTTP/1.1" 200 69738
"GET /admin/ HTTP/1.1" 200 69738
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
Bad Request: /api/setup/withholding-tax-configurations/
"POST /api/setup/withholding-tax-configurations/ HTTP/1.1" 400 179
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
Bad Request: /api/setup/withholding-tax-configurations/
"POST /api/setup/withholding-tax-configurations/ HTTP/1.1" 400 179
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 627
"POST /api/setup/withholding-tax-configurations/ HTTP/1.1" 201 573
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /admin/accounting/accounttax/ HTTP/1.1" 200 57806
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/setup/withholdingtaxconfiguration/ HTTP/1.1" 200 60476
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1201
"POST /api/setup/withholding-tax-configurations/ HTTP/1.1" 201 564
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1766
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1766
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1766
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1766
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1766
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1766
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1766
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1766
"OPTIONS /api/setup/withholding-tax-configurations/3/ HTTP/1.1" 200 0
Not Found: /api/setup/withholding-tax-configurations/3/
"PUT /api/setup/withholding-tax-configurations/3/ HTTP/1.1" 404 23
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1766
"GET /api/setup/withholding-tax-configurations/?company=1 HTTP/1.1" 200 1766
"OPTIONS /api/setup/withholding-tax-configurations/2/ HTTP/1.1" 200 0
Not Found: /api/setup/withholding-tax-configurations/2/
"PUT /api/setup/withholding-tax-configurations/2/ HTTP/1.1" 404 23
"POST /api/auth/login/ HTTP/1.1" 200 771
Not Found: /api/setup/withholding-tax-configurations/2/
"GET /api/setup/withholding-tax-configurations/2/ HTTP/1.1" 404 23
Not Found: /api/setup/withholding-tax-configurations/2/
"PATCH /api/setup/withholding-tax-configurations/2/ HTTP/1.1" 404 23
D:\odoo_erp\my_erp\backend\setup\views.py changed, reloading.
Watching for file changes with StatReloader
