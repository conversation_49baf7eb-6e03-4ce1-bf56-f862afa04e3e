"""
Purchase Module Models - Integrated with Accounting
Based on Odoo's Purchase Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime

# Import accounting models for integration
from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountTax, AccountAnalyticAccount, ProductTemplate,
    ProductProduct, AccountPaymentTerm, AccountFiscalPosition
)


class PurchaseOrder(models.Model):
    """Purchase Order - Based on Odoo purchase.order"""

    STATES = [
        ('draft', 'RFQ'),
        ('sent', 'RFQ Sent'),
        ('to approve', 'To Approve'),
        ('purchase', 'Purchase Order'),
        ('done', 'Locked'),
        ('cancel', 'Cancelled'),
    ]

    INVOICE_STATUS = [
        ('no', 'Nothing to Bill'),
        ('to invoice', 'Waiting Bills'),
        ('invoiced', 'Fully Billed'),
    ]

    # Basic Information
    name = models.CharField(max_length=64, default='New')
    origin = models.CharField(max_length=64, blank=True, help_text="Source Document")
    partner_ref = models.CharField(max_length=64, blank=True, help_text="Vendor Reference")
    state = models.CharField(max_length=12, choices=STATES, default='draft')

    # Dates
    date_order = models.DateTimeField(default=datetime.now)
    date_approve = models.DateTimeField(null=True, blank=True)
    date_planned = models.DateTimeField(null=True, blank=True, help_text="Expected Date")

    # Partner Information
    partner_id = models.ForeignKey(ResPartner, on_delete=models.RESTRICT, help_text="Vendor")
    dest_address_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.RESTRICT, related_name='purchase_dest_orders', help_text="Drop Ship Address")

    # Purchase Information
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, default=1, help_text="Purchase Representative")

    # Pricing
    currency_id = models.CharField(max_length=3, default='PKR')

    # Amounts
    amount_untaxed = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Accounting Integration
    payment_term_id = models.ForeignKey(AccountPaymentTerm, null=True, blank=True, on_delete=models.SET_NULL)
    fiscal_position_id = models.ForeignKey(AccountFiscalPosition, null=True, blank=True, on_delete=models.SET_NULL)

    # Invoice Status
    invoice_status = models.CharField(max_length=16, choices=INVOICE_STATUS, default='no')
    invoice_ids = models.ManyToManyField(AccountMove, blank=True, help_text="Generated Bills", related_name='purchase_orders')
    invoice_count = models.IntegerField(default=0)

    # Delivery
    picking_type_id = models.IntegerField(null=True, blank=True, help_text="Operation Type")
    group_id = models.IntegerField(null=True, blank=True, help_text="Procurement Group")

    # Other
    notes = models.TextField(blank=True, help_text="Terms and Conditions")
    receipt_reminder_email = models.BooleanField(default=True)
    reminder_date_before_receipt = models.IntegerField(default=1, help_text="Days before receipt")

    # Alternative PO
    alternative_po_ids = models.ManyToManyField('self', blank=True, symmetrical=False, help_text="Alternative POs")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'purchase_order'
        ordering = ['-date_order', '-name']

    def __str__(self):
        return self.name

    def button_confirm(self):
        """Confirm the purchase order - Exact Odoo logic"""
        if self.state in ['draft', 'sent']:
            self.state = 'purchase'
            self.date_approve = datetime.now()
            self.name = self._get_sequence()
            self.save()

            # Update invoice status
            self._compute_invoice_status()

            # Create stock moves (if inventory module exists)
            self._create_picking()

            return True
        return False

    def button_cancel(self):
        """Cancel the purchase order"""
        if self.state in ['draft', 'sent', 'to approve', 'purchase']:
            self.state = 'cancel'
            self.save()
            return True
        return False

    def button_draft(self):
        """Reset to draft"""
        if self.state == 'cancel':
            self.state = 'draft'
            self.save()
            return True
        return False

    def action_rfq_send(self):
        """Send RFQ to vendor"""
        if self.state == 'draft':
            self.state = 'sent'
            self.save()
            return True
        return False

    def create_bill(self):
        """Create vendor bill from purchase order - Integration with accounting"""
        if self.invoice_status != 'to invoice':
            return None

        # Create bill (AccountMove)
        bill = AccountMove.objects.create(
            move_type='in_invoice',
            partner_id=self.partner_id,
            invoice_date=date.today(),
            payment_term_id=self.payment_term_id,
            fiscal_position_id=self.fiscal_position_id,
            currency_id=self.currency_id,
            invoice_origin=self.name,
            ref=self.partner_ref,
            company_id=self.company_id,
        )

        # Create bill lines from order lines
        for line in self.order_line.all():
            line.create_bill_line(bill)

        # Compute bill amounts
        bill._compute_amounts()

        # Link bill to order
        self.invoice_ids.add(bill)
        self.invoice_count += 1
        self._compute_invoice_status()

        return bill

    def _get_sequence(self):
        """Generate sequence number"""
        return f"PO{datetime.now().year}{self.id:05d}"

    def _compute_invoice_status(self):
        """Compute invoice status - Exact Odoo logic"""
        if not self.order_line.exists():
            self.invoice_status = 'no'
        elif all(line.qty_invoiced >= line.product_qty for line in self.order_line.all()):
            self.invoice_status = 'invoiced'
        elif any(line.qty_invoiced > 0 for line in self.order_line.all()):
            self.invoice_status = 'to invoice'
        else:
            self.invoice_status = 'to invoice'
        self.save()

    def _compute_amounts(self):
        """Compute order amounts"""
        self.amount_untaxed = sum(line.price_subtotal for line in self.order_line.all())
        self.amount_tax = sum(line.price_tax for line in self.order_line.all())
        self.amount_total = self.amount_untaxed + self.amount_tax
        self.save()

    def _create_picking(self):
        """Create stock picking (if inventory module exists)"""
        # This would create stock moves for inventory
        # For now, just update quantities
        for line in self.order_line.all():
            line.qty_received = line.product_qty
            line.save()


class PurchaseOrderLine(models.Model):
    """Purchase Order Line - Based on Odoo purchase.order.line"""

    INVOICE_STATUS = [
        ('no', 'Nothing to Bill'),
        ('to invoice', 'Waiting Bills'),
        ('invoiced', 'Fully Billed'),
    ]

    order_id = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='order_line')
    name = models.TextField(help_text="Description")
    sequence = models.IntegerField(default=10)

    # Product Information
    product_id = models.ForeignKey(ProductProduct, null=True, blank=True, on_delete=models.RESTRICT)
    product_template_id = models.ForeignKey(ProductTemplate, null=True, blank=True, on_delete=models.RESTRICT)
    product_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1, help_text="Quantity")
    product_uom = models.IntegerField(null=True, blank=True, help_text="Unit of Measure")

    # Quantities
    qty_received = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    qty_invoiced = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    qty_to_invoice = models.DecimalField(max_digits=16, decimal_places=4, default=0)

    # Pricing
    price_unit = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    price_subtotal = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Taxes
    taxes_id = models.ManyToManyField(AccountTax, blank=True, help_text="Taxes", related_name='purchase_order_lines')

    # Invoice Integration
    invoice_lines = models.ManyToManyField(AccountMoveLine, blank=True, help_text="Bill Lines", related_name='purchase_order_lines')
    invoice_status = models.CharField(max_length=16, choices=INVOICE_STATUS, default='no')

    # Analytics
    account_analytic_id = models.ForeignKey(AccountAnalyticAccount, null=True, blank=True, on_delete=models.SET_NULL)
    analytic_tag_ids = models.CharField(max_length=256, blank=True, help_text="Analytic Tags")

    # Dates
    date_planned = models.DateTimeField(help_text="Scheduled Date")

    # Other
    display_type = models.CharField(max_length=16, choices=[('line_section', 'Section'), ('line_note', 'Note')], blank=True)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'purchase_order_line'

    def __str__(self):
        return f"{self.order_id.name} - {self.name[:50]}"

    def create_bill_line(self, bill):
        """Create bill line from purchase order line - Exact Odoo logic"""
        if self.display_type:
            return None

        # Get expense account from product or category
        account = self._get_expense_account()

        # Create bill line (AccountMoveLine)
        bill_line = AccountMoveLine.objects.create(
            move_id=bill,
            name=self.name,
            account_id=account,
            quantity=self.product_qty,
            price_unit=self.price_unit,
            analytic_account_id=self.account_analytic_id,
            company_id=self.company_id,
        )

        # Add taxes
        bill_line.tax_ids.set(self.taxes_id.all())

        # Link to purchase order line
        self.invoice_lines.add(bill_line)

        # Update quantities
        self.qty_invoiced += self.product_qty
        self.qty_to_invoice = self.product_qty - self.qty_invoiced
        self._compute_invoice_status()

        return bill_line

    def _get_expense_account(self):
        """Get expense account for this product - Exact Odoo logic"""
        if self.product_id and self.product_id.product_tmpl_id.property_account_expense_id:
            return self.product_id.product_tmpl_id.property_account_expense_id
        elif self.product_id and self.product_id.product_tmpl_id.categ_id.property_account_expense_categ_id:
            return self.product_id.product_tmpl_id.categ_id.property_account_expense_categ_id
        else:
            # Default expense account
            return AccountAccount.objects.filter(
                account_type='expense',
                company_id=self.company_id
            ).first()

    def _compute_invoice_status(self):
        """Compute invoice status"""
        if self.qty_invoiced >= self.product_qty:
            self.invoice_status = 'invoiced'
        elif self.qty_invoiced > 0:
            self.invoice_status = 'to invoice'
        else:
            self.invoice_status = 'to invoice'
        self.save()

    def _compute_amounts(self):
        """Compute line amounts - Exact Odoo logic"""
        self.price_subtotal = self.price_unit * self.product_qty

        # Calculate taxes
        taxes = self.taxes_id.all()
        tax_amount = 0
        for tax in taxes:
            tax_amount += (self.price_subtotal * tax.amount / 100)

        self.price_tax = tax_amount
        self.price_total = self.price_subtotal + self.price_tax
        self.save()

    def _onchange_product_id(self):
        """Update fields when product changes - Exact Odoo logic"""
        if self.product_id:
            self.name = self.product_id.name
            self.price_unit = self.product_id.standard_price
            self.product_template_id = self.product_id.product_tmpl_id

            # Set default taxes
            if self.product_id.product_tmpl_id.supplier_taxes_id.exists():
                self.taxes_id.set(self.product_id.product_tmpl_id.supplier_taxes_id.all())
