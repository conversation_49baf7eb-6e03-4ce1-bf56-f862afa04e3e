import React, { useState, useEffect } from 'react';
import { Table, Button, Typography, Tag, Space } from 'antd';
import { PlusOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Invoices = () => {
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);

  // Mock data
  const mockInvoices = [
    {
      id: 1,
      name: 'INV/2024/001',
      partner_name: 'ABC Company Ltd.',
      invoice_date: '2024-01-15',
      invoice_date_due: '2024-02-14',
      amount_total: 25000,
      amount_residual: 25000,
      payment_state: 'not_paid',
      state: 'posted',
    },
    {
      id: 2,
      name: 'INV/2024/002',
      partner_name: 'XYZ Corporation',
      invoice_date: '2024-01-10',
      invoice_date_due: '2024-02-09',
      amount_total: 18000,
      amount_residual: 0,
      payment_state: 'paid',
      state: 'posted',
    },
  ];

  const columns = [
    {
      title: 'Invoice #',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Customer',
      dataIndex: 'partner_name',
      key: 'partner_name',
    },
    {
      title: 'Invoice Date',
      dataIndex: 'invoice_date',
      key: 'invoice_date',
    },
    {
      title: 'Due Date',
      dataIndex: 'invoice_date_due',
      key: 'invoice_date_due',
    },
    {
      title: 'Total Amount',
      dataIndex: 'amount_total',
      key: 'amount_total',
      render: (amount) => `PKR ${amount.toLocaleString()}`,
      align: 'right',
    },
    {
      title: 'Amount Due',
      dataIndex: 'amount_residual',
      key: 'amount_residual',
      render: (amount) => (
        <span className={amount > 0 ? 'balance-negative' : 'balance-positive'}>
          PKR {amount.toLocaleString()}
        </span>
      ),
      align: 'right',
    },
    {
      title: 'Payment Status',
      dataIndex: 'payment_state',
      key: 'payment_state',
      render: (status) => {
        const colors = {
          not_paid: 'red',
          partial: 'orange',
          paid: 'green',
          in_payment: 'blue',
        };
        const labels = {
          not_paid: 'Not Paid',
          partial: 'Partial',
          paid: 'Paid',
          in_payment: 'In Payment',
        };
        return <Tag color={colors[status]}>{labels[status]}</Tag>;
      },
    },
    {
      title: 'Status',
      dataIndex: 'state',
      key: 'state',
      render: (state) => (
        <Tag color={state === 'posted' ? 'green' : 'orange'}>
          {state === 'posted' ? 'Posted' : 'Draft'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <Space>
          <Button type="text" icon={<EyeOutlined />} size="small" />
          <Button type="text" icon={<EditOutlined />} size="small" />
        </Space>
      ),
    },
  ];

  useEffect(() => {
    setInvoices(mockInvoices);
  }, []);

  return (
    <div>
      <div className="page-header">
        <Title level={2}>Customer Invoices</Title>
        <p>Manage customer invoices and billing</p>
      </div>

      <div className="action-buttons">
        <Button type="primary" icon={<PlusOutlined />}>
          Create Invoice
        </Button>
      </div>

      <div className="table-container">
        <Table
          columns={columns}
          dataSource={invoices}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} invoices`,
          }}
        />
      </div>
    </div>
  );
};

export default Invoices;
