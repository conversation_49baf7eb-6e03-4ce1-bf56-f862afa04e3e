"""
Authentication Views - <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Profile Management
"""
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.http import JsonResponse
import json
from .serializers import (
    LoginSerializer, UserSerializer, UserProfileUpdateSerializer,
    UserPreferencesSerializer, ChangePasswordSerializer
)
from .utils import get_user_type, get_user_display_info, get_user_companies


@csrf_exempt
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login_view(request):
    """
    User login endpoint
    """
    try:
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            password = serializer.validated_data['password']
            remember_me = serializer.validated_data.get('remember_me', False)
            
            # Try to find user by email (get the first one if multiple exist)
            try:
                user = User.objects.filter(email=email).first()
                if not user:
                    return Response({
                        'success': False,
                        'error': 'Invalid email or password'
                    }, status=status.HTTP_401_UNAUTHORIZED)
                username = user.username
            except Exception:
                return Response({
                    'success': False,
                    'error': 'Invalid email or password'
                }, status=status.HTTP_401_UNAUTHORIZED)

            # Authenticate user
            user = authenticate(request, username=username, password=password)
            
            if user and user.is_active:
                # Login user
                login(request, user)
                
                # Create or get token
                token, created = Token.objects.get_or_create(user=user)
                
                # Update session expiry based on remember_me
                if not remember_me:
                    request.session.set_expiry(0)  # Session expires when browser closes
                else:
                    request.session.set_expiry(1209600)  # 2 weeks
                
                # Get user type and display info
                user_type = get_user_type(user)
                user_info = get_user_display_info(user)
                user_companies = get_user_companies(user)

                # Prepare user data
                user_data = {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'name': user.get_full_name() or user.username,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser,
                    'userType': user_type,
                    'userTypeLabel': user_info['type_label'],
                    'userTypeDescription': user_info['type_description'],
                    'groups': [group.name for group in user.groups.all()],
                    'companies': user_companies,
                    'permissions': user_info['permissions'],
                    'last_login': user.last_login.isoformat() if user.last_login else None,
                    'date_joined': user.date_joined.isoformat(),
                    'preferences': {}  # Default empty preferences
                }
                
                return Response({
                    'success': True,
                    'message': 'Login successful',
                    'user': user_data,
                    'token': token.key
                })
            else:
                return Response({
                    'success': False,
                    'error': 'Invalid email or password'
                }, status=status.HTTP_401_UNAUTHORIZED)
        else:
            return Response({
                'success': False,
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_view(request):
    """
    User logout endpoint
    """
    try:
        # Delete user token
        try:
            token = Token.objects.get(user=request.user)
            token.delete()
        except Token.DoesNotExist:
            pass
        
        # Logout user
        logout(request)
        
        return Response({
            'success': True,
            'message': 'Logged out successfully'
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_profile(request):
    """
    Get current user profile
    """
    try:
        user_data = {
            'id': request.user.id,
            'username': request.user.username,
            'email': request.user.email,
            'name': request.user.get_full_name() or request.user.username,
            'first_name': request.user.first_name,
            'last_name': request.user.last_name,
            'is_staff': request.user.is_staff,
            'is_superuser': request.user.is_superuser,
            'userType': 'super_user' if request.user.is_superuser else 'admin' if request.user.is_staff else 'user',
            'groups': [group.name for group in request.user.groups.all()],
            'last_login': request.user.last_login.isoformat() if request.user.last_login else None,
            'date_joined': request.user.date_joined.isoformat(),
            'preferences': {}  # Default empty preferences
        }
        
        return Response({
            'success': True,
            'user': user_data
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT'])
@permission_classes([permissions.IsAuthenticated])
def update_profile(request):
    """
    Update user profile
    """
    try:
        user = request.user
        data = request.data
        
        # Update basic fields
        if 'name' in data:
            # Split name into first and last name
            name_parts = data['name'].split(' ', 1)
            user.first_name = name_parts[0]
            user.last_name = name_parts[1] if len(name_parts) > 1 else ''
        
        if 'email' in data:
            # Check if email is already taken by another user
            if User.objects.exclude(id=user.id).filter(email=data['email']).exists():
                return Response({
                    'success': False,
                    'error': 'This email is already in use'
                }, status=status.HTTP_400_BAD_REQUEST)
            user.email = data['email']
        
        user.save()
        
        # Return updated user data
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'name': user.get_full_name() or user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'userType': 'super_user' if user.is_superuser else 'admin' if user.is_staff else 'user',
            'groups': [group.name for group in user.groups.all()],
        }
        
        return Response({
            'success': True,
            'message': 'Profile updated successfully',
            'user': user_data
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT'])
@permission_classes([permissions.IsAuthenticated])
def update_preferences(request):
    """
    Update user preferences
    """
    try:
        # For now, just return success since we're using Django's built-in User model
        # In a real implementation, you'd store preferences in a separate model or JSON field
        
        return Response({
            'success': True,
            'message': 'Preferences updated successfully'
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def check_auth(request):
    """
    Check if user is authenticated
    """
    if request.user.is_authenticated:
        user_data = {
            'id': request.user.id,
            'username': request.user.username,
            'email': request.user.email,
            'name': request.user.get_full_name() or request.user.username,
            'userType': 'super_user' if request.user.is_superuser else 'admin' if request.user.is_staff else 'user',
            'groups': [group.name for group in request.user.groups.all()],
        }
        
        return Response({
            'authenticated': True,
            'user': user_data
        })
    else:
        return Response({
            'authenticated': False
        })
