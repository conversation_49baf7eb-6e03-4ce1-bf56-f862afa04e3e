/**
 * Main Layout Component - Following Odoo's Layout Structure
 * Professional ERP Interface with Modern UX/UI
 */
import React, { useState, useEffect } from 'react';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Badge,
  Button,
  Space,
  Typography,
  Drawer,
  Switch,
  Tooltip,
  Breadcrumb,
  Divider
} from 'antd';
import {
  MenuOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  SearchOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SunOutlined,
  MoonOutlined,
  GlobalOutlined,
  DashboardOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  ShopOutlined,
  InboxOutlined,
  TeamOutlined,
  PhoneOutlined,
  ProjectOutlined,
  BarChartOutlined,
  ToolOutlined,
  FileTextOutlined,
  CreditCardOutlined,
  WalletOutlined,
  ShoppingOutlined,
  BankOutlined,
  SwapOutlined,
  HomeOutlined,
  EditOutlined,
  ApartmentOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  AccountBookOutlined,
  PercentageOutlined,
  BookOutlined,
  CodeOutlined,
  AppstoreOutlined,
  RightOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../auth/AuthProvider';
import CompanySelector from './CompanySelector';
import NotificationCenter from './NotificationCenter';
import GlobalSearch from './GlobalSearch';
import './MainLayout.css';

const { Header, Sider, Content, Footer } = Layout;
const { Text, Title } = Typography;

// QuickBooks-style modular navigation structure
const getModuleApps = (canAccessModule, isSuperUser, isAdmin) => [
  {
    key: 'dashboard',
    icon: <DashboardOutlined />,
    label: 'Dashboard',
    path: '/dashboard',
    color: '#722ed1'
  },
  {
    key: 'accounting',
    icon: <DollarOutlined />,
    label: 'Accounting',
    path: '/accounting',
    disabled: !canAccessModule('accounting'),
    color: '#1890ff'
  },
  {
    key: 'sales',
    icon: <ShoppingCartOutlined />,
    label: 'Sales',
    path: '/sales',
    disabled: !canAccessModule('sales'),
    color: '#52c41a'
  },
  {
    key: 'purchase',
    icon: <ShopOutlined />,
    label: 'Purchase',
    path: '/purchase',
    disabled: !canAccessModule('purchase'),
    color: '#fa8c16'
  },
  {
    key: 'inventory',
    icon: <InboxOutlined />,
    label: 'Inventory',
    path: '/inventory',
    disabled: !canAccessModule('inventory'),
    color: '#13c2c2'
  },
  {
    key: 'hr',
    icon: <TeamOutlined />,
    label: 'HR',
    path: '/hr',
    disabled: !canAccessModule('hr'),
    color: '#eb2f96'
  },
  {
    key: 'crm',
    icon: <PhoneOutlined />,
    label: 'CRM',
    path: '/crm',
    disabled: !canAccessModule('crm'),
    color: '#f5222d'
  },
  {
    key: 'project',
    icon: <ProjectOutlined />,
    label: 'Projects',
    path: '/project',
    disabled: !canAccessModule('project'),
    color: '#faad14'
  },
  {
    key: 'manufacturing',
    icon: <ToolOutlined />,
    label: 'Manufacturing',
    path: '/manufacturing',
    disabled: !canAccessModule('manufacturing'),
    color: '#a0d911'
  },
  {
    key: 'analytics',
    icon: <BarChartOutlined />,
    label: 'Analytics',
    path: '/analytics',
    disabled: !canAccessModule('analytics'),
    color: '#2f54eb'
  },
  // Quick Access Apps
  {
    key: 'invoicing',
    icon: <CreditCardOutlined />,
    label: 'Invoicing',
    path: '/invoicing',
    disabled: !canAccessModule('accounting'),
    color: '#1890ff'
  },
  {
    key: 'expenses',
    icon: <WalletOutlined />,
    label: 'Expenses',
    path: '/expenses',
    disabled: !canAccessModule('hr'),
    color: '#eb2f96'
  },
  {
    key: 'pos',
    icon: <ShoppingOutlined />,
    label: 'POS',
    path: '/pos',
    disabled: !canAccessModule('pos'),
    color: '#52c41a'
  },
  // Admin Apps
  ...(isSuperUser || isAdmin ? [{
    key: 'setup',
    icon: <ToolOutlined />,
    label: 'Setup',
    path: '/setup',
    color: '#8c8c8c'
  }] : []),
  ...(isSuperUser || isAdmin ? [{
    key: 'settings',
    icon: <SettingOutlined />,
    label: 'Settings',
    path: '/settings',
    color: '#595959'
  }] : [])
];

// Get contextual navigation for current module
const getModuleNavigation = (currentModule, canAccessModule, isSuperUser, isAdmin) => {
  const navigationMap = {
    accounting: [
      { key: 'accounting-dashboard', label: 'Dashboard', path: '/accounting', icon: <DashboardOutlined /> },
      { key: 'customers', label: 'Customers', path: '/accounting/customers', icon: <UserOutlined /> },
      { key: 'vendors', label: 'Vendors', path: '/accounting/vendors', icon: <ShopOutlined /> },
      { key: 'invoices', label: 'Invoices', path: '/accounting/invoices', icon: <FileTextOutlined /> },
      { key: 'bills', label: 'Bills', path: '/accounting/bills', icon: <CreditCardOutlined /> },
      { key: 'payments', label: 'Payments', path: '/accounting/payments', icon: <WalletOutlined /> },
      { key: 'reconciliation', label: 'Reconciliation', path: '/accounting/reconciliation', icon: <BankOutlined /> },
      { key: 'reports', label: 'Reports', path: '/accounting/reports', icon: <BarChartOutlined /> }
    ],
    sales: [
      { key: 'sales-dashboard', label: 'Dashboard', path: '/sales', icon: <DashboardOutlined /> },
      { key: 'quotations', label: 'Quotations', path: '/sales/quotations', icon: <FileTextOutlined /> },
      { key: 'orders', label: 'Orders', path: '/sales/orders', icon: <ShoppingCartOutlined /> },
      { key: 'customers-sales', label: 'Customers', path: '/sales/customers', icon: <UserOutlined /> },
      { key: 'products-sales', label: 'Products', path: '/sales/products', icon: <InboxOutlined /> },
      { key: 'sales-reports', label: 'Reports', path: '/sales/reports', icon: <BarChartOutlined /> }
    ],
    purchase: [
      { key: 'purchase-dashboard', label: 'Dashboard', path: '/purchase', icon: <DashboardOutlined /> },
      { key: 'rfq', label: 'RFQ', path: '/purchase/rfq', icon: <FileTextOutlined /> },
      { key: 'purchase-orders', label: 'Orders', path: '/purchase/orders', icon: <ShopOutlined /> },
      { key: 'vendors-purchase', label: 'Vendors', path: '/purchase/vendors', icon: <UserOutlined /> },
      { key: 'products-purchase', label: 'Products', path: '/purchase/products', icon: <InboxOutlined /> },
      { key: 'purchase-reports', label: 'Reports', path: '/purchase/reports', icon: <BarChartOutlined /> }
    ],
    inventory: [
      { key: 'inventory-dashboard', label: 'Dashboard', path: '/inventory', icon: <DashboardOutlined /> },
      { key: 'products-inventory', label: 'Products', path: '/inventory/products', icon: <InboxOutlined /> },
      { key: 'stock-moves', label: 'Stock Moves', path: '/inventory/moves', icon: <SwapOutlined /> },
      { key: 'adjustments', label: 'Adjustments', path: '/inventory/adjustments', icon: <EditOutlined /> },
      { key: 'warehouses', label: 'Warehouses', path: '/inventory/warehouses', icon: <HomeOutlined /> },
      { key: 'inventory-reports', label: 'Reports', path: '/inventory/reports', icon: <BarChartOutlined /> }
    ],
    hr: [
      { key: 'hr-dashboard', label: 'Dashboard', path: '/hr', icon: <DashboardOutlined /> },
      { key: 'employees', label: 'Employees', path: '/hr/employees', icon: <TeamOutlined /> },
      { key: 'departments', label: 'Departments', path: '/hr/departments', icon: <ApartmentOutlined /> },
      { key: 'attendance', label: 'Attendance', path: '/hr/attendance', icon: <ClockCircleOutlined /> },
      { key: 'leaves', label: 'Time Off', path: '/hr/leaves', icon: <CalendarOutlined /> },
      { key: 'hr-reports', label: 'Reports', path: '/hr/reports', icon: <BarChartOutlined /> }
    ],
    setup: isSuperUser || isAdmin ? [
      { key: 'company-setup', label: 'Company', path: '/setup/company', icon: <GlobalOutlined /> },
      { key: 'chart-of-accounts', label: 'Chart of Accounts', path: '/setup/chart-of-accounts', icon: <AccountBookOutlined /> },
      { key: 'fiscal-year', label: 'Fiscal Year', path: '/setup/fiscal-year', icon: <CalendarOutlined /> },
      { key: 'taxes', label: 'Taxes', path: '/setup/taxes', icon: <PercentageOutlined /> },
      { key: 'currencies', label: 'Currencies', path: '/setup/currencies', icon: <DollarOutlined /> },
      { key: 'payment-terms', label: 'Payment Terms', path: '/setup/payment-terms', icon: <CreditCardOutlined /> },
      { key: 'journals', label: 'Journals', path: '/setup/journals', icon: <BookOutlined /> },
      { key: 'bank-accounts', label: 'Bank Accounts', path: '/setup/bank-accounts', icon: <BankOutlined /> }
    ] : [],
    settings: isSuperUser || isAdmin ? [
      { key: 'general-settings', label: 'General', path: '/settings/general', icon: <SettingOutlined /> },
      { key: 'users', label: 'Users & Companies', path: '/settings/users', icon: <UserOutlined /> },
      { key: 'technical', label: 'Technical', path: '/settings/technical', icon: <CodeOutlined /> }
    ] : []
  };

  return navigationMap[currentModule] || [];
};

const MainLayout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [showAppSwitcher, setShowAppSwitcher] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, isSuperUser, isAdmin, canAccessModule, currentCompany } = useAuth();

  // Get current module from path
  const getCurrentModule = () => {
    const path = location.pathname;
    if (path.startsWith('/accounting')) return 'accounting';
    if (path.startsWith('/sales')) return 'sales';
    if (path.startsWith('/purchase')) return 'purchase';
    if (path.startsWith('/inventory')) return 'inventory';
    if (path.startsWith('/hr')) return 'hr';
    if (path.startsWith('/crm')) return 'crm';
    if (path.startsWith('/project')) return 'project';
    if (path.startsWith('/manufacturing')) return 'manufacturing';
    if (path.startsWith('/analytics')) return 'analytics';
    if (path.startsWith('/invoicing')) return 'invoicing';
    if (path.startsWith('/expenses')) return 'expenses';
    if (path.startsWith('/pos')) return 'pos';
    if (path.startsWith('/setup')) return 'setup';
    if (path.startsWith('/settings')) return 'settings';
    return 'dashboard';
  };

  const currentModule = getCurrentModule();
  const moduleApps = getModuleApps(canAccessModule, isSuperUser(), isAdmin());
  const currentModuleNav = getModuleNavigation(currentModule, canAccessModule, isSuperUser(), isAdmin());

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle fullscreen
  const toggleFullscreen = () => {
    if (!fullscreen) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
    setFullscreen(!fullscreen);
  };

  // User dropdown menu
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'My Profile'
    },
    {
      key: 'preferences',
      icon: <SettingOutlined />,
      label: 'Preferences'
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: logout
    }
  ];

  // Handle app switching
  const handleAppSwitch = (appKey) => {
    const app = moduleApps.find(a => a.key === appKey);
    if (app && !app.disabled) {
      navigate(app.path);
      setShowAppSwitcher(false);
      if (isMobile) {
        setMobileDrawerVisible(false);
      }
    }
  };

  // Handle navigation click
  const handleNavClick = (navItem) => {
    navigate(navItem.path);
    if (isMobile) {
      setMobileDrawerVisible(false);
    }
  };

  // Get current app info
  const getCurrentApp = () => {
    return moduleApps.find(app => app.key === currentModule) || moduleApps[0];
  };

  const currentApp = getCurrentApp();

  return (
    <Layout className="main-layout" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'row' }}>
      {/* QuickBooks-style Modular Navigation */}
      {!isMobile && (
        <div className="qb-style-sidebar" style={{
          width: collapsed ? '80px' : '400px',
          background: '#fff',
          borderRight: '1px solid #f0f0f0',
          transition: 'width 0.3s ease',
          display: 'flex',
          flexDirection: 'column',
          flexShrink: 0
        }}>
          {/* App Header */}
          <div className="app-header" style={{
            padding: '16px',
            borderBottom: '1px solid #f0f0f0',
            background: currentApp?.color || '#1890ff',
            color: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: collapsed ? 'center' : 'space-between'
          }}>
            {!collapsed && (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ marginRight: '8px', fontSize: '18px' }}>{currentApp?.icon}</span>
                <Title level={4} style={{ margin: 0, color: '#fff' }}>
                  {currentApp?.label}
                </Title>
              </div>
            )}
            {collapsed && (
              <span style={{ fontSize: '20px' }}>{currentApp?.icon}</span>
            )}
            {!collapsed && (
              <Button
                type="text"
                icon={<AppstoreOutlined />}
                onClick={() => setShowAppSwitcher(!showAppSwitcher)}
                style={{ color: '#fff' }}
                title="Switch App"
              />
            )}
          </div>

          {/* Module Navigation */}
          {!collapsed && currentModuleNav.length > 0 && (
            <div className="module-navigation" style={{
              flex: 1,
              padding: '8px 0',
              overflowY: 'auto'
            }}>
              {currentModuleNav.map((navItem) => (
                <div
                  key={navItem.key}
                  className="nav-item"
                  style={{
                    padding: '12px 20px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    transition: 'all 0.3s ease',
                    borderLeft: location.pathname === navItem.path ? '3px solid ' + (currentApp?.color || '#1890ff') : '3px solid transparent',
                    background: location.pathname === navItem.path ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
                    color: location.pathname === navItem.path ? (currentApp?.color || '#1890ff') : '#666'
                  }}
                  onClick={() => handleNavClick(navItem)}
                  onMouseEnter={(e) => {
                    if (location.pathname !== navItem.path) {
                      e.target.style.background = '#f5f5f5';
                      e.target.style.color = '#333';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (location.pathname !== navItem.path) {
                      e.target.style.background = 'transparent';
                      e.target.style.color = '#666';
                    }
                  }}
                >
                  <span style={{ marginRight: '12px', fontSize: '16px' }}>{navItem.icon}</span>
                  <span style={{
                    fontWeight: location.pathname === navItem.path ? '600' : '400',
                    whiteSpace: 'nowrap',
                    overflow: 'visible',
                    textOverflow: 'clip',
                    flex: 1
                  }}>
                    {navItem.label}
                  </span>
                </div>
              ))}
            </div>
          )}

          {/* Collapsed App Switcher */}
          {collapsed && (
            <div className="collapsed-apps" style={{
              flex: 1,
              padding: '8px 0',
              overflowY: 'auto'
            }}>
              {moduleApps.filter(app => !app.disabled).slice(0, 8).map((app) => (
                <div
                  key={app.key}
                  style={{
                    padding: '12px',
                    cursor: 'pointer',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    transition: 'all 0.3s ease',
                    background: currentModule === app.key ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
                    borderRadius: '6px',
                    margin: '4px 8px'
                  }}
                  onClick={() => handleAppSwitch(app.key)}
                  title={app.label}
                  onMouseEnter={(e) => {
                    e.target.style.background = app.color + '20';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = currentModule === app.key ? 'rgba(24, 144, 255, 0.1)' : 'transparent';
                  }}
                >
                  <span style={{ fontSize: '18px', color: app.color }}>{app.icon}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Mobile Drawer */}
      <Drawer
        title="DataiCraft ERP"
        placement="left"
        onClose={() => setMobileDrawerVisible(false)}
        open={mobileDrawerVisible}
        styles={{ body: { padding: 0 } }}
        width={280}
      >
        <div style={{ padding: '16px 0' }}>
          {/* App Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(2, 1fr)',
            gap: '12px',
            padding: '0 16px',
            marginBottom: '24px'
          }}>
            {moduleApps.filter(app => !app.disabled).map((app) => (
              <div
                key={app.key}
                style={{
                  padding: '16px',
                  background: currentModule === app.key ? app.color + '20' : '#f5f5f5',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  textAlign: 'center',
                  transition: 'all 0.3s ease',
                  border: currentModule === app.key ? `2px solid ${app.color}` : '2px solid transparent'
                }}
                onClick={() => handleAppSwitch(app.key)}
              >
                <div style={{ fontSize: '24px', color: app.color, marginBottom: '8px' }}>
                  {app.icon}
                </div>
                <div style={{ fontSize: '12px', fontWeight: '500', color: '#333' }}>
                  {app.label}
                </div>
              </div>
            ))}
          </div>

          {/* Current Module Navigation */}
          {currentModuleNav.length > 0 && (
            <div>
              <div style={{
                padding: '0 16px 8px',
                fontSize: '14px',
                fontWeight: '600',
                color: '#666',
                borderBottom: '1px solid #f0f0f0',
                marginBottom: '8px'
              }}>
                {currentApp?.label} Menu
              </div>
              {currentModuleNav.map((navItem) => (
                <div
                  key={navItem.key}
                  style={{
                    padding: '12px 16px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    background: location.pathname === navItem.path ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
                    color: location.pathname === navItem.path ? '#1890ff' : '#666',
                    borderLeft: location.pathname === navItem.path ? '3px solid #1890ff' : '3px solid transparent'
                  }}
                  onClick={() => handleNavClick(navItem)}
                >
                  <span style={{ marginRight: '12px' }}>{navItem.icon}</span>
                  <span style={{ fontWeight: location.pathname === navItem.path ? '600' : '400' }}>
                    {navItem.label}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      </Drawer>

      {/* App Switcher Overlay */}
      {showAppSwitcher && !isMobile && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1000,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
          onClick={() => setShowAppSwitcher(false)}
        >
          <div
            style={{
              background: '#fff',
              borderRadius: '12px',
              padding: '24px',
              maxWidth: '600px',
              width: '90%',
              maxHeight: '80vh',
              overflowY: 'auto'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{ marginBottom: '24px' }}>
              <Title level={3} style={{ margin: 0, textAlign: 'center' }}>
                Switch Application
              </Title>
            </div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
              gap: '16px'
            }}>
              {moduleApps.filter(app => !app.disabled).map((app) => (
                <div
                  key={app.key}
                  style={{
                    padding: '20px',
                    background: currentModule === app.key ? app.color + '20' : '#f5f5f5',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    textAlign: 'center',
                    transition: 'all 0.3s ease',
                    border: currentModule === app.key ? `2px solid ${app.color}` : '2px solid transparent'
                  }}
                  onClick={() => handleAppSwitch(app.key)}
                  onMouseEnter={(e) => {
                    if (currentModule !== app.key) {
                      e.target.style.background = app.color + '10';
                      e.target.style.transform = 'translateY(-2px)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (currentModule !== app.key) {
                      e.target.style.background = '#f5f5f5';
                      e.target.style.transform = 'translateY(0)';
                    }
                  }}
                >
                  <div style={{ fontSize: '32px', color: app.color, marginBottom: '12px' }}>
                    {app.icon}
                  </div>
                  <div style={{ fontSize: '14px', fontWeight: '600', color: '#333' }}>
                    {app.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <Layout className="site-layout" style={{ flex: 1, minWidth: 0 }}>
        {/* Header */}
        <Header className="layout-header">
          <div className="header-left">
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => isMobile ? setMobileDrawerVisible(true) : setCollapsed(!collapsed)}
              className="trigger"
              title={isMobile ? 'Open Menu' : (collapsed ? 'Expand Sidebar' : 'Collapse Sidebar')}
            />

            <CompanySelector />
          </div>

          <div className="header-center">
            <GlobalSearch />
          </div>

          <div className="header-right">
            <Space size="middle">
              <Tooltip title={darkMode ? 'Light Mode' : 'Dark Mode'}>
                <Switch
                  checkedChildren={<MoonOutlined />}
                  unCheckedChildren={<SunOutlined />}
                  checked={darkMode}
                  onChange={setDarkMode}
                />
              </Tooltip>

              <Tooltip title={fullscreen ? 'Exit Fullscreen' : 'Fullscreen'}>
                <Button
                  type="text"
                  icon={fullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                  onClick={toggleFullscreen}
                />
              </Tooltip>

              <NotificationCenter />

              <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
                <Space className="user-info" style={{ cursor: 'pointer' }}>
                  <Avatar icon={<UserOutlined />} src={user?.avatar} />
                  <div className="user-details">
                    <Text strong>{user?.name}</Text>
                    <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>
                      {isSuperUser() ? 'Super User' : isAdmin() ? 'Administrator' : user?.groups?.[0]}
                    </Text>
                  </div>
                </Space>
              </Dropdown>
            </Space>
          </div>
        </Header>

        {/* Content */}
        <Content className="layout-content">
          {children}
        </Content>

        {/* Footer */}
        <Footer className="layout-footer">
          <div className="footer-content">
            <Text type="secondary">
              © 2025 DataiCraft Solutions - Professional ERP System
            </Text>
            <Space>
              <Text type="secondary">Version 1.0.0</Text>
              <Divider type="vertical" />
              <Text type="secondary">
                User: {user?.name} ({isSuperUser() ? 'Super User' : isAdmin() ? 'Admin' : 'User'})
              </Text>
              <Divider type="vertical" />
              <Text type="secondary">Company: {currentCompany?.name}</Text>
            </Space>
          </div>
        </Footer>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
