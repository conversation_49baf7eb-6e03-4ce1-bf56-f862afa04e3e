/**
 * Main Layout Component - Following Odoo's Layout Structure
 * Professional ERP Interface with Modern UX/UI
 */
import React, { useState, useEffect } from 'react';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Badge,
  Button,
  Space,
  Typography,
  Drawer,
  Switch,
  Tooltip,
  Breadcrumb,
  Divider
} from 'antd';
import {
  MenuOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  SearchOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SunOutlined,
  MoonOutlined,
  GlobalOutlined,
  DashboardOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  ShopOutlined,
  InboxOutlined,
  TeamOutlined,
  PhoneOutlined,
  ProjectOutlined,
  BarChartOutlined,
  ToolOutlined,
  FileTextOutlined,
  CreditCardOutlined,
  WalletOutlined,
  ShoppingOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../auth/AuthProvider';
import CompanySelector from './CompanySelector';
import NotificationCenter from './NotificationCenter';
import GlobalSearch from './GlobalSearch';
import './MainLayout.css';

const { Header, Sider, Content, Footer } = Layout;
const { Text, Title } = Typography;

// Menu items following Odoo's app structure
const getMenuItems = (canAccessModule, isSuperUser, isAdmin) => [
  {
    key: 'dashboard',
    icon: <DashboardOutlined />,
    label: 'Dashboard',
    path: '/dashboard'
  },
  {
    key: 'accounting',
    icon: <DollarOutlined />,
    label: 'Accounting',
    path: '/accounting',
    disabled: !canAccessModule('accounting'),
    children: [
      { key: 'accounting-dashboard', label: 'Dashboard', path: '/accounting' },
      { key: 'customers', label: 'Customers', path: '/accounting/customers' },
      { key: 'vendors', label: 'Vendors', path: '/accounting/vendors' },
      { key: 'invoices', label: 'Customer Invoices', path: '/accounting/invoices' },
      { key: 'bills', label: 'Vendor Bills', path: '/accounting/bills' },
      { key: 'payments', label: 'Payments', path: '/accounting/payments' },
      { key: 'bank-reconciliation', label: 'Bank Reconciliation', path: '/accounting/reconciliation' },
      { key: 'reports', label: 'Reporting', path: '/accounting/reports' },
      { key: 'configuration', label: 'Configuration', path: '/accounting/config' }
    ]
  },
  {
    key: 'sales',
    icon: <ShoppingCartOutlined />,
    label: 'Sales',
    path: '/sales',
    disabled: !canAccessModule('sales'),
    children: [
      { key: 'sales-dashboard', label: 'Dashboard', path: '/sales' },
      { key: 'quotations', label: 'Quotations', path: '/sales/quotations' },
      { key: 'orders', label: 'Sales Orders', path: '/sales/orders' },
      { key: 'customers-sales', label: 'Customers', path: '/sales/customers' },
      { key: 'products-sales', label: 'Products', path: '/sales/products' },
      { key: 'sales-reports', label: 'Reporting', path: '/sales/reports' }
    ]
  },
  {
    key: 'purchase',
    icon: <ShopOutlined />,
    label: 'Purchase',
    path: '/purchase',
    disabled: !canAccessModule('purchase'),
    children: [
      { key: 'purchase-dashboard', label: 'Dashboard', path: '/purchase' },
      { key: 'rfq', label: 'Requests for Quotation', path: '/purchase/rfq' },
      { key: 'purchase-orders', label: 'Purchase Orders', path: '/purchase/orders' },
      { key: 'vendors-purchase', label: 'Vendors', path: '/purchase/vendors' },
      { key: 'products-purchase', label: 'Products', path: '/purchase/products' },
      { key: 'purchase-reports', label: 'Reporting', path: '/purchase/reports' }
    ]
  },
  {
    key: 'inventory',
    icon: <InboxOutlined />,
    label: 'Inventory',
    path: '/inventory',
    disabled: !canAccessModule('inventory'),
    children: [
      { key: 'inventory-dashboard', label: 'Dashboard', path: '/inventory' },
      { key: 'products-inventory', label: 'Products', path: '/inventory/products' },
      { key: 'stock-moves', label: 'Stock Moves', path: '/inventory/moves' },
      { key: 'inventory-adjustments', label: 'Inventory Adjustments', path: '/inventory/adjustments' },
      { key: 'warehouses', label: 'Warehouses', path: '/inventory/warehouses' },
      { key: 'inventory-reports', label: 'Reporting', path: '/inventory/reports' }
    ]
  },
  {
    key: 'hr',
    icon: <TeamOutlined />,
    label: 'Human Resources',
    path: '/hr',
    disabled: !canAccessModule('hr'),
    children: [
      { key: 'hr-dashboard', label: 'Dashboard', path: '/hr' },
      { key: 'employees', label: 'Employees', path: '/hr/employees' },
      { key: 'departments', label: 'Departments', path: '/hr/departments' },
      { key: 'attendance', label: 'Attendance', path: '/hr/attendance' },
      { key: 'leaves', label: 'Time Off', path: '/hr/leaves' },
      { key: 'hr-reports', label: 'Reporting', path: '/hr/reports' }
    ]
  },
  {
    key: 'crm',
    icon: <PhoneOutlined />,
    label: 'CRM',
    path: '/crm',
    disabled: !canAccessModule('crm'),
    children: [
      { key: 'crm-dashboard', label: 'Dashboard', path: '/crm' },
      { key: 'leads', label: 'Leads', path: '/crm/leads' },
      { key: 'opportunities', label: 'Opportunities', path: '/crm/opportunities' },
      { key: 'customers-crm', label: 'Customers', path: '/crm/customers' },
      { key: 'crm-reports', label: 'Reporting', path: '/crm/reports' }
    ]
  },
  {
    key: 'project',
    icon: <ProjectOutlined />,
    label: 'Project',
    path: '/project',
    disabled: !canAccessModule('project'),
    children: [
      { key: 'project-dashboard', label: 'Dashboard', path: '/project' },
      { key: 'projects', label: 'Projects', path: '/project/projects' },
      { key: 'tasks', label: 'Tasks', path: '/project/tasks' },
      { key: 'timesheets', label: 'Timesheets', path: '/project/timesheets' },
      { key: 'project-reports', label: 'Reporting', path: '/project/reports' }
    ]
  },
  {
    key: 'manufacturing',
    icon: <ToolOutlined />,
    label: 'Manufacturing',
    path: '/manufacturing',
    disabled: !canAccessModule('manufacturing'),
    children: [
      { key: 'manufacturing-dashboard', label: 'Dashboard', path: '/manufacturing' },
      { key: 'manufacturing-orders', label: 'Manufacturing Orders', path: '/manufacturing/orders' },
      { key: 'bom', label: 'Bills of Materials', path: '/manufacturing/bom' },
      { key: 'work-centers', label: 'Work Centers', path: '/manufacturing/workcenters' },
      { key: 'manufacturing-reports', label: 'Reporting', path: '/manufacturing/reports' }
    ]
  },
  {
    key: 'analytics',
    icon: <BarChartOutlined />,
    label: 'Analytics',
    path: '/analytics',
    disabled: !canAccessModule('analytics')
  },
  {
    key: 'financial-reports',
    icon: <FileTextOutlined />,
    label: 'Financial Reports',
    path: '/financial-reports',
    disabled: !canAccessModule('accounting')
  },
  {
    key: 'invoicing',
    icon: <CreditCardOutlined />,
    label: 'Invoicing',
    path: '/invoicing',
    disabled: !canAccessModule('accounting')
  },
  {
    key: 'expenses',
    icon: <WalletOutlined />,
    label: 'Expenses',
    path: '/expenses',
    disabled: !canAccessModule('hr')
  },
  {
    key: 'pos',
    icon: <ShoppingOutlined />,
    label: 'Point of Sale',
    path: '/pos',
    disabled: !canAccessModule('pos')
  },
  // Setup (only for Super User and Admin)
  ...(isSuperUser || isAdmin ? [{
    key: 'setup',
    icon: <ToolOutlined />,
    label: 'Setup',
    path: '/setup',
    children: [
      { key: 'company-setup', label: 'Company Setup', path: '/setup/company' },
      { key: 'chart-of-accounts-setup', label: 'Chart of Accounts', path: '/setup/chart-of-accounts' },
      { key: 'fiscal-year-setup', label: 'Fiscal Year', path: '/setup/fiscal-year' },
      { key: 'taxes-setup', label: 'Taxes Configuration', path: '/setup/taxes' },
      { key: 'currencies-setup', label: 'Currencies', path: '/setup/currencies' },
      { key: 'payment-terms-setup', label: 'Payment Terms', path: '/setup/payment-terms' },
      { key: 'journals-setup', label: 'Journals', path: '/setup/journals' },
      { key: 'bank-accounts-setup', label: 'Bank Accounts', path: '/setup/bank-accounts' },
      { key: 'product-categories-setup', label: 'Product Categories', path: '/setup/product-categories' },
      { key: 'warehouses-setup', label: 'Warehouses', path: '/setup/warehouses' },
      { key: 'departments-setup', label: 'Departments', path: '/setup/departments' },
      { key: 'employee-positions-setup', label: 'Employee Positions', path: '/setup/positions' },
      { key: 'project-stages-setup', label: 'Project Stages', path: '/setup/project-stages' },
      { key: 'manufacturing-setup', label: 'Manufacturing Setup', path: '/setup/manufacturing' }
    ]
  }] : []),
  // Settings (only for Super User and Admin)
  ...(isSuperUser || isAdmin ? [{
    key: 'settings',
    icon: <SettingOutlined />,
    label: 'Settings',
    path: '/settings',
    children: [
      { key: 'general-settings', label: 'General Settings', path: '/settings/general' },
      { key: 'users', label: 'Users & Companies', path: '/settings/users' },
      { key: 'technical', label: 'Technical', path: '/settings/technical' }
    ]
  }] : [])
];

const MainLayout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [activeSubmenu, setActiveSubmenu] = useState(null);
  const [submenuVisible, setSubmenuVisible] = useState(false);
  
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, isSuperUser, isAdmin, canAccessModule, currentCompany } = useAuth();

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle fullscreen
  const toggleFullscreen = () => {
    if (!fullscreen) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
    setFullscreen(!fullscreen);
  };

  // User dropdown menu
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'My Profile'
    },
    {
      key: 'preferences',
      icon: <SettingOutlined />,
      label: 'Preferences'
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: logout
    }
  ];

  // Get current menu key from location
  const getCurrentMenuKey = () => {
    const path = location.pathname;
    if (path.startsWith('/accounting')) return ['accounting'];
    if (path.startsWith('/sales')) return ['sales'];
    if (path.startsWith('/purchase')) return ['purchase'];
    if (path.startsWith('/inventory')) return ['inventory'];
    if (path.startsWith('/hr')) return ['hr'];
    if (path.startsWith('/crm')) return ['crm'];
    if (path.startsWith('/project')) return ['project'];
    if (path.startsWith('/manufacturing')) return ['manufacturing'];
    if (path.startsWith('/analytics')) return ['analytics'];
    if (path.startsWith('/financial-reports')) return ['financial-reports'];
    if (path.startsWith('/invoicing')) return ['invoicing'];
    if (path.startsWith('/expenses')) return ['expenses'];
    if (path.startsWith('/pos')) return ['pos'];
    if (path.startsWith('/settings')) return ['settings'];
    return ['dashboard'];
  };

  // Handle menu click
  const handleMenuClick = ({ key, keyPath }) => {
    const menuItems = getMenuItems(canAccessModule, isSuperUser(), isAdmin());
    
    const findMenuItem = (items, targetKey) => {
      for (const item of items) {
        if (item.key === targetKey) return item;
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    const menuItem = findMenuItem(menuItems, key);
    if (menuItem?.path) {
      navigate(menuItem.path);
      if (isMobile) {
        setMobileDrawerVisible(false);
      }
    }
  };

  // Handle main menu click for two-panel layout
  const handleMainMenuClick = (menuItem) => {
    if (menuItem.children && menuItem.children.length > 0) {
      // Show submenu panel
      setActiveSubmenu(menuItem);
      setSubmenuVisible(true);
    } else {
      // Navigate directly
      navigate(menuItem.path);
      setSubmenuVisible(false);
    }
  };

  // Handle submenu click
  const handleSubmenuClick = (submenuItem) => {
    navigate(submenuItem.path);
    setSubmenuVisible(false);
  };

  const menuItems = getMenuItems(canAccessModule, isSuperUser(), isAdmin());

  return (
    <Layout className="main-layout" style={{ minHeight: '100vh' }}>
      {/* Desktop Sidebar */}
      {!isMobile && (
        <div style={{
          width: collapsed ? '80px' : '320px',
          background: '#fff',
          borderRight: '1px solid #f0f0f0',
          transition: 'width 0.3s ease',
          overflow: 'hidden'
        }}>
          <div className="logo-container" style={{
            padding: '16px',
            borderBottom: '1px solid #f0f0f0',
            textAlign: 'center'
          }}>
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              {collapsed ? 'ERP' : 'DataiCraft ERP'}
            </Title>
          </div>

          <div style={{
            padding: '8px 0',
            height: 'calc(100vh - 120px)',
            overflowY: 'auto',
            overflowX: 'hidden'
          }}>
            {menuItems.map((item) => (
              <div key={item.key}>
                <div
                  style={{
                    padding: collapsed ? '12px 8px' : '12px 16px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    fontWeight: '600',
                    borderBottom: '1px solid #f5f5f5',
                    background: '#fafafa',
                    justifyContent: collapsed ? 'center' : 'flex-start'
                  }}
                  onClick={() => item.path && navigate(item.path)}
                  title={collapsed ? item.label : ''}
                >
                  <span style={{ marginRight: collapsed ? '0' : '12px' }}>{item.icon}</span>
                  {!collapsed && item.label}
                </div>
                {item.children && !collapsed && (
                  <div style={{ background: '#fff' }}>
                    {item.children.map((child) => (
                      <div
                        key={child.key}
                        style={{
                          padding: '10px 16px',
                          paddingLeft: '48px',
                          cursor: 'pointer',
                          borderBottom: '1px solid #f9f9f9',
                          transition: 'background 0.3s',
                          minHeight: '40px',
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          boxSizing: 'border-box',
                          fontSize: '14px',
                          color: '#666'
                        }}
                        onClick={() => navigate(child.path)}
                        onMouseEnter={(e) => {
                          e.target.style.background = '#f0f8ff';
                          e.target.style.color = '#1890ff';
                          e.target.style.paddingLeft = '52px';
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.background = '#fff';
                          e.target.style.color = '#666';
                          e.target.style.paddingLeft = '48px';
                        }}
                      >
                        {child.label}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Mobile Drawer */}
      <Drawer
        title="DataiCraft ERP"
        placement="left"
        onClose={() => setMobileDrawerVisible(false)}
        open={mobileDrawerVisible}
        styles={{ body: { padding: 0 } }}
        width={280}
      >
        <Menu
          mode="vertical"
          selectedKeys={getCurrentMenuKey()}
          items={menuItems}
          onClick={handleMenuClick}
          className="main-menu"
          triggerSubMenuAction="hover"
          expandIcon={null}
        />
      </Drawer>

      <Layout className="site-layout">
        {/* Header */}
        <Header className="layout-header">
          <div className="header-left">
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => isMobile ? setMobileDrawerVisible(true) : setCollapsed(!collapsed)}
              className="trigger"
              title={isMobile ? 'Open Menu' : (collapsed ? 'Expand Sidebar' : 'Collapse Sidebar')}
            />

            <CompanySelector />
          </div>

          <div className="header-center">
            <GlobalSearch />
          </div>

          <div className="header-right">
            <Space size="middle">
              <Tooltip title={darkMode ? 'Light Mode' : 'Dark Mode'}>
                <Switch
                  checkedChildren={<MoonOutlined />}
                  unCheckedChildren={<SunOutlined />}
                  checked={darkMode}
                  onChange={setDarkMode}
                />
              </Tooltip>

              <Tooltip title={fullscreen ? 'Exit Fullscreen' : 'Fullscreen'}>
                <Button
                  type="text"
                  icon={fullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                  onClick={toggleFullscreen}
                />
              </Tooltip>

              <NotificationCenter />

              <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
                <Space className="user-info" style={{ cursor: 'pointer' }}>
                  <Avatar icon={<UserOutlined />} src={user?.avatar} />
                  <div className="user-details">
                    <Text strong>{user?.name}</Text>
                    <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>
                      {isSuperUser() ? 'Super User' : isAdmin() ? 'Administrator' : user?.groups?.[0]}
                    </Text>
                  </div>
                </Space>
              </Dropdown>
            </Space>
          </div>
        </Header>

        {/* Content */}
        <Content className="layout-content">
          {children}
        </Content>

        {/* Footer */}
        <Footer className="layout-footer">
          <div className="footer-content">
            <Text type="secondary">
              © 2025 DataiCraft Solutions - Professional ERP System
            </Text>
            <Space>
              <Text type="secondary">Version 1.0.0</Text>
              <Divider type="vertical" />
              <Text type="secondary">
                User: {user?.name} ({isSuperUser() ? 'Super User' : isAdmin() ? 'Admin' : 'User'})
              </Text>
              <Divider type="vertical" />
              <Text type="secondary">Company: {currentCompany?.name}</Text>
            </Space>
          </div>
        </Footer>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
