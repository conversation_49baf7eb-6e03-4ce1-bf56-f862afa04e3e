/**
 * Main Layout Component - Following Odoo's Layout Structure
 * Professional ERP Interface with Modern UX/UI
 */
import React, { useState, useEffect } from 'react';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Badge,
  Button,
  Space,
  Typography,
  Drawer,
  Switch,
  Tooltip,
  <PERSON><PERSON>crumb,
  Divider
} from 'antd';
import {
  MenuOutlined,
  UserOutlined,
  SettingOutlined,
  ToolOutlined,
  LogoutOutlined,
  BellOutlined,
  SearchOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SunOutlined,
  MoonOutlined,
  GlobalOutlined,
  DashboardOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  ShopOutlined,
  InboxOutlined,
  TeamOutlined,
  PhoneOutlined,
  ProjectOutlined,
  Bar<PERSON>hartOutlined,
  ToolOutlined,
  FileTextOutlined,
  CreditCardOutlined,
  WalletOutlined,
  ShoppingOutlined,
  BankOutlined,
  SwapOutlined,
  HomeOutlined,
  EditOutlined,
  ApartmentOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  AccountBookOutlined,
  PercentageOutlined,
  BookOutlined,
  CodeOutlined,
  AppstoreOutlined,
  RightOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../auth/AuthProvider';
import CompanySelector from './CompanySelector';
import NotificationCenter from './NotificationCenter';
import GlobalSearch from './GlobalSearch';
import UserProfile from '../profile/UserProfile';
import UserPreferences from '../profile/UserPreferences';
import './MainLayout.css';

const { Header, Sider, Content, Footer } = Layout;
const { Text, Title } = Typography;

// QuickBooks-style modular navigation structure
const getModuleApps = (canAccessModule, isSuperUser, isAdmin) => [
  {
    key: 'dashboard',
    icon: <DashboardOutlined />,
    label: 'Dashboard',
    path: '/dashboard',
    color: '#722ed1'
  },
  {
    key: 'accounting',
    icon: <DollarOutlined />,
    label: 'Accounting',
    path: '/accounting',
    disabled: !canAccessModule('accounting'),
    color: '#1890ff'
  },
  {
    key: 'sales',
    icon: <ShoppingCartOutlined />,
    label: 'Sales',
    path: '/sales',
    disabled: !canAccessModule('sales'),
    color: '#52c41a'
  },
  {
    key: 'purchase',
    icon: <ShopOutlined />,
    label: 'Purchase',
    path: '/purchase',
    disabled: !canAccessModule('purchase'),
    color: '#fa8c16'
  },
  {
    key: 'inventory',
    icon: <InboxOutlined />,
    label: 'Inventory',
    path: '/inventory',
    disabled: !canAccessModule('inventory'),
    color: '#13c2c2'
  },
  {
    key: 'hr',
    icon: <TeamOutlined />,
    label: 'HR',
    path: '/hr',
    disabled: !canAccessModule('hr'),
    color: '#eb2f96'
  },
  {
    key: 'crm',
    icon: <PhoneOutlined />,
    label: 'CRM',
    path: '/crm',
    disabled: !canAccessModule('crm'),
    color: '#f5222d'
  },
  {
    key: 'project',
    icon: <ProjectOutlined />,
    label: 'Projects',
    path: '/project',
    disabled: !canAccessModule('project'),
    color: '#faad14'
  },
  {
    key: 'manufacturing',
    icon: <ToolOutlined />,
    label: 'Manufacturing',
    path: '/manufacturing',
    disabled: !canAccessModule('manufacturing'),
    color: '#a0d911'
  },
  {
    key: 'analytics',
    icon: <BarChartOutlined />,
    label: 'Analytics',
    path: '/analytics',
    disabled: !canAccessModule('analytics'),
    color: '#2f54eb'
  },
  // Quick Access Apps
  {
    key: 'invoicing',
    icon: <CreditCardOutlined />,
    label: 'Invoicing',
    path: '/invoicing',
    disabled: !canAccessModule('accounting'),
    color: '#1890ff'
  },
  {
    key: 'expenses',
    icon: <WalletOutlined />,
    label: 'Expenses',
    path: '/expenses',
    disabled: !canAccessModule('hr'),
    color: '#eb2f96'
  },
  {
    key: 'pos',
    icon: <ShoppingOutlined />,
    label: 'POS',
    path: '/pos',
    disabled: !canAccessModule('pos'),
    color: '#52c41a'
  },
  // Admin Apps
  ...(isSuperUser || isAdmin ? [{
    key: 'setup',
    icon: <ToolOutlined />,
    label: 'Setup',
    path: '/setup',
    color: '#8c8c8c'
  }] : []),
  ...(isSuperUser || isAdmin ? [{
    key: 'settings',
    icon: <SettingOutlined />,
    label: 'Settings',
    path: '/settings',
    color: '#595959'
  }] : [])
];

// Get contextual navigation for current module
const getModuleNavigation = (currentModule, canAccessModule, isSuperUser, isAdmin) => {
  const navigationMap = {
    accounting: [
      { key: 'accounting-dashboard', label: 'Dashboard', path: '/accounting', icon: <DashboardOutlined /> },
      { key: 'customers', label: 'Customers', path: '/accounting/customers', icon: <UserOutlined /> },
      { key: 'vendors', label: 'Vendors', path: '/accounting/vendors', icon: <ShopOutlined /> },
      { key: 'invoices', label: 'Invoices', path: '/accounting/invoices', icon: <FileTextOutlined /> },
      { key: 'bills', label: 'Bills', path: '/accounting/bills', icon: <CreditCardOutlined /> },
      { key: 'payments', label: 'Payments', path: '/accounting/payments', icon: <WalletOutlined /> },
      { key: 'reconciliation', label: 'Reconciliation', path: '/accounting/reconciliation', icon: <BankOutlined /> },
      { key: 'reports', label: 'Reports', path: '/accounting/reports', icon: <BarChartOutlined /> }
    ],
    sales: [
      { key: 'sales-dashboard', label: 'Dashboard', path: '/sales', icon: <DashboardOutlined /> },
      { key: 'quotations', label: 'Quotations', path: '/sales/quotations', icon: <FileTextOutlined /> },
      { key: 'orders', label: 'Orders', path: '/sales/orders', icon: <ShoppingCartOutlined /> },
      { key: 'customers-sales', label: 'Customers', path: '/sales/customers', icon: <UserOutlined /> },
      { key: 'products-sales', label: 'Products', path: '/sales/products', icon: <InboxOutlined /> },
      { key: 'sales-reports', label: 'Reports', path: '/sales/reports', icon: <BarChartOutlined /> }
    ],
    purchase: [
      { key: 'purchase-dashboard', label: 'Dashboard', path: '/purchase', icon: <DashboardOutlined /> },
      { key: 'rfq', label: 'RFQ', path: '/purchase/rfq', icon: <FileTextOutlined /> },
      { key: 'purchase-orders', label: 'Orders', path: '/purchase/orders', icon: <ShopOutlined /> },
      { key: 'vendors-purchase', label: 'Vendors', path: '/purchase/vendors', icon: <UserOutlined /> },
      { key: 'products-purchase', label: 'Products', path: '/purchase/products', icon: <InboxOutlined /> },
      { key: 'purchase-reports', label: 'Reports', path: '/purchase/reports', icon: <BarChartOutlined /> }
    ],
    inventory: [
      { key: 'inventory-dashboard', label: 'Dashboard', path: '/inventory', icon: <DashboardOutlined /> },
      { key: 'products-inventory', label: 'Products', path: '/inventory/products', icon: <InboxOutlined /> },
      { key: 'stock-moves', label: 'Stock Moves', path: '/inventory/moves', icon: <SwapOutlined /> },
      { key: 'adjustments', label: 'Adjustments', path: '/inventory/adjustments', icon: <EditOutlined /> },
      { key: 'warehouses', label: 'Warehouses', path: '/inventory/warehouses', icon: <HomeOutlined /> },
      { key: 'inventory-reports', label: 'Reports', path: '/inventory/reports', icon: <BarChartOutlined /> }
    ],
    hr: [
      { key: 'hr-dashboard', label: 'Dashboard', path: '/hr', icon: <DashboardOutlined /> },
      { key: 'employees', label: 'Employees', path: '/hr/employees', icon: <TeamOutlined /> },
      { key: 'departments', label: 'Departments', path: '/hr/departments', icon: <ApartmentOutlined /> },
      { key: 'attendance', label: 'Attendance', path: '/hr/attendance', icon: <ClockCircleOutlined /> },
      { key: 'leaves', label: 'Time Off', path: '/hr/leaves', icon: <CalendarOutlined /> },
      { key: 'hr-reports', label: 'Reports', path: '/hr/reports', icon: <BarChartOutlined /> }
    ],
    setup: isSuperUser || isAdmin ? [
      { key: 'company-setup', label: 'Company', path: '/setup/company', icon: <GlobalOutlined /> },
      { key: 'chart-of-accounts', label: 'Chart of Accounts', path: '/setup/chart-of-accounts', icon: <AccountBookOutlined /> },
      { key: 'fiscal-year', label: 'Fiscal Year', path: '/setup/fiscal-year', icon: <CalendarOutlined /> },
      { key: 'taxes', label: 'Taxes', path: '/setup/taxes', icon: <PercentageOutlined /> },
      { key: 'currencies', label: 'Currencies', path: '/setup/currencies', icon: <DollarOutlined /> },
      { key: 'payment-terms', label: 'Payment Terms', path: '/setup/payment-terms', icon: <CreditCardOutlined /> },
      { key: 'journals', label: 'Journals', path: '/setup/journals', icon: <BookOutlined /> },
      { key: 'bank-accounts', label: 'Bank Accounts', path: '/setup/bank-accounts', icon: <BankOutlined /> }
    ] : [],
    settings: isSuperUser || isAdmin ? [
      { key: 'general-settings', label: 'General', path: '/settings/general', icon: <SettingOutlined /> },
      { key: 'users', label: 'Users & Companies', path: '/settings/users', icon: <UserOutlined /> },
      { key: 'technical', label: 'Technical', path: '/settings/technical', icon: <CodeOutlined /> }
    ] : []
  };

  return navigationMap[currentModule] || [];
};

const MainLayout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [hoveredSection, setHoveredSection] = useState(null);
  const [submenuPosition, setSubmenuPosition] = useState({ top: 0, left: 250 });
  const [showProfile, setShowProfile] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, isSuperUser, isAdmin, canAccessModule, currentCompany } = useAuth();

  // Get current module from path
  const getCurrentModule = () => {
    const path = location.pathname;
    if (path.startsWith('/accounting')) return 'accounting';
    if (path.startsWith('/sales')) return 'sales';
    if (path.startsWith('/purchase')) return 'purchase';
    if (path.startsWith('/inventory')) return 'inventory';
    if (path.startsWith('/hr')) return 'hr';
    if (path.startsWith('/crm')) return 'crm';
    if (path.startsWith('/project')) return 'project';
    if (path.startsWith('/manufacturing')) return 'manufacturing';
    if (path.startsWith('/analytics')) return 'analytics';
    if (path.startsWith('/invoicing')) return 'invoicing';
    if (path.startsWith('/expenses')) return 'expenses';
    if (path.startsWith('/pos')) return 'pos';
    if (path.startsWith('/setup')) return 'setup';
    if (path.startsWith('/settings')) return 'settings';
    return 'dashboard';
  };

  const currentModule = getCurrentModule();
  const moduleApps = getModuleApps(canAccessModule, isSuperUser(), isAdmin());
  const currentModuleNav = getModuleNavigation(currentModule, canAccessModule, isSuperUser(), isAdmin());

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle fullscreen
  const toggleFullscreen = () => {
    if (!fullscreen) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
    setFullscreen(!fullscreen);
  };

  // Handle user menu clicks
  const handleUserMenuClick = ({ key }) => {
    switch (key) {
      case 'profile':
        setShowProfile(true);
        break;
      case 'preferences':
        setShowPreferences(true);
        break;
      case 'logout':
        logout();
        break;
      default:
        break;
    }
  };

  // User dropdown menu
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'My Profile'
    },
    {
      key: 'preferences',
      icon: <SettingOutlined />,
      label: 'Preferences'
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout'
    }
  ];

  // Handle app switching
  const handleAppSwitch = (appKey) => {
    const app = moduleApps.find(a => a.key === appKey);
    if (app && !app.disabled) {
      navigate(app.path);
      setShowAppSwitcher(false);
      if (isMobile) {
        setMobileDrawerVisible(false);
      }
    }
  };

  // Handle navigation click
  const handleNavClick = (navItem) => {
    navigate(navItem.path);
    if (isMobile) {
      setMobileDrawerVisible(false);
    }
  };

  // Get current app info
  const getCurrentApp = () => {
    return moduleApps.find(app => app.key === currentModule) || moduleApps[0];
  };

  const currentApp = getCurrentApp();

  return (
    <Layout className="main-layout" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'row' }}>
      {/* QuickBooks-style Navigation with Right-side Submenus */}
      {!isMobile && (
        <div style={{ position: 'relative' }}>
          <div style={{
            width: '250px',
            background: '#2c3e50',
            color: '#fff',
            display: 'flex',
            flexDirection: 'column',
            flexShrink: 0,
            height: '100vh',
            overflowY: 'auto'
          }}>
            {/* Logo/Brand */}
            <div style={{
              padding: '20px 16px',
              borderBottom: '1px solid #34495e',
              textAlign: 'center'
            }}>
              <Title level={4} style={{ margin: 0, color: '#fff' }}>
                DataiCraft ERP
              </Title>
            </div>

            {/* Navigation Menu */}
            <div style={{ flex: 1, padding: '16px 0' }}>
              {/* Dashboard */}
              <div
                style={{
                  padding: '12px 20px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  background: currentModule === 'dashboard' ? '#3498db' : 'transparent',
                  color: '#fff'
                }}
                onClick={() => navigate('/dashboard')}
              >
                <DashboardOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
                <span>Dashboard</span>
              </div>

              {/* Sales Section */}
              <div
                style={{
                  padding: '12px 20px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  background: currentModule === 'sales' ? '#3498db' : (hoveredSection === 'sales' ? '#34495e' : 'transparent'),
                  color: '#fff',
                  position: 'relative'
                }}
                onMouseEnter={(e) => {
                  setHoveredSection('sales');
                  const rect = e.currentTarget.getBoundingClientRect();
                  setSubmenuPosition({ top: rect.top, left: 250 });
                }}
                onMouseLeave={() => setHoveredSection(null)}
                onClick={() => navigate('/sales')}
              >
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <ShoppingCartOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
                  <span>Sales</span>
                </div>
                <RightOutlined style={{ fontSize: '12px' }} />
              </div>

              {/* Accounting Section */}
              <div
                style={{
                  padding: '12px 20px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  background: currentModule === 'accounting' ? '#3498db' : (hoveredSection === 'accounting' ? '#34495e' : 'transparent'),
                  color: '#fff'
                }}
                onMouseEnter={(e) => {
                  setHoveredSection('accounting');
                  const rect = e.currentTarget.getBoundingClientRect();
                  setSubmenuPosition({ top: rect.top, left: 250 });
                }}
                onMouseLeave={() => setHoveredSection(null)}
                onClick={() => navigate('/accounting')}
              >
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <DollarOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
                  <span>Accounting</span>
                </div>
                <RightOutlined style={{ fontSize: '12px' }} />
              </div>

              {/* Purchase Section */}
              <div
                style={{
                  padding: '12px 20px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  background: currentModule === 'purchase' ? '#3498db' : (hoveredSection === 'purchase' ? '#34495e' : 'transparent'),
                  color: '#fff'
                }}
                onMouseEnter={(e) => {
                  setHoveredSection('purchase');
                  const rect = e.currentTarget.getBoundingClientRect();
                  setSubmenuPosition({ top: rect.top, left: 250 });
                }}
                onMouseLeave={() => setHoveredSection(null)}
                onClick={() => navigate('/purchase')}
              >
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <ShopOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
                  <span>Purchase</span>
                </div>
                <RightOutlined style={{ fontSize: '12px' }} />
              </div>

              {/* Inventory Section */}
              <div
                style={{
                  padding: '12px 20px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  background: currentModule === 'inventory' ? '#3498db' : (hoveredSection === 'inventory' ? '#34495e' : 'transparent'),
                  color: '#fff'
                }}
                onMouseEnter={(e) => {
                  setHoveredSection('inventory');
                  const rect = e.currentTarget.getBoundingClientRect();
                  setSubmenuPosition({ top: rect.top, left: 250 });
                }}
                onMouseLeave={() => setHoveredSection(null)}
                onClick={() => navigate('/inventory')}
              >
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <InboxOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
                  <span>Inventory</span>
                </div>
                <RightOutlined style={{ fontSize: '12px' }} />
              </div>

              {/* HR Section */}
              <div
                style={{
                  padding: '12px 20px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  background: currentModule === 'hr' ? '#3498db' : (hoveredSection === 'hr' ? '#34495e' : 'transparent'),
                  color: '#fff'
                }}
                onMouseEnter={(e) => {
                  setHoveredSection('hr');
                  const rect = e.currentTarget.getBoundingClientRect();
                  setSubmenuPosition({ top: rect.top, left: 250 });
                }}
                onMouseLeave={() => setHoveredSection(null)}
                onClick={() => navigate('/hr')}
              >
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <TeamOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
                  <span>HR</span>
                </div>
                <RightOutlined style={{ fontSize: '12px' }} />
              </div>

              {/* Settings (Admin only) */}
              {(isSuperUser() || isAdmin()) && (
                <div
                  style={{
                    padding: '12px 20px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    background: currentModule === 'settings' ? '#3498db' : (hoveredSection === 'settings' ? '#34495e' : 'transparent'),
                    color: '#fff'
                  }}
                  onMouseEnter={(e) => {
                    setHoveredSection('settings');
                    const rect = e.currentTarget.getBoundingClientRect();
                    setSubmenuPosition({ top: rect.top, left: 250 });
                  }}
                  onMouseLeave={() => setHoveredSection(null)}
                  onClick={() => navigate('/settings')}
                >
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <SettingOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
                    <span>Settings</span>
                  </div>
                  <RightOutlined style={{ fontSize: '12px' }} />
                </div>
              )}

              {/* Setup Section (Admin only) */}
              {(isSuperUser() || isAdmin()) && (
                <div
                  style={{
                    padding: '12px 20px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    background: currentModule === 'setup' ? '#3498db' : (hoveredSection === 'setup' ? '#34495e' : 'transparent'),
                    color: '#fff'
                  }}
                  onMouseEnter={(e) => {
                    setHoveredSection('setup');
                    const rect = e.currentTarget.getBoundingClientRect();
                    setSubmenuPosition({ top: rect.top, left: 250 });
                  }}
                  onMouseLeave={() => setHoveredSection(null)}
                  onClick={() => navigate('/setup')}
                >
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <ToolOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
                    <span>Setup</span>
                  </div>
                  <RightOutlined style={{ fontSize: '12px' }} />
                </div>
              )}
            </div>
          </div>

          {/* Right-side Submenu */}
          {hoveredSection && (
            <div
              style={{
                position: 'fixed',
                top: submenuPosition.top,
                left: submenuPosition.left,
                background: '#fff',
                border: '1px solid #ddd',
                borderRadius: '4px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                zIndex: 1000,
                minWidth: '200px',
                color: '#333'
              }}
              onMouseEnter={() => setHoveredSection(hoveredSection)}
              onMouseLeave={() => setHoveredSection(null)}
            >
              {hoveredSection === 'sales' && (
                <div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/sales/quotations')}>Quotations</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/sales/orders')}>Orders</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/sales/customers')}>Customers</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/sales/products')}>Products</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer' }} onClick={() => navigate('/sales/reports')}>Reports</div>
                </div>
              )}
              {hoveredSection === 'accounting' && (
                <div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/accounting/customers')}>Customers</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/accounting/vendors')}>Vendors</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/accounting/invoices')}>Invoices</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/accounting/bills')}>Bills</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/accounting/payments')}>Payments</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/accounting/reconciliation')}>Bank Reconciliation</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer' }} onClick={() => navigate('/accounting/reports')}>Reports</div>
                </div>
              )}
              {hoveredSection === 'purchase' && (
                <div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/purchase/rfq')}>Requests for Quotation</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/purchase/orders')}>Purchase Orders</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/purchase/vendors')}>Vendors</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/purchase/products')}>Products</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer' }} onClick={() => navigate('/purchase/reports')}>Reports</div>
                </div>
              )}
              {hoveredSection === 'inventory' && (
                <div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/inventory/products')}>Products</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/inventory/moves')}>Stock Moves</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/inventory/adjustments')}>Inventory Adjustments</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/inventory/warehouses')}>Warehouses</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer' }} onClick={() => navigate('/inventory/reports')}>Reports</div>
                </div>
              )}
              {hoveredSection === 'hr' && (
                <div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/hr/employees')}>Employees</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/hr/departments')}>Departments</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/hr/attendance')}>Attendance</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/hr/leaves')}>Time Off</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer' }} onClick={() => navigate('/hr/reports')}>Reports</div>
                </div>
              )}
              {hoveredSection === 'settings' && (
                <div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/settings/general')}>General Settings</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/settings/users')}>Users & Companies</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer' }} onClick={() => navigate('/settings/technical')}>Technical</div>
                </div>
              )}
              {hoveredSection === 'setup' && (
                <div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/setup/company')}>Company Setup</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/setup/taxes/income')}>Income Tax</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/setup/taxes/sales')}>Sales Tax</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/setup/chart-of-accounts')}>Chart of Accounts</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/setup/fiscal-year')}>Fiscal Year</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/setup/currencies')}>Currencies</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/setup/payment-terms')}>Payment Terms</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/setup/journals')}>Journals</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0' }} onClick={() => navigate('/setup/bank-accounts')}>Bank Accounts</div>
                  <div style={{ padding: '12px 16px', cursor: 'pointer' }} onClick={() => navigate('/setup/warehouses')}>Warehouses</div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Mobile Drawer */}
      <Drawer
        title="DataiCraft ERP"
        placement="left"
        onClose={() => setMobileDrawerVisible(false)}
        open={mobileDrawerVisible}
        styles={{ body: { padding: 0, background: '#2c3e50' } }}
        width={250}
      >
        <div style={{ color: '#fff' }}>
          {/* Dashboard */}
          <div
            style={{
              padding: '12px 20px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              background: currentModule === 'dashboard' ? '#3498db' : 'transparent'
            }}
            onClick={() => { navigate('/dashboard'); setMobileDrawerVisible(false); }}
          >
            <DashboardOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
            <span>Dashboard</span>
          </div>

          {/* Sales */}
          <div
            style={{
              padding: '12px 20px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              background: currentModule === 'sales' ? '#3498db' : 'transparent'
            }}
            onClick={() => { navigate('/sales'); setMobileDrawerVisible(false); }}
          >
            <ShoppingCartOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
            <span>Sales</span>
          </div>

          {/* Accounting */}
          <div
            style={{
              padding: '12px 20px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              background: currentModule === 'accounting' ? '#3498db' : 'transparent'
            }}
            onClick={() => { navigate('/accounting'); setMobileDrawerVisible(false); }}
          >
            <DollarOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
            <span>Accounting</span>
          </div>

          {/* Purchase */}
          <div
            style={{
              padding: '12px 20px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              background: currentModule === 'purchase' ? '#3498db' : 'transparent'
            }}
            onClick={() => { navigate('/purchase'); setMobileDrawerVisible(false); }}
          >
            <ShopOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
            <span>Purchase</span>
          </div>

          {/* Inventory */}
          <div
            style={{
              padding: '12px 20px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              background: currentModule === 'inventory' ? '#3498db' : 'transparent'
            }}
            onClick={() => { navigate('/inventory'); setMobileDrawerVisible(false); }}
          >
            <InboxOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
            <span>Inventory</span>
          </div>

          {/* HR */}
          <div
            style={{
              padding: '12px 20px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              background: currentModule === 'hr' ? '#3498db' : 'transparent'
            }}
            onClick={() => { navigate('/hr'); setMobileDrawerVisible(false); }}
          >
            <TeamOutlined style={{ marginRight: '12px', fontSize: '16px' }} />
            <span>HR</span>
          </div>
        </div>
      </Drawer>



      <Layout className="site-layout" style={{ flex: 1, minWidth: 0 }}>
        {/* Header */}
        <Header className="layout-header">
          <div className="header-left">
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => isMobile ? setMobileDrawerVisible(true) : setCollapsed(!collapsed)}
              className="trigger"
              title={isMobile ? 'Open Menu' : (collapsed ? 'Expand Sidebar' : 'Collapse Sidebar')}
            />

            <CompanySelector />
          </div>

          <div className="header-center">
            <GlobalSearch />
          </div>

          <div className="header-right">
            <Space size="middle">
              <Tooltip title={darkMode ? 'Light Mode' : 'Dark Mode'}>
                <Switch
                  checkedChildren={<MoonOutlined />}
                  unCheckedChildren={<SunOutlined />}
                  checked={darkMode}
                  onChange={setDarkMode}
                />
              </Tooltip>

              <Tooltip title={fullscreen ? 'Exit Fullscreen' : 'Fullscreen'}>
                <Button
                  type="text"
                  icon={fullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                  onClick={toggleFullscreen}
                />
              </Tooltip>

              <NotificationCenter />

              <Dropdown menu={{ items: userMenuItems, onClick: handleUserMenuClick }} placement="bottomRight">
                <Space className="user-info" style={{ cursor: 'pointer' }}>
                  <Avatar icon={<UserOutlined />} src={user?.avatar} />
                  <div className="user-details">
                    <Text strong>{user?.name}</Text>
                    <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>
                      {isSuperUser() ? 'Super User' : isAdmin() ? 'Administrator' : user?.groups?.[0]}
                    </Text>
                  </div>
                </Space>
              </Dropdown>
            </Space>
          </div>
        </Header>

        {/* Content */}
        <Content className="layout-content">
          {children}
        </Content>

        {/* Footer */}
        <Footer className="layout-footer">
          <div className="footer-content">
            <Text type="secondary">
              © 2025 DataiCraft Solutions - Professional ERP System
            </Text>
            <Space>
              <Text type="secondary">Version 1.0.0</Text>
              <Divider type="vertical" />
              <Text type="secondary">
                User: {user?.name} ({isSuperUser() ? 'Super User' : isAdmin() ? 'Admin' : 'User'})
              </Text>
              <Divider type="vertical" />
              <Text type="secondary">Company: {currentCompany?.name}</Text>
            </Space>
          </div>
        </Footer>
      </Layout>

      {/* Profile and Preferences Modals */}
      <UserProfile
        visible={showProfile}
        onClose={() => setShowProfile(false)}
      />

      <UserPreferences
        visible={showPreferences}
        onClose={() => setShowPreferences(false)}
      />
    </Layout>
  );
};

export default MainLayout;
