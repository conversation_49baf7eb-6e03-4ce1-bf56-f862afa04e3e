import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { QueryClient, QueryClientProvider } from 'react-query';
import { AuthProvider } from './components/shared/auth/AuthProvider';
import MainLayout from './components/shared/layout/MainLayout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import ChartOfAccounts from './pages/ChartOfAccounts';
import JournalEntries from './pages/JournalEntries';
import Customers from './pages/Customers';
import Vendors from './pages/Vendors';
import Invoices from './pages/Invoices';
import Bills from './pages/Bills';
import Payments from './pages/Payments';
import Reports from './pages/Reports';

// Setup Components
import CompanySetup from './components/setup/CompanySetup';
import ChartOfAccountsSetup from './components/setup/ChartOfAccountsSetup';
import FiscalYearSetup from './components/setup/FiscalYearSetup';
import TaxesSetup from './components/setup/TaxesSetup';
import CurrenciesSetup from './components/setup/CurrenciesSetup';
import PaymentTermsSetup from './components/setup/PaymentTermsSetup';
import JournalsSetup from './components/setup/JournalsSetup';
import BankAccountsSetup from './components/setup/BankAccountsSetup';
import ProductCategoriesSetup from './components/setup/ProductCategoriesSetup';
import WarehousesSetup from './components/setup/WarehousesSetup';
import DepartmentsSetup from './components/setup/DepartmentsSetup';
import PositionsSetup from './components/setup/PositionsSetup';
import ProjectStagesSetup from './components/setup/ProjectStagesSetup';
import ManufacturingSetup from './components/setup/ManufacturingSetup';

import './App.css';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

// Ant Design theme configuration
const theme = {
  token: {
    colorPrimary: '#1890ff',
    borderRadius: 6,
  },
};

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider theme={theme}>
        <Router>
          <AuthProvider>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/*" element={
                <MainLayout>
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/chart-of-accounts" element={<ChartOfAccounts />} />
              <Route path="/journal-entries" element={<JournalEntries />} />
              <Route path="/customers" element={<Customers />} />
              <Route path="/vendors" element={<Vendors />} />
              <Route path="/invoices" element={<Invoices />} />
              <Route path="/bills" element={<Bills />} />
              <Route path="/payments" element={<Payments />} />
              <Route path="/reports" element={<Reports />} />

              {/* Setup Routes */}
              <Route path="/setup/company" element={<CompanySetup />} />
              <Route path="/setup/chart-of-accounts" element={<ChartOfAccountsSetup />} />
              <Route path="/setup/fiscal-year" element={<FiscalYearSetup />} />
              <Route path="/setup/taxes" element={<TaxesSetup />} />
              <Route path="/setup/currencies" element={<CurrenciesSetup />} />
              <Route path="/setup/payment-terms" element={<PaymentTermsSetup />} />
              <Route path="/setup/journals" element={<JournalsSetup />} />
              <Route path="/setup/bank-accounts" element={<BankAccountsSetup />} />
              <Route path="/setup/product-categories" element={<ProductCategoriesSetup />} />
              <Route path="/setup/warehouses" element={<WarehousesSetup />} />
              <Route path="/setup/departments" element={<DepartmentsSetup />} />
              <Route path="/setup/positions" element={<PositionsSetup />} />
                    <Route path="/setup/project-stages" element={<ProjectStagesSetup />} />
                    <Route path="/setup/manufacturing" element={<ManufacturingSetup />} />
                  </Routes>
                </MainLayout>
              } />
            </Routes>
          </AuthProvider>
        </Router>
      </ConfigProvider>
    </QueryClientProvider>
  );
}

export default App;
