import React from 'react';
import { Row, Col, Card, Statistic, Typography, Table, Tag } from 'antd';
import { 
  UserOutlined, 
  ShopOutlined, 
  FileTextOutlined, 
  CreditCardOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined 
} from '@ant-design/icons';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

const { Title } = Typography;

const Dashboard = () => {
  // Mock data - in real app, this would come from API
  const stats = {
    totalCustomers: 156,
    totalVendors: 89,
    totalInvoices: 1234,
    totalBills: 567,
    outstandingReceivables: 125000,
    outstandingPayables: 89000,
    monthlyRevenue: 450000,
    monthlyExpenses: 320000,
  };

  const revenueData = [
    { month: 'Jan', revenue: 400000, expenses: 280000 },
    { month: 'Feb', revenue: 420000, expenses: 290000 },
    { month: 'Mar', revenue: 380000, expenses: 270000 },
    { month: 'Apr', revenue: 450000, expenses: 320000 },
    { month: 'May', revenue: 480000, expenses: 340000 },
    { month: 'Jun', revenue: 520000, expenses: 360000 },
  ];

  const accountTypeData = [
    { name: 'Assets', value: 2500000, color: '#1890ff' },
    { name: 'Liabilities', value: 1200000, color: '#52c41a' },
    { name: 'Equity', value: 1300000, color: '#faad14' },
  ];

  const recentTransactions = [
    {
      key: '1',
      date: '2024-01-15',
      reference: 'INV-001',
      partner: 'ABC Company',
      amount: 15000,
      type: 'Invoice',
      status: 'Posted',
    },
    {
      key: '2',
      date: '2024-01-14',
      reference: 'BILL-001',
      partner: 'XYZ Supplier',
      amount: -8000,
      type: 'Bill',
      status: 'Posted',
    },
    {
      key: '3',
      date: '2024-01-13',
      reference: 'PAY-001',
      partner: 'ABC Company',
      amount: 15000,
      type: 'Payment',
      status: 'Posted',
    },
  ];

  const transactionColumns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: 'Reference',
      dataIndex: 'reference',
      key: 'reference',
    },
    {
      title: 'Partner',
      dataIndex: 'partner',
      key: 'partner',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => (
        <span className={amount > 0 ? 'balance-positive' : 'balance-negative'}>
          PKR {Math.abs(amount).toLocaleString()}
        </span>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'Posted' ? 'green' : 'orange'}>
          {status}
        </Tag>
      ),
    },
  ];

  return (
    <div>
      <div className="page-header">
        <Title level={2}>Accounting Dashboard</Title>
        <p>Overview of your financial data</p>
      </div>

      {/* Key Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Customers"
              value={stats.totalCustomers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Vendors"
              value={stats.totalVendors}
              prefix={<ShopOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Outstanding Receivables"
              value={stats.outstandingReceivables}
              prefix="PKR"
              suffix={<ArrowUpOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Outstanding Payables"
              value={stats.outstandingPayables}
              prefix="PKR"
              suffix={<ArrowDownOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Charts */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card title="Revenue vs Expenses" className="chart-container">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => `PKR ${value.toLocaleString()}`} />
                <Line type="monotone" dataKey="revenue" stroke="#1890ff" strokeWidth={2} />
                <Line type="monotone" dataKey="expenses" stroke="#ff4d4f" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="Account Types Distribution" className="chart-container">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={accountTypeData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {accountTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `PKR ${value.toLocaleString()}`} />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* Recent Transactions */}
      <Card title="Recent Transactions" className="table-container">
        <Table
          columns={transactionColumns}
          dataSource={recentTransactions}
          pagination={false}
          size="small"
        />
      </Card>
    </div>
  );
};

export default Dashboard;
