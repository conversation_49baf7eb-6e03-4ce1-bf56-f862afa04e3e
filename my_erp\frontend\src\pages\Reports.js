import React, { useState } from 'react';
import { Card, Row, Col, Typography, Button, DatePicker, Space, Table } from 'antd';
import { FileTextOutlined, BarChartOutlined, PieChartOutlined } from '@ant-design/icons';

const { Title } = Typography;
const { RangePicker } = DatePicker;

const Reports = () => {
  const [dateRange, setDateRange] = useState(null);

  // Mock trial balance data
  const trialBalanceData = [
    {
      key: '1',
      account_code: '1001',
      account_name: 'Cash in Hand',
      account_type: 'asset_cash',
      debit: 50000,
      credit: 0,
    },
    {
      key: '2',
      account_code: '1100',
      account_name: 'Accounts Receivable',
      account_type: 'asset_receivable',
      debit: 125000,
      credit: 0,
    },
    {
      key: '3',
      account_code: '2001',
      account_name: 'Accounts Payable',
      account_type: 'liability_payable',
      debit: 0,
      credit: 89000,
    },
    {
      key: '4',
      account_code: '4001',
      account_name: 'Sales Revenue',
      account_type: 'income',
      debit: 0,
      credit: 450000,
    },
  ];

  const trialBalanceColumns = [
    {
      title: 'Account Code',
      dataIndex: 'account_code',
      key: 'account_code',
      width: 120,
    },
    {
      title: 'Account Name',
      dataIndex: 'account_name',
      key: 'account_name',
    },
    {
      title: 'Debit',
      dataIndex: 'debit',
      key: 'debit',
      render: (amount) => amount > 0 ? `PKR ${amount.toLocaleString()}` : '-',
      align: 'right',
    },
    {
      title: 'Credit',
      dataIndex: 'credit',
      key: 'credit',
      render: (amount) => amount > 0 ? `PKR ${amount.toLocaleString()}` : '-',
      align: 'right',
    },
  ];

  const reportCards = [
    {
      title: 'Trial Balance',
      description: 'List of all accounts with their debit and credit balances',
      icon: <BarChartOutlined style={{ fontSize: 24, color: '#1890ff' }} />,
      action: 'Generate Trial Balance',
    },
    {
      title: 'Profit & Loss',
      description: 'Income statement showing revenue and expenses',
      icon: <PieChartOutlined style={{ fontSize: 24, color: '#52c41a' }} />,
      action: 'Generate P&L',
    },
    {
      title: 'Balance Sheet',
      description: 'Statement of financial position',
      icon: <FileTextOutlined style={{ fontSize: 24, color: '#faad14' }} />,
      action: 'Generate Balance Sheet',
    },
    {
      title: 'Cash Flow',
      description: 'Cash flow statement',
      icon: <BarChartOutlined style={{ fontSize: 24, color: '#722ed1' }} />,
      action: 'Generate Cash Flow',
    },
    {
      title: 'Aged Receivables',
      description: 'Customer outstanding analysis',
      icon: <FileTextOutlined style={{ fontSize: 24, color: '#eb2f96' }} />,
      action: 'Generate Report',
    },
    {
      title: 'Aged Payables',
      description: 'Vendor outstanding analysis',
      icon: <FileTextOutlined style={{ fontSize: 24, color: '#f5222d' }} />,
      action: 'Generate Report',
    },
  ];

  return (
    <div>
      <div className="page-header">
        <Title level={2}>Financial Reports</Title>
        <p>Generate and view financial reports</p>
      </div>

      <div style={{ marginBottom: 24 }}>
        <Space>
          <span>Report Period:</span>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            placeholder={['Start Date', 'End Date']}
          />
          <Button type="primary">Apply Filter</Button>
        </Space>
      </div>

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {reportCards.map((report, index) => (
          <Col xs={24} sm={12} lg={8} key={index}>
            <Card
              hoverable
              style={{ height: '100%' }}
              actions={[
                <Button type="link">{report.action}</Button>
              ]}
            >
              <Card.Meta
                avatar={report.icon}
                title={report.title}
                description={report.description}
              />
            </Card>
          </Col>
        ))}
      </Row>

      <Card title="Sample Trial Balance" className="table-container">
        <Table
          columns={trialBalanceColumns}
          dataSource={trialBalanceData}
          pagination={false}
          size="small"
          summary={(pageData) => {
            let totalDebit = 0;
            let totalCredit = 0;
            pageData.forEach(({ debit, credit }) => {
              totalDebit += debit;
              totalCredit += credit;
            });

            return (
              <Table.Summary.Row style={{ fontWeight: 'bold' }}>
                <Table.Summary.Cell index={0}>Total</Table.Summary.Cell>
                <Table.Summary.Cell index={1}></Table.Summary.Cell>
                <Table.Summary.Cell index={2} align="right">
                  PKR {totalDebit.toLocaleString()}
                </Table.Summary.Cell>
                <Table.Summary.Cell index={3} align="right">
                  PKR {totalCredit.toLocaleString()}
                </Table.Summary.Cell>
              </Table.Summary.Row>
            );
          }}
        />
      </Card>
    </div>
  );
};

export default Reports;
