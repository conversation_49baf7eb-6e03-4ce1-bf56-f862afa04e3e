"""
Odoo-Style User Hierarchy Implementation
Following Odoo's exact user management structure
"""

# USER HIERARCHY (from highest to lowest access):

"""
1. SYSTEM SUPER USER (is_superuser=True)
   - Complete system access across ALL companies
   - Can create/delete companies
   - Can manage all users system-wide
   - Cannot be deleted
   - Usually the person who installed the ERP
   - Example: System Administrator, IT Manager

2. COMPANY SUPER USER (company-specific super user)
   - Full access within THEIR company only
   - Can manage users within their company
   - Can access all modules within their company
   - Cannot access other companies
   - Cannot be deleted by company admins
   - Example: Company Owner, CEO

3. COMPANY ADMIN (is_staff=True within company)
   - Administrative access within their company
   - Can manage some users (not super users)
   - Access based on assigned groups/modules
   - Can be managed by company super user
   - Example: Department Manager, Admin Assistant

4. REGULAR USER (normal user with groups)
   - Access based on assigned groups/permissions
   - Limited to specific modules/features
   - Can be managed by admins and super users
   - Example: Accountant, Sales Rep, HR Officer

5. PORTAL USER (external access)
   - Limited external access (customers, vendors)
   - Can only see their own data
   - Cannot access internal modules
   - Example: Customer, Vendor, External Consultant
"""

# IMPLEMENTATION STRUCTURE:

USER_HIERARCHY = {
    'SYSTEM_SUPER_USER': {
        'level': 1,
        'django_field': 'is_superuser=True',
        'description': 'Complete system access across all companies',
        'can_manage': ['COMPANY_SUPER_USER', 'COMPANY_ADMIN', 'REGULAR_USER', 'PORTAL_USER'],
        'can_delete': False,
        'company_access': 'ALL',
        'module_access': 'ALL'
    },
    
    'COMPANY_SUPER_USER': {
        'level': 2,
        'django_field': 'profile.is_company_super_user=True',
        'description': 'Full access within specific company',
        'can_manage': ['COMPANY_ADMIN', 'REGULAR_USER', 'PORTAL_USER'],
        'can_delete': False,  # Cannot be deleted by company admins
        'company_access': 'ASSIGNED_COMPANIES',
        'module_access': 'ALL_IN_COMPANY'
    },
    
    'COMPANY_ADMIN': {
        'level': 3,
        'django_field': 'is_staff=True',
        'description': 'Administrative access within company',
        'can_manage': ['REGULAR_USER', 'PORTAL_USER'],
        'can_delete': True,
        'company_access': 'ASSIGNED_COMPANIES',
        'module_access': 'BASED_ON_GROUPS'
    },
    
    'REGULAR_USER': {
        'level': 4,
        'django_field': 'is_active=True',
        'description': 'Standard user with group-based permissions',
        'can_manage': [],
        'can_delete': True,
        'company_access': 'ASSIGNED_COMPANIES',
        'module_access': 'BASED_ON_GROUPS'
    },
    
    'PORTAL_USER': {
        'level': 5,
        'django_field': 'profile.is_portal_user=True',
        'description': 'External user with limited access',
        'can_manage': [],
        'can_delete': True,
        'company_access': 'LIMITED',
        'module_access': 'PORTAL_ONLY'
    }
}

# COMPANY-USER RELATIONSHIP:

COMPANY_USER_ROLES = {
    'OWNER': {
        'description': 'Company owner (usually company super user)',
        'permissions': ['ALL'],
        'can_be_deleted': False
    },
    
    'MANAGER': {
        'description': 'Department/module manager',
        'permissions': ['MANAGE_USERS', 'FULL_MODULE_ACCESS'],
        'can_be_deleted': True
    },
    
    'EMPLOYEE': {
        'description': 'Regular company employee',
        'permissions': ['LIMITED_ACCESS'],
        'can_be_deleted': True
    },
    
    'EXTERNAL': {
        'description': 'External user (customer, vendor)',
        'permissions': ['PORTAL_ACCESS'],
        'can_be_deleted': True
    }
}

# PERMISSION MATRIX:

PERMISSION_MATRIX = {
    'SYSTEM_SUPER_USER': {
        'companies': {
            'create': True,
            'read': True,
            'update': True,
            'delete': True,
            'switch': True
        },
        'users': {
            'create': True,
            'read': True,
            'update': True,
            'delete': True,
            'manage_all': True
        },
        'modules': {
            'access_all': True,
            'configure': True,
            'install': True
        }
    },
    
    'COMPANY_SUPER_USER': {
        'companies': {
            'create': False,
            'read': 'ASSIGNED_ONLY',
            'update': 'ASSIGNED_ONLY',
            'delete': False,
            'switch': 'ASSIGNED_ONLY'
        },
        'users': {
            'create': 'WITHIN_COMPANY',
            'read': 'WITHIN_COMPANY',
            'update': 'WITHIN_COMPANY',
            'delete': 'WITHIN_COMPANY',
            'manage_all': 'WITHIN_COMPANY'
        },
        'modules': {
            'access_all': 'WITHIN_COMPANY',
            'configure': 'WITHIN_COMPANY',
            'install': False
        }
    },
    
    'COMPANY_ADMIN': {
        'companies': {
            'create': False,
            'read': 'ASSIGNED_ONLY',
            'update': False,
            'delete': False,
            'switch': 'ASSIGNED_ONLY'
        },
        'users': {
            'create': 'LIMITED',
            'read': 'LIMITED',
            'update': 'LIMITED',
            'delete': 'LIMITED',
            'manage_all': False
        },
        'modules': {
            'access_all': False,
            'configure': 'ASSIGNED_MODULES',
            'install': False
        }
    }
}

# EXAMPLE USAGE:

"""
# System Super User (<NAME_EMAIL>)
- Can create multiple companies
- Can assign company super users to each company
- Has access to system-wide settings
- Cannot be deleted

# Company Super User (e.g., <EMAIL>)
- Full access within Company1 only
- Can manage all users in Company1
- Cannot see Company2 data
- Cannot be deleted by Company1 admins

# Company Admin (e.g., <EMAIL>)
- Administrative access in Company1
- Can manage regular users (not super users)
- Access based on assigned modules
- Can be managed by company super user

# Regular User (e.g., <EMAIL>)
- Access to Accounting module only
- Cannot manage other users
- Can only see their assigned data
- Can be managed by admins and super users
"""
