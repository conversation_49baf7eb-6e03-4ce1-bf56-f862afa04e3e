# Generated by Django 4.2.21 on 2025-07-15 20:28

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('hr', '0001_initial'),
        ('accounting', '0003_delete_ircron'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='HrExpenseSheet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Expense Report Summary', max_length=128)),
                ('accounting_date', models.DateField(blank=True, help_text='Accounting Date', null=True)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('submit', 'Submitted'), ('approve', 'Approved'), ('post', 'Posted'), ('done', 'Paid'), ('cancel', 'Refused')], default='draft', max_length=16)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, help_text='Total Amount', max_digits=16)),
                ('untaxed_amount', models.DecimalField(decimal_places=2, default=0, help_text='Untaxed Amount', max_digits=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_move_id', models.ForeignKey(blank=True, help_text='Journal Entry', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove')),
                ('bank_journal_id', models.ForeignKey(help_text='Bank Journal', on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('employee_id', models.ForeignKey(help_text='Employee', on_delete=django.db.models.deletion.CASCADE, to='hr.hremployee')),
                ('user_id', models.ForeignKey(help_text='Manager', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'hr_expense_sheet',
            },
        ),
        migrations.CreateModel(
            name='HrExpenseRefuseWizard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.TextField(help_text='Reason')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('expense_sheet_ids', models.ManyToManyField(help_text='Expense Reports', to='expenses.hrexpensesheet')),
            ],
            options={
                'db_table': 'hr_expense_refuse_wizard',
            },
        ),
        migrations.CreateModel(
            name='HrExpenseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_id', models.ForeignKey(help_text='Expense Account', on_delete=django.db.models.deletion.CASCADE, to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('product_id', models.ForeignKey(blank=True, help_text='Related Product', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.productproduct')),
            ],
            options={
                'db_table': 'hr_expense_category',
            },
        ),
        migrations.CreateModel(
            name='HrExpenseApprovalWizard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('approval_date', models.DateTimeField(default=datetime.datetime.now, help_text='Approval Date')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('expense_sheet_ids', models.ManyToManyField(help_text='Expense Reports', to='expenses.hrexpensesheet')),
                ('user_id', models.ForeignKey(help_text='Approved By', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'hr_expense_approval_wizard',
            },
        ),
        migrations.CreateModel(
            name='HrExpense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Description', max_length=128)),
                ('date', models.DateField(default=datetime.date.today, help_text='Expense Date')),
                ('unit_amount', models.DecimalField(decimal_places=2, default=0, help_text='Unit Price', max_digits=16)),
                ('quantity', models.DecimalField(decimal_places=4, default=1, help_text='Quantity', max_digits=16)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, help_text='Total', max_digits=16)),
                ('untaxed_amount', models.DecimalField(decimal_places=2, default=0, help_text='Untaxed Amount', max_digits=16)),
                ('currency_id', models.CharField(default='PKR', help_text='Currency', max_length=3)),
                ('payment_mode', models.CharField(choices=[('own_account', 'Employee (to reimburse)'), ('company_account', 'Company')], default='own_account', max_length=16)),
                ('state', models.CharField(choices=[('draft', 'To Submit'), ('reported', 'Submitted'), ('approved', 'Approved'), ('done', 'Paid'), ('refused', 'Refused')], default='draft', max_length=16)),
                ('reference', models.CharField(blank=True, help_text='Bill Reference', max_length=64)),
                ('attachment_number', models.IntegerField(default=0, help_text='Number of Attachments')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_id', models.ForeignKey(help_text='Account', on_delete=django.db.models.deletion.CASCADE, to='accounting.accountaccount')),
                ('analytic_account_id', models.ForeignKey(blank=True, help_text='Analytic Account', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountanalyticaccount')),
                ('category_id', models.ForeignKey(blank=True, help_text='Category', null=True, on_delete=django.db.models.deletion.SET_NULL, to='expenses.hrexpensecategory')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('employee_id', models.ForeignKey(help_text='Employee', on_delete=django.db.models.deletion.CASCADE, to='hr.hremployee')),
                ('product_id', models.ForeignKey(help_text='Product', on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
                ('sheet_id', models.ForeignKey(blank=True, help_text='Expense Report', null=True, on_delete=django.db.models.deletion.CASCADE, to='expenses.hrexpensesheet')),
                ('tax_ids', models.ManyToManyField(blank=True, help_text='Taxes', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'hr_expense',
            },
        ),
    ]
