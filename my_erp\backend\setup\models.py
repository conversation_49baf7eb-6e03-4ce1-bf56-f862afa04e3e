"""
Setup Models - Company Configuration and Setup Management
Following Odoo's setup structure with comprehensive configuration options
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator, EmailValidator
import uuid

class Country(models.Model):
    """Country model for localization"""
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=3, unique=True)  # ISO country code
    phone_code = models.CharField(max_length=10, blank=True)
    currency_code = models.CharField(max_length=3, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Countries"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

class Currency(models.Model):
    """Currency model for multi-currency support"""
    name = models.Char<PERSON>ield(max_length=100)
    code = models.CharField(max_length=3, unique=True)  # ISO currency code
    symbol = models.CharField(max_length=10)
    decimal_places = models.IntegerField(default=2)
    rounding = models.DecimalField(max_digits=10, decimal_places=6, default=0.01)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Currencies"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

class Timezone(models.Model):
    """Timezone model for localization"""
    name = models.CharField(max_length=100, unique=True)
    offset = models.CharField(max_length=10)  # e.g., "+05:00"
    description = models.CharField(max_length=200, blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['offset', 'name']

    def __str__(self):
        return f"{self.name} ({self.offset})"

class CompanySetup(models.Model):
    """Main company setup model following Odoo's company structure"""

    # Basic Information
    name = models.CharField(max_length=200)
    legal_name = models.CharField(max_length=200, blank=True)
    email = models.EmailField(validators=[EmailValidator()])
    phone = models.CharField(max_length=20, blank=True)
    website = models.URLField(blank=True)

    # Address Information
    street = models.TextField(blank=True)
    street2 = models.CharField(max_length=200, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    zip = models.CharField(max_length=20, blank=True)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)

    # Localization
    currency = models.ForeignKey(Currency, on_delete=models.SET_NULL, null=True, blank=True)
    timezone = models.ForeignKey(Timezone, on_delete=models.SET_NULL, null=True, blank=True)
    language = models.CharField(max_length=10, default='en_US')
    date_format = models.CharField(max_length=20, default='DD/MM/YYYY')
    time_format = models.CharField(max_length=10, default='24')

    # Number Formatting
    decimal_precision = models.IntegerField(default=2)
    thousands_separator = models.CharField(max_length=1, default=',')
    decimal_separator = models.CharField(max_length=1, default='.')

    # Fiscal Settings
    fiscal_year_start = models.DateField(null=True, blank=True)
    fiscal_year_end = models.DateField(null=True, blank=True)

    # Company Logo and Branding
    logo = models.ImageField(upload_to='company_logos/', blank=True, null=True)
    favicon = models.ImageField(upload_to='company_favicons/', blank=True, null=True)

    # Tax Settings
    tax_calculation_rounding_method = models.CharField(
        max_length=20,
        choices=[
            ('round_per_line', 'Round per Line'),
            ('round_globally', 'Round Globally'),
        ],
        default='round_per_line'
    )

    # Setup Progress Tracking
    setup_completed = models.BooleanField(default=False)
    setup_progress = models.IntegerField(default=0)  # Percentage
    setup_step = models.CharField(max_length=50, default='company')

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Company Setup"
        verbose_name_plural = "Company Setups"

    def __str__(self):
        return self.name

    def calculate_setup_progress(self):
        """Calculate setup completion percentage"""
        required_fields = [
            'name', 'email', 'phone', 'street', 'city', 'country',
            'currency', 'timezone', 'fiscal_year_start'
        ]

        completed_fields = 0
        for field in required_fields:
            value = getattr(self, field)
            if value:
                completed_fields += 1

        progress = int((completed_fields / len(required_fields)) * 100)
        self.setup_progress = progress

        if progress == 100:
            self.setup_completed = True

        return progress

    def save(self, *args, **kwargs):
        # Auto-calculate progress on save
        self.calculate_setup_progress()
        super().save(*args, **kwargs)

class BankAccount(models.Model):
    """Bank account setup for company"""
    company = models.ForeignKey(CompanySetup, on_delete=models.CASCADE, related_name='bank_accounts')
    bank_name = models.CharField(max_length=200)
    account_number = models.CharField(max_length=50)
    account_holder_name = models.CharField(max_length=200)
    iban = models.CharField(max_length=34, blank=True)
    swift_code = models.CharField(max_length=11, blank=True)
    branch_name = models.CharField(max_length=200, blank=True)
    branch_code = models.CharField(max_length=20, blank=True)
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['company', 'account_number']

    def __str__(self):
        return f"{self.bank_name} - {self.account_number}"

class TaxConfiguration(models.Model):
    """Tax configuration for company setup"""
    company = models.ForeignKey(CompanySetup, on_delete=models.CASCADE, related_name='tax_configurations')
    name = models.CharField(max_length=100)
    rate = models.DecimalField(max_digits=5, decimal_places=2)
    tax_type = models.CharField(
        max_length=20,
        choices=[
            ('sales', 'Sales Tax'),
            ('purchase', 'Purchase Tax'),
            ('both', 'Both'),
        ],
        default='both'
    )
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['company', 'name']

    def __str__(self):
        return f"{self.name} ({self.rate}%)"


class IncomeTaxConfiguration(models.Model):
    """Income tax configuration for company setup"""
    company = models.ForeignKey(CompanySetup, on_delete=models.CASCADE, related_name='income_tax_configurations')
    name = models.CharField(max_length=100, help_text="Income tax name (e.g., Corporate Income Tax)")
    rate = models.DecimalField(max_digits=5, decimal_places=2, help_text="Tax rate percentage")

    # Income tax specific fields
    tax_year_start = models.DateField(help_text="Tax year start date")
    tax_year_end = models.DateField(help_text="Tax year end date")

    # Tax brackets for progressive taxation
    min_income = models.DecimalField(max_digits=15, decimal_places=2, default=0, help_text="Minimum income for this bracket")
    max_income = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True, help_text="Maximum income for this bracket (null for unlimited)")

    # Tax calculation method
    calculation_method = models.CharField(
        max_length=20,
        choices=[
            ('flat', 'Flat Rate'),
            ('progressive', 'Progressive'),
            ('slab', 'Slab System'),
        ],
        default='flat'
    )

    # Deductions and exemptions
    standard_deduction = models.DecimalField(max_digits=15, decimal_places=2, default=0, help_text="Standard deduction amount")
    personal_exemption = models.DecimalField(max_digits=15, decimal_places=2, default=0, help_text="Personal exemption amount")

    # Payment and filing details
    advance_tax_required = models.BooleanField(default=False, help_text="Whether advance tax payments are required")
    quarterly_filing = models.BooleanField(default=False, help_text="Whether quarterly filing is required")
    annual_filing_deadline = models.DateField(null=True, blank=True, help_text="Annual tax filing deadline")

    # Status and metadata
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['company', 'name']
        ordering = ['min_income']

    def __str__(self):
        return f"{self.name} ({self.rate}%) - {self.company.name}"

    def clean(self):
        """Validate income tax configuration"""
        from django.core.exceptions import ValidationError

        if self.tax_year_start and self.tax_year_end:
            if self.tax_year_start >= self.tax_year_end:
                raise ValidationError("Tax year start must be before tax year end")

        if self.max_income and self.min_income >= self.max_income:
            raise ValidationError("Minimum income must be less than maximum income")

    def calculate_tax(self, income_amount):
        """Calculate tax for given income amount"""
        if income_amount < self.min_income:
            return 0

        if self.max_income and income_amount > self.max_income:
            taxable_income = self.max_income - self.min_income
        else:
            taxable_income = income_amount - self.min_income

        # Apply deductions
        taxable_income -= self.standard_deduction
        taxable_income -= self.personal_exemption

        if taxable_income <= 0:
            return 0

        # Calculate tax based on method
        if self.calculation_method == 'flat':
            return (taxable_income * self.rate) / 100
        elif self.calculation_method == 'progressive':
            # For progressive, this would be one bracket in a series
            return (taxable_income * self.rate) / 100
        elif self.calculation_method == 'slab':
            # Slab system - tax on entire amount if in this slab
            return (income_amount * self.rate) / 100

        return 0

class PaymentTerm(models.Model):
    """Payment terms configuration"""
    company = models.ForeignKey(CompanySetup, on_delete=models.CASCADE, related_name='payment_terms')
    name = models.CharField(max_length=100)
    days = models.IntegerField(default=0)
    description = models.TextField(blank=True)
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['company', 'name']
        ordering = ['days']

    def __str__(self):
        return f"{self.name} ({self.days} days)"

class SetupWizardStep(models.Model):
    """Track setup wizard progress"""
    company = models.ForeignKey(CompanySetup, on_delete=models.CASCADE, related_name='wizard_steps')
    step_name = models.CharField(max_length=50)
    step_order = models.IntegerField()
    is_completed = models.BooleanField(default=False)
    completed_at = models.DateTimeField(null=True, blank=True)
    data = models.TextField(default='{}', blank=True)  # Store step-specific data as JSON string
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['company', 'step_name']
        ordering = ['step_order']

    def __str__(self):
        return f"{self.company.name} - {self.step_name}"

class SystemConfiguration(models.Model):
    """System-wide configuration settings"""
    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.key}: {self.value[:50]}"

class SetupTemplate(models.Model):
    """Predefined setup templates for different business types"""
    name = models.CharField(max_length=100)
    description = models.TextField()
    business_type = models.CharField(max_length=50)
    template_data = models.TextField(default='{}')  # Store template data as JSON string
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.business_type})"
