"""
Setup Models - Company Configuration and Setup Management
Following Odoo's setup structure with comprehensive configuration options
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator, EmailValidator
import uuid

class Country(models.Model):
    """Country model for localization"""
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=3, unique=True)  # ISO country code
    phone_code = models.CharField(max_length=10, blank=True)
    currency_code = models.CharField(max_length=3, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Countries"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

class Currency(models.Model):
    """Currency model for multi-currency support"""
    name = models.Char<PERSON>ield(max_length=100)
    code = models.CharField(max_length=3, unique=True)  # ISO currency code
    symbol = models.CharField(max_length=10)
    decimal_places = models.IntegerField(default=2)
    rounding = models.DecimalField(max_digits=10, decimal_places=6, default=0.01)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Currencies"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

class Timezone(models.Model):
    """Timezone model for localization"""
    name = models.CharField(max_length=100, unique=True)
    offset = models.CharField(max_length=10)  # e.g., "+05:00"
    description = models.CharField(max_length=200, blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['offset', 'name']

    def __str__(self):
        return f"{self.name} ({self.offset})"

class CompanySetup(models.Model):
    """Main company setup model following Odoo's company structure"""

    # Basic Information
    name = models.CharField(max_length=200)
    legal_name = models.CharField(max_length=200, blank=True)
    email = models.EmailField(validators=[EmailValidator()])
    phone = models.CharField(max_length=20, blank=True)
    website = models.URLField(blank=True)

    # Address Information
    street = models.TextField(blank=True)
    street2 = models.CharField(max_length=200, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    zip = models.CharField(max_length=20, blank=True)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)

    # Localization
    currency = models.ForeignKey(Currency, on_delete=models.SET_NULL, null=True, blank=True)
    timezone = models.ForeignKey(Timezone, on_delete=models.SET_NULL, null=True, blank=True)
    language = models.CharField(max_length=10, default='en_US')
    date_format = models.CharField(max_length=20, default='DD/MM/YYYY')
    time_format = models.CharField(max_length=10, default='24')

    # Number Formatting
    decimal_precision = models.IntegerField(default=2)
    thousands_separator = models.CharField(max_length=1, default=',')
    decimal_separator = models.CharField(max_length=1, default='.')

    # Fiscal Settings
    fiscal_year_start = models.DateField(null=True, blank=True)
    fiscal_year_end = models.DateField(null=True, blank=True)

    # Company Logo and Branding
    logo = models.ImageField(upload_to='company_logos/', blank=True, null=True)
    favicon = models.ImageField(upload_to='company_favicons/', blank=True, null=True)

    # Tax Settings
    tax_calculation_rounding_method = models.CharField(
        max_length=20,
        choices=[
            ('round_per_line', 'Round per Line'),
            ('round_globally', 'Round Globally'),
        ],
        default='round_per_line'
    )

    # Setup Progress Tracking
    setup_completed = models.BooleanField(default=False)
    setup_progress = models.IntegerField(default=0)  # Percentage
    setup_step = models.CharField(max_length=50, default='company')

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Company Setup"
        verbose_name_plural = "Company Setups"

    def __str__(self):
        return self.name

    def calculate_setup_progress(self):
        """Calculate setup completion percentage"""
        required_fields = [
            'name', 'email', 'phone', 'street', 'city', 'country',
            'currency', 'timezone', 'fiscal_year_start'
        ]

        completed_fields = 0
        for field in required_fields:
            value = getattr(self, field)
            if value:
                completed_fields += 1

        progress = int((completed_fields / len(required_fields)) * 100)
        self.setup_progress = progress

        if progress == 100:
            self.setup_completed = True

        return progress

    def save(self, *args, **kwargs):
        # Auto-calculate progress on save
        self.calculate_setup_progress()
        super().save(*args, **kwargs)

class BankAccount(models.Model):
    """Bank account setup for company"""
    company = models.ForeignKey(CompanySetup, on_delete=models.CASCADE, related_name='bank_accounts')
    bank_name = models.CharField(max_length=200)
    account_number = models.CharField(max_length=50)
    account_holder_name = models.CharField(max_length=200)
    iban = models.CharField(max_length=34, blank=True)
    swift_code = models.CharField(max_length=11, blank=True)
    branch_name = models.CharField(max_length=200, blank=True)
    branch_code = models.CharField(max_length=20, blank=True)
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['company', 'account_number']

    def __str__(self):
        return f"{self.bank_name} - {self.account_number}"

class TaxConfiguration(models.Model):
    """Tax configuration for company setup"""
    company = models.ForeignKey(CompanySetup, on_delete=models.CASCADE, related_name='tax_configurations')
    name = models.CharField(max_length=100)
    rate = models.DecimalField(max_digits=5, decimal_places=2)
    tax_type = models.CharField(
        max_length=20,
        choices=[
            ('sales', 'Sales Tax'),
            ('purchase', 'Purchase Tax'),
            ('both', 'Both'),
        ],
        default='both'
    )
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['company', 'name']

    def __str__(self):
        return f"{self.name} ({self.rate}%)"


class WithholdingTaxConfiguration(models.Model):
    """Withholding tax configuration for tax deduction at source (TDS)"""
    company = models.ForeignKey(CompanySetup, on_delete=models.CASCADE, related_name='withholding_tax_configurations')
    name = models.CharField(max_length=100, help_text="Withholding tax name (e.g., Professional Services TDS)")
    code = models.CharField(max_length=20, help_text="Tax code (e.g., TDS194J)")
    rate = models.DecimalField(max_digits=5, decimal_places=2, help_text="Withholding tax rate percentage")

    # Tax type and applicability
    tax_type = models.CharField(
        max_length=30,
        choices=[
            ('professional_services', 'Professional Services'),
            ('contractor_payments', 'Contractor Payments'),
            ('rent_payments', 'Rent Payments'),
            ('commission_payments', 'Commission Payments'),
            ('interest_payments', 'Interest Payments'),
            ('salary_payments', 'Salary Payments'),
            ('dividend_payments', 'Dividend Payments'),
            ('royalty_payments', 'Royalty Payments'),
            ('other', 'Other'),
        ],
        default='professional_services'
    )

    # Threshold amounts
    threshold_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Minimum amount above which tax should be deducted"
    )

    # Exemption limit
    exemption_limit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Annual exemption limit for this tax type"
    )

    # Applicable to
    applicable_to = models.CharField(
        max_length=20,
        choices=[
            ('vendors', 'Vendors/Suppliers'),
            ('employees', 'Employees'),
            ('customers', 'Customers'),
            ('all', 'All Parties'),
        ],
        default='vendors'
    )

    # Tax calculation details
    calculation_base = models.CharField(
        max_length=20,
        choices=[
            ('gross_amount', 'Gross Amount'),
            ('net_amount', 'Net Amount'),
            ('taxable_amount', 'Taxable Amount'),
        ],
        default='gross_amount'
    )

    # Certificate and compliance
    requires_certificate = models.BooleanField(
        default=False,
        help_text="Whether TDS certificate is required"
    )

    certificate_series = models.CharField(
        max_length=10,
        blank=True,
        help_text="TDS certificate series (e.g., A, B, C)"
    )

    # Filing and compliance
    quarterly_return_required = models.BooleanField(
        default=True,
        help_text="Whether quarterly TDS return filing is required"
    )

    challan_required = models.BooleanField(
        default=True,
        help_text="Whether tax payment through challan is required"
    )

    # Due dates
    payment_due_date = models.IntegerField(
        default=7,
        help_text="Payment due date (days from month end)"
    )

    return_due_date = models.IntegerField(
        default=31,
        help_text="Return filing due date (days from quarter end)"
    )

    # Account mapping
    withholding_account = models.CharField(
        max_length=100,
        blank=True,
        help_text="Account code for withholding tax liability"
    )

    # Status and metadata
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    effective_from = models.DateField(help_text="Effective from date")
    effective_to = models.DateField(null=True, blank=True, help_text="Effective to date (null for ongoing)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['company', 'code']
        ordering = ['tax_type', 'name']

    def __str__(self):
        return f"{self.name} ({self.rate}%) - {self.code}"

    def clean(self):
        """Validate withholding tax configuration"""
        from django.core.exceptions import ValidationError

        if self.effective_from and self.effective_to:
            if self.effective_from >= self.effective_to:
                raise ValidationError("Effective from date must be before effective to date")

        if self.rate < 0 or self.rate > 100:
            raise ValidationError("Tax rate must be between 0 and 100")

    def calculate_withholding_tax(self, payment_amount, cumulative_amount=0):
        """Calculate withholding tax for given payment amount"""
        # Check if amount is above threshold
        if payment_amount < self.threshold_amount:
            return 0

        # Check exemption limit
        if cumulative_amount >= self.exemption_limit and self.exemption_limit > 0:
            return 0

        # Calculate taxable amount considering exemption
        if self.exemption_limit > 0:
            remaining_exemption = max(0, self.exemption_limit - cumulative_amount)
            taxable_amount = max(0, payment_amount - remaining_exemption)
        else:
            taxable_amount = payment_amount

        # Calculate tax based on calculation base
        if self.calculation_base == 'gross_amount':
            tax_base = taxable_amount
        elif self.calculation_base == 'net_amount':
            # Assuming net amount is gross minus some standard deductions
            tax_base = taxable_amount * 0.9  # Example: 10% standard deduction
        else:  # taxable_amount
            tax_base = taxable_amount

        # Calculate withholding tax
        withholding_tax = (tax_base * self.rate) / 100

        return round(withholding_tax, 2)

    def is_applicable_for_amount(self, amount):
        """Check if withholding tax is applicable for given amount"""
        return amount >= self.threshold_amount

    def get_next_payment_due_date(self):
        """Get next payment due date"""
        from datetime import datetime, timedelta
        from calendar import monthrange

        today = datetime.now().date()
        # Get last day of current month
        last_day = monthrange(today.year, today.month)[1]
        month_end = today.replace(day=last_day)

        # Add payment due days
        due_date = month_end + timedelta(days=self.payment_due_date)
        return due_date

class PaymentTerm(models.Model):
    """Payment terms configuration"""
    company = models.ForeignKey(CompanySetup, on_delete=models.CASCADE, related_name='payment_terms')
    name = models.CharField(max_length=100)
    days = models.IntegerField(default=0)
    description = models.TextField(blank=True)
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['company', 'name']
        ordering = ['days']

    def __str__(self):
        return f"{self.name} ({self.days} days)"

class SetupWizardStep(models.Model):
    """Track setup wizard progress"""
    company = models.ForeignKey(CompanySetup, on_delete=models.CASCADE, related_name='wizard_steps')
    step_name = models.CharField(max_length=50)
    step_order = models.IntegerField()
    is_completed = models.BooleanField(default=False)
    completed_at = models.DateTimeField(null=True, blank=True)
    data = models.TextField(default='{}', blank=True)  # Store step-specific data as JSON string
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['company', 'step_name']
        ordering = ['step_order']

    def __str__(self):
        return f"{self.company.name} - {self.step_name}"

class SystemConfiguration(models.Model):
    """System-wide configuration settings"""
    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.key}: {self.value[:50]}"

class SetupTemplate(models.Model):
    """Predefined setup templates for different business types"""
    name = models.CharField(max_length=100)
    description = models.TextField()
    business_type = models.CharField(max_length=50)
    template_data = models.TextField(default='{}')  # Store template data as JSON string
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.business_type})"
