#!/usr/bin/env python
"""
Test all accounting models to ensure they work properly
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from django.apps import apps
from django.db import connection

def test_all_models():
    print("="*60)
    print("🧪 TESTING ALL ACCOUNTING MODELS")
    print("="*60)
    
    # Get all models from accounting app
    accounting_models = list(apps.get_app_config('accounting').get_models())

    print(f"📊 Total models in accounting app: {len(accounting_models)}")
    print()
    
    # Test each model
    working_models = []
    broken_models = []
    
    for model in accounting_models:
        model_name = model.__name__
        table_name = model._meta.db_table
        
        try:
            # Test basic operations
            count = model.objects.count()
            
            # Test if we can create a simple query
            model.objects.all()[:1]
            
            working_models.append((model_name, table_name, count))
            print(f"✅ {model_name:30} | {table_name:35} | Records: {count}")
            
        except Exception as e:
            broken_models.append((model_name, table_name, str(e)))
            print(f"❌ {model_name:30} | {table_name:35} | ERROR: {str(e)[:50]}")
    
    print()
    print("="*60)
    print("📊 SUMMARY")
    print("="*60)
    print(f"✅ Working models: {len(working_models)}")
    print(f"❌ Broken models: {len(broken_models)}")
    
    if broken_models:
        print("\n🔧 BROKEN MODELS DETAILS:")
        for name, table, error in broken_models:
            print(f"❌ {name}: {error}")
    
    print()
    print("="*60)
    print("📋 MODEL CATEGORIZATION")
    print("="*60)
    
    # Categorize models
    core_accounting = []
    business_processes = []
    integration_models = []
    reporting_models = []
    system_models = []
    
    for model_name, table_name, count in working_models:
        if any(x in model_name.lower() for x in ['account', 'tax', 'fiscal', 'payment', 'journal', 'move']):
            core_accounting.append((model_name, count))
        elif any(x in model_name.lower() for x in ['sequence', 'cron', 'reconcile', 'bank', 'asset', 'budget']):
            business_processes.append((model_name, count))
        elif any(x in model_name.lower() for x in ['product', 'sale', 'purchase', 'stock']):
            integration_models.append((model_name, count))
        elif any(x in model_name.lower() for x in ['report', 'aged', 'invoice_report', 'financial']):
            reporting_models.append((model_name, count))
        else:
            system_models.append((model_name, count))
    
    print(f"💰 CORE ACCOUNTING ({len(core_accounting)} models):")
    for name, count in core_accounting:
        print(f"   ✅ {name:35} | Records: {count}")
    
    print(f"\n🔄 BUSINESS PROCESSES ({len(business_processes)} models):")
    for name, count in business_processes:
        print(f"   ✅ {name:35} | Records: {count}")
    
    print(f"\n🔗 INTEGRATION MODELS ({len(integration_models)} models):")
    for name, count in integration_models:
        print(f"   ✅ {name:35} | Records: {count}")
    
    print(f"\n📊 REPORTING MODELS ({len(reporting_models)} models):")
    for name, count in reporting_models:
        print(f"   ✅ {name:35} | Records: {count}")
    
    print(f"\n🛠️ SYSTEM MODELS ({len(system_models)} models):")
    for name, count in system_models:
        print(f"   ✅ {name:35} | Records: {count}")
    
    print()
    print("="*60)
    print("🎯 ACCOUNTING RELEVANCE ANALYSIS")
    print("="*60)
    
    # Check if models are relevant to accounting
    accounting_relevant = len(core_accounting) + len(business_processes) + len(reporting_models)
    integration_relevant = len(integration_models)  # These support accounting but aren't core
    system_relevant = len(system_models)  # These are infrastructure
    
    total_relevant = accounting_relevant + integration_relevant
    
    print(f"💰 Pure Accounting Models: {accounting_relevant}")
    print(f"🔗 Integration Support: {integration_relevant}")
    print(f"🛠️ System Infrastructure: {system_relevant}")
    print(f"📊 Total Accounting-Relevant: {total_relevant}/{len(working_models)}")
    
    relevance_percentage = (total_relevant / len(working_models)) * 100
    print(f"🎯 Accounting Relevance: {relevance_percentage:.1f}%")
    
    print()
    print("="*60)
    print("✅ CONCLUSION")
    print("="*60)
    
    if len(broken_models) == 0:
        print("🎉 ALL MODELS ARE WORKING PERFECTLY!")
    else:
        print(f"⚠️ {len(broken_models)} models need attention")
    
    if relevance_percentage >= 90:
        print("🎯 EXCELLENT: All models are highly relevant to accounting!")
    elif relevance_percentage >= 80:
        print("👍 GOOD: Most models are relevant to accounting")
    else:
        print("⚠️ Some models might not be necessary for accounting app")
    
    return working_models, broken_models

if __name__ == '__main__':
    test_all_models()
