/**
 * User Profile Component - User Profile Management
 * Following Odoo's user profile functionality
 */
import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Avatar,
  Upload,
  Button,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Card,
  Tag,
  message
} from 'antd';
import {
  UserOutlined,
  CameraOutlined,
  EditOutlined,
  SaveOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { useAuth } from '../auth/AuthProvider';

const { Title, Text } = Typography;
const { TextArea } = Input;

const UserProfile = ({ visible, onClose }) => {
  const { user, updateUser } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);

  const handleSave = async (values) => {
    setLoading(true);
    try {
      // Mock API call - replace with real API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update user data
      if (updateUser) {
        updateUser({
          ...user,
          ...values
        });
      }
      
      message.success('Profile updated successfully');
      setEditing(false);
    } catch (error) {
      message.error('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarChange = (info) => {
    if (info.file.status === 'done') {
      message.success('Avatar updated successfully');
    } else if (info.file.status === 'error') {
      message.error('Failed to upload avatar');
    }
  };

  const uploadButton = (
    <div>
      <CameraOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <UserOutlined />
          <span>My Profile</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={600}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Close
        </Button>,
        !editing && (
          <Button
            key="edit"
            type="primary"
            icon={<EditOutlined />}
            onClick={() => setEditing(true)}
          >
            Edit Profile
          </Button>
        ),
        editing && (
          <Button
            key="save"
            type="primary"
            icon={<SaveOutlined />}
            loading={loading}
            onClick={() => form.submit()}
          >
            Save Changes
          </Button>
        ),
        editing && (
          <Button
            key="cancel-edit"
            icon={<CloseOutlined />}
            onClick={() => {
              setEditing(false);
              form.resetFields();
            }}
          >
            Cancel
          </Button>
        )
      ].filter(Boolean)}
    >
      <div style={{ textAlign: 'center', marginBottom: 24 }}>
        <Upload
          name="avatar"
          listType="picture-circle"
          className="avatar-uploader"
          showUploadList={false}
          action="/api/upload/avatar"
          onChange={handleAvatarChange}
          disabled={!editing}
        >
          {user?.avatar ? (
            <Avatar size={80} src={user.avatar} />
          ) : (
            <Avatar size={80} icon={<UserOutlined />} />
          )}
          {editing && (
            <div className="upload-overlay">
              {uploadButton}
            </div>
          )}
        </Upload>
        <Title level={4} style={{ marginTop: 16, marginBottom: 8 }}>
          {user?.name}
        </Title>
        <Text type="secondary">{user?.email}</Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={user}
        onFinish={handleSave}
        disabled={!editing}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Full Name"
              name="name"
              rules={[{ required: true, message: 'Please enter your name' }]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Email"
              name="email"
              rules={[
                { required: true, message: 'Please enter your email' },
                { type: 'email', message: 'Please enter a valid email' }
              ]}
            >
              <Input />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Phone" name="phone">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Department" name="department">
              <Input disabled />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label="Bio" name="bio">
          <TextArea rows={3} placeholder="Tell us about yourself..." />
        </Form.Item>
      </Form>

      <Divider />

      <Card size="small" title="Account Information">
        <Row gutter={16}>
          <Col span={12}>
            <Text strong>User Type:</Text>
            <br />
            <Tag color="blue">{user?.userType || 'User'}</Tag>
          </Col>
          <Col span={12}>
            <Text strong>Groups:</Text>
            <br />
            <Space wrap>
              {user?.groups?.map(group => (
                <Tag key={group} color="green">{group}</Tag>
              )) || <Tag>No groups assigned</Tag>}
            </Space>
          </Col>
        </Row>
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col span={12}>
            <Text strong>Last Login:</Text>
            <br />
            <Text type="secondary">{user?.lastLogin || 'Never'}</Text>
          </Col>
          <Col span={12}>
            <Text strong>Account Created:</Text>
            <br />
            <Text type="secondary">{user?.createdAt || 'Unknown'}</Text>
          </Col>
        </Row>
      </Card>

      <style jsx>{`
        .avatar-uploader {
          position: relative;
        }
        .upload-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          opacity: 0;
          transition: opacity 0.3s;
        }
        .avatar-uploader:hover .upload-overlay {
          opacity: 1;
        }
      `}</style>
    </Modal>
  );
};

export default UserProfile;
