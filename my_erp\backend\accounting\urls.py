"""
URL Configuration for Accounting Module
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router and register viewsets
router = DefaultRouter()
router.register(r'companies', views.CompanyViewSet)
router.register(r'partners', views.PartnerViewSet)
router.register(r'accounts', views.AccountViewSet)
router.register(r'journals', views.JournalViewSet)

urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),
]
