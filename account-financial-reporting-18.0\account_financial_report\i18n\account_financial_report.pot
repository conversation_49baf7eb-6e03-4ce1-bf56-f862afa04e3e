# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_financial_report
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "&gt; 120 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "1 - 30 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
msgid "10"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "31 - 60 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "61 - 90 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "91 - 120 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "<b>Taxes summary</b>"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid ""
"<i class=\"fa fa-exclamation-triangle mr-3\"/>\n"
"                    Duplicate amounts may be shown because more than one analytical account may be defined in the journal items."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Intervals configuration</span>"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid "<span class=\"oe_inline\">To</span>"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_abstract_report
msgid "Abstract Report"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_financial_report_abstract_wizard
msgid "Abstract Wizard"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_abstract_report_xlsx
msgid "Abstract XLSX Account Financial Report"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model,name:account_financial_report.model_account_account
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Account"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__account_age_report_config_id
msgid "Account Age Report Config"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_code_from
msgid "Account Code From"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_code_to
msgid "Account Code To"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_group
msgid "Account Group"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Account Name"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Account at 0 filter"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
msgid "Account balance at 0 filter"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__account_ids
msgid "Accounts"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__centralize
msgid "Activate centralization"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Additional Filtering"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.action_aged_partner_report_configuration
msgid "Age Partner Report Configuration"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 120\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 120 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 30\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 30 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 60\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 60 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 90\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 90 d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_aged_partner_balance_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_aged_partner_balance_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_aged_partner_balance_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_aged_partner_balance_wizard
msgid "Aged Partner Balance"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_base
msgid "Aged Partner Balance -"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_aged_partner_balance
msgid "Aged Partner Balance Report"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_aged_partner_balance_report_wizard
msgid "Aged Partner Balance Wizard"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_aged_partner_balance_xlsx
msgid "Aged Partner Balance XLSL Report"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_aged_partner_balance_xlsx
msgid "Aged Partner Balance XLSX"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "All"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__aged_partner_balance_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__open_items_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__target_move__all
msgid "All Entries"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__aged_partner_balance_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__open_items_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__target_move__posted
msgid "All Posted Entries"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "All entries"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "All posted entries"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Amount Cur."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Amount Currency"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Amount cur."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_move_line__analytic_account_ids
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__grouped_by__analytic_account
msgid "Analytic Account"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Analytic Distribution"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Balance"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Base Amount"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Credit"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Debit"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__based_on
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Based On"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Based on"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Cancel"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "Centralize filter"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_account__centralized
msgid "Centralized"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__group_child_ids
msgid "Child Groups"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Code"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_account_financial_report_abstract_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__company_id
msgid "Company"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__compute_account_ids
msgid "Compute accounts"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "Configurations"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__create_date
msgid "Created on"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Credit"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Cumul cur."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Cumul. Bal."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur. Original"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur. Residual"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Currency"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid "Current"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Date"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__date_at
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__date_at
msgid "Date At"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_from
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Date From"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_to
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_to
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Date To"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
msgid "Date at filter"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Date from"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_range_id
msgid "Date range"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Date range filter"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Date to"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Debit"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Description"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__tax_detail
msgid "Detail Taxes"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__foreign_currency
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__foreign_currency
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__foreign_currency
msgid ""
"Display foreign currency for move lines, unless account currency is not "
"setup through chart of accounts will display initial and final balance in "
"that currency."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__hide_parent_hierarchy_level
msgid "Do not display parent levels"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Due\n"
"                        date"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid ""
"Due\n"
"                    date"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
msgid "Due date"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_to
msgid "End Date"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_to
msgid "End date"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_ending_cumul
msgid ""
"Ending\n"
"                        balance"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_aged_partner_balance_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__account_code_to
msgid "Ending account in a range"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Ending balance"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid ""
"Ending balance\n"
"                        cur."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Entries sorted by"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Entry"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Entry number"
msgstr ""

#. module: account_financial_report
#. odoo-javascript
#: code:addons/account_financial_report/static/src/xml/report.xml:0
msgid "Export"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Export PDF"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Export XLSX"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Filter accounts"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Filter analytic accounts"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__cost_center_ids
msgid "Filter cost centers"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_journal_ids
msgid "Filter journals"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__partner_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Filter partners"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__foreign_currency
msgid "Foreign Currency"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid "From Code"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "From:"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "From: %(date_from)s To: %(date_to)s"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__complete_code
msgid "Full Code"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__complete_name
msgid "Full Name"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__fy_start_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__fy_start_date
msgid "Fy Start Date"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.act_action_general_ledger_wizard_partner_relation
#: model:ir.actions.act_window,name:account_financial_report.action_general_ledger_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_general_ledger_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_general_ledger_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_general_ledger_wizard
msgid "General Ledger"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_base
msgid "General Ledger -"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_general_ledger
msgid "General Ledger Report"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_general_ledger_report_wizard
msgid "General Ledger Report Wizard"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_general_ledger_xlsx
msgid "General Ledger XLSL Report"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_general_ledger_xlsx
msgid "General Ledger XLSX"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid ""
"General Ledger can be computed only if selected company have\n"
"                        only one unaffected earnings account."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__group_option
msgid "Group entries by"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__grouped_by
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__grouped_by
msgid "Grouped By"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid ""
"Here you can set the intervals that will appear on the Aged Partner Balance."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Hide"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__hide_account_at_0
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__hide_account_at_0
msgid "Hide account ending balance at 0"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__hide_account_at_0
msgid "Hide accounts at 0"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_hierarchy_level
msgid "Hierarchy Levels to display"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__id
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__id
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__id
msgid "ID"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_account_account__centralized
msgid ""
"If flagged, no details will be displayed in the General Ledger report (the "
"webkit one only), only centralized amounts per period."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__inferior_limit
msgid "Inferior Limit"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/models/account_age_report_configuration.py:0
msgid "Inferior Limit must be greather than zero"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid ""
"Initial\n"
"                        balance cur."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid ""
"Initial\n"
"                    balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Initial balance"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__age_partner_config_id
#: model:ir.model.fields,field_description:account_financial_report.field_res_config_settings__age_partner_config_id
msgid "Intervals configuration"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__journal_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Journal"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__domain
msgid "Journal Items Domain"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_journal_ledger_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_journal_ledger_wizard_html
#: model:ir.ui.menu,name:account_financial_report.menu_journal_ledger_wizard
msgid "Journal Ledger"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_base
msgid "Journal Ledger -"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_journal_ledger
msgid "Journal Ledger Report"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_journal_ledger_report_wizard
msgid "Journal Ledger Report Wizard"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_journal_ledger_xlsx
msgid "Journal Ledger XLSX"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_journal_ledger_xlsx
msgid "Journal Ledger XLSX Report"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__journal_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Journals"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__level
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Level"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "Level %s"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__limit_hierarchy_level
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Limit hierarchy levels"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__line_ids
msgid "Line"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger.py:0
#: code:addons/account_financial_report/report/open_items.py:0
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid "Missing Partner"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_age_report_configuration_line
msgid "Model to set interval lines for Age partner balance report"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_age_report_configuration
msgid "Model to set intervals for Age partner balance report"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__move_target
msgid "Move Target"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal
msgid "Moves"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/models/account_age_report_configuration.py:0
msgid "Must complete Configuration Lines"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__name
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__name
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Name"
msgstr ""

#. module: account_financial_report
#: model:ir.model.constraint,message:account_financial_report.constraint_account_age_report_configuration_line_unique_name_config_combination
msgid "Name must be unique per report configuration"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Net"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "No"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "No group"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "No limit"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__
msgid "None"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Not Posted"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "Not due"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "OCA Aged Report Configuration"
msgstr ""

#. module: account_financial_report
#: model:ir.ui.menu,name:account_financial_report.menu_oca_reports
msgid "OCA accounting reports"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid "Older"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__only_one_unaffected_earnings_account
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__only_one_unaffected_earnings_account
msgid "Only One Unaffected Earnings Account"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_open_items_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_open_items_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_open_items_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_open_items_wizard
msgid "Open Items"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_base
msgid "Open Items -"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.act_action_open_items_wizard_partner_relation
msgid "Open Items Partner"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_open_items
msgid "Open Items Report"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_open_items_report_wizard
msgid "Open Items Report Wizard"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_open_items_xlsx
msgid "Open Items XLSX"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_open_items_xlsx
msgid "Open Items XLSX Report"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Options"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Original"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Partner"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_partner_ending_cumul
msgid ""
"Partner\n"
"                    cumul aged balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
msgid "Partner Initial balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Partner cumul aged balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_ending_cumul
msgid "Partner ending balance"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Partner initial balance"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__partners
msgid "Partners"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__payable_accounts_only
msgid "Payable Accounts Only"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_account_ending_cumul
msgid "Percents"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Period balance"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Periods"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Posted"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Rec."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__receivable_accounts_only
msgid "Receivable Accounts Only"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid ""
"Ref -\n"
"                        Label"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid ""
"Ref -\n"
"                    Label"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Ref - Label"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Residual"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Sequence"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Show"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__show_cost_center
msgid "Show Analytic Account"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__with_auto_sequence
msgid "Show Auto Sequence"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__show_move_line_details
msgid "Show Move Line Details"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__show_partner_details
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_partner_details
msgid "Show Partner Details"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__foreign_currency
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__foreign_currency
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__foreign_currency
msgid "Show foreign currency"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_hierarchy
msgid "Show hierarchy"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__sort_option
msgid "Sort entries by"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_from
msgid "Start Date"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_from
msgid "Start date"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_aged_partner_balance_report_wizard__account_code_from
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__account_code_from
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__account_code_from
msgid "Starting account in a range"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "TOTAL"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Tags"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__target_move
msgid "Target Moves"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Target moves filter"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Tax"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Tax Amount"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Credit"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Debit"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__based_on__taxgroups
msgid "Tax Groups"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
msgid "Tax Initial balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__based_on__taxtags
msgid "Tax Tags"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
msgid "Tax ending balance"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Tax initial balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Taxes"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/general_ledger_wizard.py:0
msgid ""
"The Company in the General Ledger Report Wizard and in Date Range must be "
"the same."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/trial_balance_wizard.py:0
msgid ""
"The Company in the Trial Balance Report Wizard and in Date Range must be the"
" same."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/vat_report_wizard.py:0
msgid ""
"The Company in the Vat Report Wizard and in Date Range must be the same."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/trial_balance_wizard.py:0
msgid "The hierarchy level to filter on must be greater than 0."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid ""
"There is a problem in the structure of the account groups. You may need to "
"create some child group of %s."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__domain
msgid "This domain will be used to select specific domain for Journal Items"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "To"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "To:"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_account_ending_cumul
msgid "Total"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_trial_balance_wizard
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_html
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_trial_balance_wizard
msgid "Trial Balance"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_base
msgid "Trial Balance -"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_trial_balance
msgid "Trial Balance Report"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_trial_balance_report_wizard
msgid "Trial Balance Report Wizard"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_xlsx
msgid "Trial Balance XLSX"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_trial_balance_xlsx
msgid "Trial Balance XLSX Report"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid ""
"Trial Balance can be computed only if selected company have only\n"
"                        one unaffected earnings account."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__unaffected_earnings_account
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__unaffected_earnings_account
msgid "Unaffected Earnings Account"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__hide_account_at_0
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__hide_account_at_0
msgid ""
"Use this filter to hide an account or a partner with an ending balance at 0."
" If partners are filtered, debits and credits totals will not match the "
"trial balance."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__show_hierarchy
msgid "Use when your account groups are hierarchical"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.action_vat_report_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_vat_report_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_vat_report_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_vat_report_wizard
msgid "VAT Report"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "VAT Report -"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "VAT Report Options"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_vat_report_wizard
msgid "VAT Report Wizard"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_vat_report_xlsx
msgid "VAT Report XLSX"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Vat Report"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_vat_report
msgid "Vat Report Report"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_vat_report_xlsx
msgid "Vat Report XLSX Report"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "View"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__hide_account_at_0
msgid ""
"When this option is enabled, the trial balance will not display accounts "
"that have initial balance = debit = credit = end balance = 0"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__with_account_name
msgid "With Account Name"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid "Without analytic account"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "Yes"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger.py:0
msgid "future"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "or"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_print_journal_ledger_wizard_qweb
msgid "ournal Ledger"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal
msgid "to"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 16.21%;"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 23.24%;"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 23.78%;"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 31.35%;"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 38.92%;"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 8.11%;"
msgstr ""
