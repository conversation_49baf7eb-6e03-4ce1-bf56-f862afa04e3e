)]}'
{"version": 3, "sources": ["/web/static/src/module_loader.js", "/bus/static/src/workers/websocket_worker.js", "/bus/static/src/workers/websocket_worker_script.js", "/bus/static/src/workers/websocket_worker_utils.js"], "mappings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zfA;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;ACtBA;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["/**\r\n *------------------------------------------------------------------------------\r\n * Odoo Web Boostrap Code\r\n *------------------------------------------------------------------------------\r\n */\r\n(function () {\r\n    \"use strict\";\r\n\r\n    class ModuleLoader {\r\n        /** @type {Map<string,{fn: Function, deps: string[]}>} mapping name => deps/fn */\r\n        factories = new Map();\r\n        /** @type {Set<string>} names of modules waiting to be started */\r\n        jobs = new Set();\r\n        /** @type {Set<string>} names of failed modules */\r\n        failed = new Set();\r\n\r\n        /** @type {Map<string,any>} mapping name => value */\r\n        modules = new Map();\r\n\r\n        bus = new EventTarget();\r\n\r\n        checkErrorProm = null;\r\n\r\n        /**\r\n         * @param {string} name\r\n         * @param {string[]} deps\r\n         * @param {Function} factory\r\n         */\r\n        define(name, deps, factory) {\r\n            if (typeof name !== \"string\") {\r\n                throw new Error(`Invalid name definition: ${name} (should be a string)\"`);\r\n            }\r\n            if (!(deps instanceof Array)) {\r\n                throw new Error(`Dependencies should be defined by an array: ${deps}`);\r\n            }\r\n            if (typeof factory !== \"function\") {\r\n                throw new Error(`Factory should be defined by a function ${factory}`);\r\n            }\r\n            if (!this.factories.has(name)) {\r\n                this.factories.set(name, {\r\n                    deps,\r\n                    fn: factory,\r\n                    ignoreMissingDeps: globalThis.__odooIgnoreMissingDependencies,\r\n                });\r\n                this.addJob(name);\r\n                this.checkErrorProm ||= Promise.resolve().then(() => {\r\n                    this.checkAndReportErrors();\r\n                    this.checkErrorProm = null;\r\n                });\r\n            }\r\n        }\r\n\r\n        addJob(name) {\r\n            this.jobs.add(name);\r\n            this.startModules();\r\n        }\r\n\r\n        findJob() {\r\n            for (const job of this.jobs) {\r\n                if (this.factories.get(job).deps.every((dep) => this.modules.has(dep))) {\r\n                    return job;\r\n                }\r\n            }\r\n            return null;\r\n        }\r\n\r\n        startModules() {\r\n            let job;\r\n            while ((job = this.findJob())) {\r\n                this.startModule(job);\r\n            }\r\n        }\r\n\r\n        startModule(name) {\r\n            const require = (name) => this.modules.get(name);\r\n            this.jobs.delete(name);\r\n            const factory = this.factories.get(name);\r\n            let value = null;\r\n            try {\r\n                value = factory.fn(require);\r\n            } catch (error) {\r\n                this.failed.add(name);\r\n                throw new Error(`Error while loading \"${name}\":\\n${error}`);\r\n            }\r\n            this.modules.set(name, value);\r\n            this.bus.dispatchEvent(\r\n                new CustomEvent(\"module-started\", { detail: { moduleName: name, module: value } })\r\n            );\r\n        }\r\n\r\n        findErrors() {\r\n            // cycle detection\r\n            const dependencyGraph = new Map();\r\n            for (const job of this.jobs) {\r\n                dependencyGraph.set(job, this.factories.get(job).deps);\r\n            }\r\n            function visitJobs(jobs, visited = new Set()) {\r\n                for (const job of jobs) {\r\n                    const result = visitJob(job, visited);\r\n                    if (result) {\r\n                        return result;\r\n                    }\r\n                }\r\n                return null;\r\n            }\r\n\r\n            function visitJob(job, visited) {\r\n                if (visited.has(job)) {\r\n                    const jobs = Array.from(visited).concat([job]);\r\n                    const index = jobs.indexOf(job);\r\n                    return jobs\r\n                        .slice(index)\r\n                        .map((j) => `\"${j}\"`)\r\n                        .join(\" => \");\r\n                }\r\n                const deps = dependencyGraph.get(job);\r\n                return deps ? visitJobs(deps, new Set(visited).add(job)) : null;\r\n            }\r\n\r\n            // missing dependencies\r\n            const missing = new Set();\r\n            for (const job of this.jobs) {\r\n                const factory = this.factories.get(job);\r\n                if (factory.ignoreMissingDeps) {\r\n                    continue;\r\n                }\r\n                for (const dep of factory.deps) {\r\n                    if (!this.factories.has(dep)) {\r\n                        missing.add(dep);\r\n                    }\r\n                }\r\n            }\r\n\r\n            return {\r\n                failed: [...this.failed],\r\n                cycle: visitJobs(this.jobs),\r\n                missing: [...missing],\r\n                unloaded: [...this.jobs].filter((j) => !this.factories.get(j).ignoreMissingDeps),\r\n            };\r\n        }\r\n\r\n        async checkAndReportErrors() {\r\n            const { failed, cycle, missing, unloaded } = this.findErrors();\r\n            if (!failed.length && !unloaded.length) {\r\n                return;\r\n            }\r\n            const debug = new URLSearchParams(location.search).get(\"debug\");\r\n            if (debug && debug !== \"0\") {\r\n                const style = document.createElement(\"style\");\r\n                style.textContent = `\r\n                    body::before {\r\n                        font-weight: bold;\r\n                        content: \"An error occurred while loading javascript modules, you may find more information in the devtools console\";\r\n                        position: fixed;\r\n                        left: 0;\r\n                        bottom: 0;\r\n                        z-index: 100000000000;\r\n                        background-color: #C00;\r\n                        color: #DDD;\r\n                    }\r\n                `;\r\n                document.head.appendChild(style);\r\n            }\r\n\r\n            if (failed.length) {\r\n                console.error(\"The following modules failed to load because of an error:\", failed);\r\n            }\r\n            if (missing) {\r\n                console.error(\r\n                    \"The following modules are needed by other modules but have not been defined, they may not be present in the correct asset bundle:\",\r\n                    missing\r\n                );\r\n            }\r\n            if (cycle) {\r\n                console.error(\r\n                    \"The following modules could not be loaded because they form a dependency cycle:\",\r\n                    cycle\r\n                );\r\n            }\r\n            if (unloaded) {\r\n                console.error(\r\n                    \"The following modules could not be loaded because they have unmet dependencies, this is a secondary error which is likely caused by one of the above problems:\",\r\n                    unloaded\r\n                );\r\n            }\r\n        }\r\n    }\r\n\r\n    if (!globalThis.odoo) {\r\n        globalThis.odoo = {};\r\n    }\r\n    const odoo = globalThis.odoo;\r\n    if (odoo.debug && !new URLSearchParams(location.search).has(\"debug\")) {\r\n        // remove debug mode if not explicitely set in url\r\n        odoo.debug = \"\";\r\n    }\r\n\r\n    const loader = new ModuleLoader();\r\n    odoo.define = loader.define.bind(loader);\r\n\r\n    odoo.loader = loader;\r\n})();\r\n", "/** @odoo-module **/\r\n\r\nimport { debounce, Deferred } from \"@bus/workers/websocket_worker_utils\";\r\n\r\n/**\r\n * Type of events that can be sent from the worker to its clients.\r\n *\r\n * @typedef { 'connect' | 'reconnect' | 'disconnect' | 'reconnecting' | 'notification' | 'initialized' | 'outdated'} WorkerEvent\r\n */\r\n\r\n/**\r\n * Type of action that can be sent from the client to the worker.\r\n *\r\n * @typedef {'add_channel' | 'delete_channel' | 'force_update_channels' | 'initialize_connection' | 'send' | 'leave' | 'stop' | 'start'} WorkerAction\r\n */\r\n\r\nexport const WEBSOCKET_CLOSE_CODES = Object.freeze({\r\n    CLEAN: 1000,\r\n    GOING_AWAY: 1001,\r\n    PROTOCOL_ERROR: 1002,\r\n    INCORRECT_DATA: 1003,\r\n    ABNORMAL_CLOSURE: 1006,\r\n    INCONSISTENT_DATA: 1007,\r\n    MESSAGE_VIOLATING_POLICY: 1008,\r\n    MESSAGE_TOO_BIG: 1009,\r\n    EXTENSION_NEGOTIATION_FAILED: 1010,\r\n    SERVER_ERROR: 1011,\r\n    RESTART: 1012,\r\n    TRY_LATER: 1013,\r\n    BAD_GATEWAY: 1014,\r\n    SESSION_EXPIRED: 4001,\r\n    KEEP_ALIVE_TIMEOUT: 4002,\r\n    RECONNECTING: 4003,\r\n});\r\n/** @deprecated worker version is now retrieved from `session.websocket_worker_bundle` */\r\nexport const WORKER_VERSION = \"17.0-1\";\r\nconst MAXIMUM_RECONNECT_DELAY = 60000;\r\n\r\n/**\r\n * This class regroups the logic necessary in order for the\r\n * SharedWorker/Worker to work. Indeed, Safari and some minor browsers\r\n * do not support SharedWorker. In order to solve this issue, a Worker\r\n * is used in this case. The logic is almost the same than the one used\r\n * for SharedWorker and this class implements it.\r\n */\r\nexport class WebsocketWorker {\r\n    INITIAL_RECONNECT_DELAY = 1000;\r\n    RECONNECT_JITTER = 1000;\r\n\r\n    constructor() {\r\n        // Timestamp of start of most recent bus service sender\r\n        this.newestStartTs = undefined;\r\n        this.websocketURL = \"\";\r\n        this.currentUID = null;\r\n        this.currentDB = null;\r\n        this.isWaitingForNewUID = true;\r\n        this.channelsByClient = new Map();\r\n        this.connectRetryDelay = this.INITIAL_RECONNECT_DELAY;\r\n        this.connectTimeout = null;\r\n        this.debugModeByClient = new Map();\r\n        this.isDebug = false;\r\n        this.active = true;\r\n        this.isReconnecting = false;\r\n        this.lastChannelSubscription = null;\r\n        this.firstSubscribeDeferred = new Deferred();\r\n        this.lastNotificationId = 0;\r\n        this.messageWaitQueue = [];\r\n        this._forceUpdateChannels = debounce(this._forceUpdateChannels, 300);\r\n        this._debouncedUpdateChannels = debounce(this._updateChannels, 300);\r\n        this._debouncedSendToServer = debounce(this._sendToServer, 300);\r\n\r\n        this._onWebsocketClose = this._onWebsocketClose.bind(this);\r\n        this._onWebsocketError = this._onWebsocketError.bind(this);\r\n        this._onWebsocketMessage = this._onWebsocketMessage.bind(this);\r\n        this._onWebsocketOpen = this._onWebsocketOpen.bind(this);\r\n    }\r\n\r\n    //--------------------------------------------------------------------------\r\n    // Public\r\n    //--------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Send the message to all the clients that are connected to the\r\n     * worker.\r\n     *\r\n     * @param {WorkerEvent} type Event to broadcast to connected\r\n     * clients.\r\n     * @param {Object} data\r\n     */\r\n    broadcast(type, data) {\r\n        for (const client of this.channelsByClient.keys()) {\r\n            client.postMessage({ type, data });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Register a client handled by this worker.\r\n     *\r\n     * @param {MessagePort} messagePort\r\n     */\r\n    registerClient(messagePort) {\r\n        messagePort.onmessage = (ev) => {\r\n            this._onClientMessage(messagePort, ev.data);\r\n        };\r\n        this.channelsByClient.set(messagePort, []);\r\n    }\r\n\r\n    /**\r\n     * Send message to the given client.\r\n     *\r\n     * @param {number} client\r\n     * @param {WorkerEvent} type\r\n     * @param {Object} data\r\n     */\r\n    sendToClient(client, type, data) {\r\n        client.postMessage({ type, data });\r\n    }\r\n\r\n    //--------------------------------------------------------------------------\r\n    // PRIVATE\r\n    //--------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Called when a message is posted to the worker by a client (i.e. a\r\n     * MessagePort connected to this worker).\r\n     *\r\n     * @param {MessagePort} client\r\n     * @param {Object} message\r\n     * @param {WorkerAction} [message.action]\r\n     * Action to execute.\r\n     * @param {Object|undefined} [message.data] Data required by the\r\n     * action.\r\n     */\r\n    _onClientMessage(client, { action, data }) {\r\n        switch (action) {\r\n            case \"send\": {\r\n                if (data[\"event_name\"] === \"update_presence\") {\r\n                    this._debouncedSendToServer(data);\r\n                } else {\r\n                    this._sendToServer(data);\r\n                }\r\n                return;\r\n            }\r\n            case \"start\":\r\n                return this._start();\r\n            case \"stop\":\r\n                return this._stop();\r\n            case \"leave\":\r\n                return this._unregisterClient(client);\r\n            case \"add_channel\":\r\n                return this._addChannel(client, data);\r\n            case \"delete_channel\":\r\n                return this._deleteChannel(client, data);\r\n            case \"force_update_channels\":\r\n                return this._forceUpdateChannels();\r\n            case \"initialize_connection\":\r\n                return this._initializeConnection(client, data);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Add a channel for the given client. If this channel is not yet\r\n     * known, update the subscription on the server.\r\n     *\r\n     * @param {MessagePort} client\r\n     * @param {string} channel\r\n     */\r\n    _addChannel(client, channel) {\r\n        const clientChannels = this.channelsByClient.get(client);\r\n        if (!clientChannels.includes(channel)) {\r\n            clientChannels.push(channel);\r\n            this.channelsByClient.set(client, clientChannels);\r\n            this._debouncedUpdateChannels();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a channel for the given client. If this channel is not\r\n     * used anymore, update the subscription on the server.\r\n     *\r\n     * @param {MessagePort} client\r\n     * @param {string} channel\r\n     */\r\n    _deleteChannel(client, channel) {\r\n        const clientChannels = this.channelsByClient.get(client);\r\n        if (!clientChannels) {\r\n            return;\r\n        }\r\n        const channelIndex = clientChannels.indexOf(channel);\r\n        if (channelIndex !== -1) {\r\n            clientChannels.splice(channelIndex, 1);\r\n            this._debouncedUpdateChannels();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the channels on the server side even if the channels on\r\n     * the client side are the same than the last time we subscribed.\r\n     */\r\n    _forceUpdateChannels() {\r\n        this._updateChannels({ force: true });\r\n    }\r\n\r\n    /**\r\n     * Remove the given client from this worker client list as well as\r\n     * its channels. If some of its channels are not used anymore,\r\n     * update the subscription on the server.\r\n     *\r\n     * @param {MessagePort} client\r\n     */\r\n    _unregisterClient(client) {\r\n        this.channelsByClient.delete(client);\r\n        this.debugModeByClient.delete(client);\r\n        this.isDebug = Object.values(this.debugModeByClient).some(\r\n            (debugValue) => debugValue !== \"\"\r\n        );\r\n        this._debouncedUpdateChannels();\r\n    }\r\n\r\n    /**\r\n     * Initialize a client connection to this worker.\r\n     *\r\n     * @param {Object} param0\r\n     * @param {string} [param0.db] Database name.\r\n     * @param {String} [param0.debug] Current debugging mode for the\r\n     * given client.\r\n     * @param {Number} [param0.lastNotificationId] Last notification id\r\n     * known by the client.\r\n     * @param {String} [param0.websocketURL] URL of the websocket endpoint.\r\n     * @param {Number|false|undefined} [param0.uid] Current user id\r\n     *     - Number: user is logged whether on the frontend/backend.\r\n     *     - false: user is not logged.\r\n     *     - undefined: not available (e.g. livechat support page)\r\n     * @param {Number} param0.startTs Timestamp of start of bus service sender.\r\n     */\r\n    _initializeConnection(client, { db, debug, lastNotificationId, uid, websocketURL, startTs }) {\r\n        if (this.newestStartTs && this.newestStartTs > startTs) {\r\n            this.debugModeByClient[client] = debug;\r\n            this.isDebug = Object.values(this.debugModeByClient).some(\r\n                (debugValue) => debugValue !== \"\"\r\n            );\r\n            this.sendToClient(client, \"initialized\");\r\n            return;\r\n        }\r\n        this.newestStartTs = startTs;\r\n        this.websocketURL = websocketURL;\r\n        this.lastNotificationId = lastNotificationId;\r\n        this.debugModeByClient[client] = debug;\r\n        this.isDebug = Object.values(this.debugModeByClient).some(\r\n            (debugValue) => debugValue !== \"\"\r\n        );\r\n        const isCurrentUserKnown = uid !== undefined;\r\n        if (this.isWaitingForNewUID && isCurrentUserKnown) {\r\n            this.isWaitingForNewUID = false;\r\n            this.currentUID = uid;\r\n        }\r\n        if ((this.currentUID !== uid && isCurrentUserKnown) || this.currentDB !== db) {\r\n            this.currentUID = uid;\r\n            this.currentDB = db;\r\n            if (this.websocket) {\r\n                this.websocket.close(WEBSOCKET_CLOSE_CODES.CLEAN);\r\n            }\r\n            this.channelsByClient.forEach((_, key) => this.channelsByClient.set(key, []));\r\n        }\r\n        this.sendToClient(client, \"initialized\");\r\n        if (!this.active) {\r\n            this.sendToClient(client, \"outdated\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Determine whether or not the websocket associated to this worker\r\n     * is connected.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    _isWebsocketConnected() {\r\n        return this.websocket && this.websocket.readyState === 1;\r\n    }\r\n\r\n    /**\r\n     * Determine whether or not the websocket associated to this worker\r\n     * is connecting.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    _isWebsocketConnecting() {\r\n        return this.websocket && this.websocket.readyState === 0;\r\n    }\r\n\r\n    /**\r\n     * Determine whether or not the websocket associated to this worker\r\n     * is in the closing state.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    _isWebsocketClosing() {\r\n        return this.websocket && this.websocket.readyState === 2;\r\n    }\r\n\r\n    /**\r\n     * Triggered when a connection is closed. If closure was not clean ,\r\n     * try to reconnect after indicating to the clients that the\r\n     * connection was closed.\r\n     *\r\n     * @param {CloseEvent} ev\r\n     * @param {number} code  close code indicating why the connection\r\n     * was closed.\r\n     * @param {string} reason reason indicating why the connection was\r\n     * closed.\r\n     */\r\n    _onWebsocketClose({ code, reason }) {\r\n        if (this.isDebug) {\r\n            console.debug(\r\n                `%c${new Date().toLocaleString()} - [onClose]`,\r\n                \"color: #c6e; font-weight: bold;\",\r\n                code,\r\n                reason\r\n            );\r\n        }\r\n        this.lastChannelSubscription = null;\r\n        this.firstSubscribeDeferred = new Deferred();\r\n        if (this.isReconnecting) {\r\n            // Connection was not established but the close event was\r\n            // triggered anyway. Let the onWebsocketError method handle\r\n            // this case.\r\n            return;\r\n        }\r\n        this.broadcast(\"disconnect\", { code, reason });\r\n        if (code === WEBSOCKET_CLOSE_CODES.CLEAN) {\r\n            if (reason === \"OUTDATED_VERSION\") {\r\n                console.warn(\"Worker deactivated due to an outdated version.\");\r\n                this.active = false;\r\n                this.broadcast(\"outdated\");\r\n            }\r\n            // WebSocket was closed on purpose, do not try to reconnect.\r\n            return;\r\n        }\r\n        // WebSocket was not closed cleanly, let's try to reconnect.\r\n        this.broadcast(\"reconnecting\", { closeCode: code });\r\n        this.isReconnecting = true;\r\n        if (code === WEBSOCKET_CLOSE_CODES.KEEP_ALIVE_TIMEOUT) {\r\n            // Don't wait to reconnect on keep alive timeout.\r\n            this.connectRetryDelay = 0;\r\n        }\r\n        if (code === WEBSOCKET_CLOSE_CODES.SESSION_EXPIRED) {\r\n            this.isWaitingForNewUID = true;\r\n        }\r\n        this._retryConnectionWithDelay();\r\n    }\r\n\r\n    /**\r\n     * Triggered when a connection failed or failed to established.\r\n     */\r\n    _onWebsocketError() {\r\n        if (this.isDebug) {\r\n            console.debug(\r\n                `%c${new Date().toLocaleString()} - [onError]`,\r\n                \"color: #c6e; font-weight: bold;\"\r\n            );\r\n        }\r\n        this._retryConnectionWithDelay();\r\n    }\r\n\r\n    /**\r\n     * Handle data received from the bus.\r\n     *\r\n     * @param {MessageEvent} messageEv\r\n     */\r\n    _onWebsocketMessage(messageEv) {\r\n        const notifications = JSON.parse(messageEv.data);\r\n        if (this.isDebug) {\r\n            console.debug(\r\n                `%c${new Date().toLocaleString()} - [onMessage]`,\r\n                \"color: #c6e; font-weight: bold;\",\r\n                notifications\r\n            );\r\n        }\r\n        this.lastNotificationId = notifications[notifications.length - 1].id;\r\n        this.broadcast(\"notification\", notifications);\r\n    }\r\n\r\n    /**\r\n     * Triggered on websocket open. Send message that were waiting for\r\n     * the connection to open.\r\n     */\r\n    _onWebsocketOpen() {\r\n        if (this.isDebug) {\r\n            console.debug(\r\n                `%c${new Date().toLocaleString()} - [onOpen]`,\r\n                \"color: #c6e; font-weight: bold;\"\r\n            );\r\n        }\r\n        this.broadcast(this.isReconnecting ? \"reconnect\" : \"connect\");\r\n        this._debouncedUpdateChannels();\r\n        this.connectRetryDelay = this.INITIAL_RECONNECT_DELAY;\r\n        this.connectTimeout = null;\r\n        this.isReconnecting = false;\r\n        this.firstSubscribeDeferred.then(() => {\r\n            this.messageWaitQueue.forEach((msg) => this.websocket.send(msg));\r\n            this.messageWaitQueue = [];\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Try to reconnect to the server, an exponential back off is\r\n     * applied to the reconnect attempts.\r\n     */\r\n    _retryConnectionWithDelay() {\r\n        this.connectRetryDelay =\r\n            Math.min(this.connectRetryDelay * 1.5, MAXIMUM_RECONNECT_DELAY) +\r\n            this.RECONNECT_JITTER * Math.random();\r\n        this.connectTimeout = setTimeout(this._start.bind(this), this.connectRetryDelay);\r\n    }\r\n\r\n    /**\r\n     * Send a message to the server through the websocket connection.\r\n     * If the websocket is not open, enqueue the message and send it\r\n     * upon the next reconnection.\r\n     *\r\n     * @param {{event_name: string, data: any }} message Message to send to the server.\r\n     */\r\n    _sendToServer(message) {\r\n        const payload = JSON.stringify(message);\r\n        if (!this._isWebsocketConnected()) {\r\n            if (message[\"event_name\"] === \"subscribe\") {\r\n                this.messageWaitQueue = this.messageWaitQueue.filter(\r\n                    (msg) => JSON.parse(msg).event_name !== \"subscribe\"\r\n                );\r\n                this.messageWaitQueue.unshift(payload);\r\n            } else {\r\n                this.messageWaitQueue.push(payload);\r\n            }\r\n        } else {\r\n            if (message[\"event_name\"] === \"subscribe\") {\r\n                this.websocket.send(payload);\r\n            } else {\r\n                this.firstSubscribeDeferred.then(() => this.websocket.send(payload));\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Start the worker by opening a websocket connection.\r\n     */\r\n    _start() {\r\n        if (!this.active || this._isWebsocketConnected() || this._isWebsocketConnecting()) {\r\n            return;\r\n        }\r\n        if (this.websocket) {\r\n            this.websocket.removeEventListener(\"open\", this._onWebsocketOpen);\r\n            this.websocket.removeEventListener(\"message\", this._onWebsocketMessage);\r\n            this.websocket.removeEventListener(\"error\", this._onWebsocketError);\r\n            this.websocket.removeEventListener(\"close\", this._onWebsocketClose);\r\n        }\r\n        if (this._isWebsocketClosing()) {\r\n            // close event was not triggered and will never be, broadcast the\r\n            // disconnect event for consistency sake.\r\n            this.lastChannelSubscription = null;\r\n            this.broadcast(\"disconnect\", { code: WEBSOCKET_CLOSE_CODES.ABNORMAL_CLOSURE });\r\n        }\r\n        this.websocket = new WebSocket(this.websocketURL);\r\n        this.websocket.addEventListener(\"open\", this._onWebsocketOpen);\r\n        this.websocket.addEventListener(\"error\", this._onWebsocketError);\r\n        this.websocket.addEventListener(\"message\", this._onWebsocketMessage);\r\n        this.websocket.addEventListener(\"close\", this._onWebsocketClose);\r\n    }\r\n\r\n    /**\r\n     * Stop the worker.\r\n     */\r\n    _stop() {\r\n        clearTimeout(this.connectTimeout);\r\n        this.connectRetryDelay = this.INITIAL_RECONNECT_DELAY;\r\n        this.isReconnecting = false;\r\n        this.lastChannelSubscription = null;\r\n        if (this.websocket) {\r\n            this.websocket.close();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the channel subscription on the server. Ignore if the channels\r\n     * did not change since the last subscription.\r\n     *\r\n     * @param {boolean} force Whether or not we should update the subscription\r\n     * event if the channels haven't change since last subscription.\r\n     */\r\n    _updateChannels({ force = false } = {}) {\r\n        const allTabsChannels = [\r\n            ...new Set([].concat.apply([], [...this.channelsByClient.values()])),\r\n        ].sort();\r\n        const allTabsChannelsString = JSON.stringify(allTabsChannels);\r\n        const shouldUpdateChannelSubscription =\r\n            allTabsChannelsString !== this.lastChannelSubscription;\r\n        if (force || shouldUpdateChannelSubscription) {\r\n            this.lastChannelSubscription = allTabsChannelsString;\r\n            this._sendToServer({\r\n                event_name: \"subscribe\",\r\n                data: { channels: allTabsChannels, last: this.lastNotificationId },\r\n            });\r\n            this.firstSubscribeDeferred.resolve();\r\n        }\r\n    }\r\n}\r\n", "/** @odoo-module **/\r\n/* eslint-env worker */\r\n/* eslint-disable no-restricted-globals */\r\n\r\nimport { WebsocketWorker } from \"./websocket_worker\";\r\n\r\n(function () {\r\n    const websocketWorker = new WebsocketWorker();\r\n\r\n    if (self.name.includes(\"shared\")) {\r\n        // The script is running in a shared worker: let's register every\r\n        // tab connection to the worker in order to relay notifications\r\n        // coming from the websocket.\r\n        onconnect = function (ev) {\r\n            const currentClient = ev.ports[0];\r\n            websocketWorker.registerClient(currentClient);\r\n        };\r\n    } else {\r\n        // The script is running in a simple web worker.\r\n        websocketWorker.registerClient(self);\r\n    }\r\n})();\r\n", "/** @odoo-module **/\r\n\r\n/**\r\n * Returns a function, that, as long as it continues to be invoked, will not\r\n * be triggered. The function will be called after it stops being called for\r\n * N milliseconds. If `immediate` is passed, trigger the function on the\r\n * leading edge, instead of the trailing.\r\n *\r\n * Inspired by https://davidwalsh.name/javascript-debounce-function\r\n */\r\nexport function debounce(func, wait, immediate) {\r\n    let timeout;\r\n    return function () {\r\n        const context = this;\r\n        const args = arguments;\r\n        function later() {\r\n            timeout = null;\r\n            if (!immediate) {\r\n                func.apply(context, args);\r\n            }\r\n        }\r\n        const callNow = immediate && !timeout;\r\n        clearTimeout(timeout);\r\n        timeout = setTimeout(later, wait);\r\n        if (callNow) {\r\n            func.apply(context, args);\r\n        }\r\n    };\r\n}\r\n\r\n/**\r\n * Deferred is basically a resolvable/rejectable extension of Promise.\r\n */\r\nexport class Deferred extends Promise {\r\n    constructor() {\r\n        let resolve;\r\n        let reject;\r\n        const prom = new Promise((res, rej) => {\r\n            resolve = res;\r\n            reject = rej;\r\n        });\r\n        return Object.assign(prom, { resolve, reject });\r\n    }\r\n}\r\n"], "file": "/web/assets/4897557/bus.websocket_worker_assets.js", "sourceRoot": "../../../"}