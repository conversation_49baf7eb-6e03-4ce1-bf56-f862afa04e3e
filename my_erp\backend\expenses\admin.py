"""
Expenses Module Admin Interface
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import (
    HrExpenseCategory, HrExpense, HrExpenseSheet,
    HrExpenseRefuseWizard, HrExpenseApprovalWizard
)


@admin.register(HrExpenseCategory)
class HrExpenseCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'account_id', 'product_id', 'company_id']
    list_filter = ['active', 'company_id']
    search_fields = ['name']
    ordering = ['name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'active')
        }),
        ('Account Configuration', {
            'fields': ('account_id',)
        }),
        ('Product Integration', {
            'fields': ('product_id',)
        }),
    )


class HrExpenseInline(admin.TabularInline):
    model = HrExpense
    extra = 0
    fields = ['name', 'product_id', 'unit_amount', 'quantity', 'total_amount', 'state']
    readonly_fields = ['total_amount']


@admin.register(HrExpense)
class HrExpenseAdmin(admin.ModelAdmin):
    list_display = ['name', 'employee_id', 'product_id', 'total_amount_display', 'state_display', 'payment_mode', 'date']
    list_filter = ['state', 'payment_mode', 'date', 'company_id']
    search_fields = ['name', 'employee_id__name', 'reference']
    filter_horizontal = ['tax_ids']
    ordering = ['-date', '-create_date']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'date', 'employee_id')
        }),
        ('Product and Category', {
            'fields': ('product_id', 'category_id')
        }),
        ('Amount and Currency', {
            'fields': ('unit_amount', 'quantity', 'total_amount', 'untaxed_amount', 'currency_id')
        }),
        ('Tax', {
            'fields': ('tax_ids',)
        }),
        ('Payment', {
            'fields': ('payment_mode',)
        }),
        ('State and Approval', {
            'fields': ('state',)
        }),
        ('Accounting', {
            'fields': ('account_id', 'analytic_account_id')
        }),
        ('Reference', {
            'fields': ('reference', 'attachment_number')
        }),
        ('Sheet Reference', {
            'fields': ('sheet_id',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['total_amount', 'untaxed_amount']

    def total_amount_display(self, obj):
        return format_html('<span style="font-weight: bold; color: green;">{} {}</span>',
                          obj.currency_id, f'{obj.total_amount:,.2f}')
    total_amount_display.short_description = 'Total Amount'

    def state_display(self, obj):
        colors = {
            'draft': 'gray',
            'reported': 'blue',
            'approved': 'green',
            'done': 'darkgreen',
            'refused': 'red'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.state, 'black'), obj.get_state_display()
        )
    state_display.short_description = 'State'

    actions = ['action_submit', 'action_approve', 'action_refuse', 'action_reset']

    def action_submit(self, request, queryset):
        submitted = 0
        for expense in queryset.filter(state='draft'):
            if expense.action_submit_expenses():
                submitted += 1
        self.message_user(request, f'{submitted} expenses submitted successfully.')
    action_submit.short_description = "Submit expenses"

    def action_approve(self, request, queryset):
        approved = 0
        for expense in queryset.filter(state='reported'):
            if expense.action_approve_expenses():
                approved += 1
        self.message_user(request, f'{approved} expenses approved successfully.')
    action_approve.short_description = "Approve expenses"

    def action_refuse(self, request, queryset):
        refused = 0
        for expense in queryset.filter(state__in=['reported', 'approved']):
            if expense.action_refuse_expenses():
                refused += 1
        self.message_user(request, f'{refused} expenses refused successfully.')
    action_refuse.short_description = "Refuse expenses"

    def action_reset(self, request, queryset):
        reset = 0
        for expense in queryset.filter(state__in=['refused', 'reported']):
            if expense.action_reset_to_draft():
                reset += 1
        self.message_user(request, f'{reset} expenses reset to draft successfully.')
    action_reset.short_description = "Reset to draft"


@admin.register(HrExpenseSheet)
class HrExpenseSheetAdmin(admin.ModelAdmin):
    list_display = ['name', 'employee_id', 'total_amount_display', 'state_display', 'accounting_date', 'user_id']
    list_filter = ['state', 'accounting_date', 'company_id']
    search_fields = ['name', 'employee_id__name']
    inlines = [HrExpenseInline]
    ordering = ['-create_date']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'employee_id')
        }),
        ('Dates', {
            'fields': ('accounting_date',)
        }),
        ('State', {
            'fields': ('state',)
        }),
        ('Amounts', {
            'fields': ('total_amount', 'untaxed_amount')
        }),
        ('Journal and Payment', {
            'fields': ('bank_journal_id',)
        }),
        ('Accounting Integration', {
            'fields': ('account_move_id',),
            'classes': ('collapse',)
        }),
        ('Approval', {
            'fields': ('user_id',)
        }),
    )

    readonly_fields = ['total_amount', 'untaxed_amount', 'account_move_id']

    def total_amount_display(self, obj):
        return format_html('<span style="font-weight: bold; color: green;">${:,.2f}</span>', obj.total_amount)
    total_amount_display.short_description = 'Total Amount'

    def state_display(self, obj):
        colors = {
            'draft': 'gray',
            'submit': 'blue',
            'approve': 'green',
            'post': 'darkgreen',
            'done': 'purple',
            'cancel': 'red'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.state, 'black'), obj.get_state_display()
        )
    state_display.short_description = 'State'

    actions = ['action_submit_sheet', 'action_approve_sheet', 'action_create_move', 'action_pay_sheet']

    def action_submit_sheet(self, request, queryset):
        submitted = 0
        for sheet in queryset.filter(state='draft'):
            if sheet.action_submit_sheet():
                submitted += 1
        self.message_user(request, f'{submitted} expense sheets submitted successfully.')
    action_submit_sheet.short_description = "Submit expense sheets"

    def action_approve_sheet(self, request, queryset):
        approved = 0
        for sheet in queryset.filter(state='submit'):
            if sheet.approve_expense_sheets():
                approved += 1
        self.message_user(request, f'{approved} expense sheets approved successfully.')
    action_approve_sheet.short_description = "Approve expense sheets"

    def action_create_move(self, request, queryset):
        created = 0
        for sheet in queryset.filter(state='approve'):
            if sheet.action_sheet_move_create():
                created += 1
        self.message_user(request, f'{created} accounting moves created successfully.')
    action_create_move.short_description = "Create accounting moves"

    def action_pay_sheet(self, request, queryset):
        paid = 0
        for sheet in queryset.filter(state='post'):
            if sheet.action_pay_expense_sheet():
                paid += 1
        self.message_user(request, f'{paid} expense sheets marked as paid successfully.')
    action_pay_sheet.short_description = "Mark as paid"


@admin.register(HrExpenseRefuseWizard)
class HrExpenseRefuseWizardAdmin(admin.ModelAdmin):
    list_display = ['reason_short', 'sheet_count', 'create_date']
    search_fields = ['reason']
    filter_horizontal = ['expense_sheet_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Reason', {
            'fields': ('reason',)
        }),
        ('Expense Reports', {
            'fields': ('expense_sheet_ids',)
        }),
    )

    def reason_short(self, obj):
        return obj.reason[:50] + '...' if len(obj.reason) > 50 else obj.reason
    reason_short.short_description = 'Reason'

    def sheet_count(self, obj):
        count = obj.expense_sheet_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    sheet_count.short_description = 'Sheets'

    actions = ['refuse_expenses']

    def refuse_expenses(self, request, queryset):
        refused = 0
        for wizard in queryset:
            if wizard.expense_refuse_reason():
                refused += wizard.expense_sheet_ids.count()
        self.message_user(request, f'{refused} expense sheets refused successfully.')
    refuse_expenses.short_description = "Refuse expenses with reason"


@admin.register(HrExpenseApprovalWizard)
class HrExpenseApprovalWizardAdmin(admin.ModelAdmin):
    list_display = ['user_id', 'approval_date', 'sheet_count', 'create_date']
    list_filter = ['approval_date', 'user_id']
    filter_horizontal = ['expense_sheet_ids']
    ordering = ['-create_date']

    fieldsets = (
        ('Approval Settings', {
            'fields': ('user_id', 'approval_date')
        }),
        ('Expense Reports', {
            'fields': ('expense_sheet_ids',)
        }),
    )

    def sheet_count(self, obj):
        count = obj.expense_sheet_ids.count()
        return format_html('<span style="font-weight: bold;">{}</span>', count)
    sheet_count.short_description = 'Sheets'

    actions = ['approve_sheets']

    def approve_sheets(self, request, queryset):
        approved = 0
        for wizard in queryset:
            approved += wizard.approve_expense_sheets()
        self.message_user(request, f'{approved} expense sheets approved successfully.')
    approve_sheets.short_description = "Approve expense sheets"


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - Expenses"
