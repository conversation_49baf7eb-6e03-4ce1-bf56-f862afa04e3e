/**
 * Global Search Component - Professional ERP Search
 * Following Odoo's global search functionality
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Input,
  List,
  Typography,
  Space,
  Avatar,
  Tag,
  Spin,
  Empty,
  Divider
} from 'antd';
import {
  SearchOutlined,
  UserOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  ShopOutlined,
  InboxOutlined,
  TeamOutlined,
  PhoneOutlined,
  ProjectOutlined,
  FileTextOutlined,
  CreditCardOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../auth/AuthProvider';

const { Text } = Typography;

const GlobalSearch = () => {
  const [searchValue, setSearchValue] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const searchRef = useRef(null);
  const navigate = useNavigate();
  const { canAccessModule } = useAuth();

  // Module icons mapping
  const moduleIcons = {
    accounting: <DollarOutlined />,
    sales: <ShoppingCartOutlined />,
    purchase: <ShopOutlined />,
    inventory: <InboxOutlined />,
    hr: <TeamOutlined />,
    crm: <PhoneOutlined />,
    project: <ProjectOutlined />,
    invoicing: <CreditCardOutlined />,
    expenses: <FileTextOutlined />
  };

  // Module colors mapping
  const moduleColors = {
    accounting: '#1890ff',
    sales: '#52c41a',
    purchase: '#fa8c16',
    inventory: '#722ed1',
    hr: '#eb2f96',
    crm: '#13c2c2',
    project: '#faad14',
    invoicing: '#9254de',
    expenses: '#ff7a45'
  };

  // Sample search data
  const sampleData = [
    // Customers
    { id: 1, type: 'customer', module: 'accounting', title: 'ABC Corporation', subtitle: 'Customer • $25,000 balance', path: '/accounting/customers/1' },
    { id: 2, type: 'customer', module: 'accounting', title: 'XYZ Industries', subtitle: 'Customer • $15,500 balance', path: '/accounting/customers/2' },
    
    // Vendors
    { id: 3, type: 'vendor', module: 'accounting', title: 'Office Supplies Co.', subtitle: 'Vendor • $8,200 payable', path: '/accounting/vendors/3' },
    
    // Products
    { id: 4, type: 'product', module: 'inventory', title: 'Office Chair Premium', subtitle: 'Product • 25 in stock', path: '/inventory/products/4' },
    { id: 5, type: 'product', module: 'inventory', title: 'Laptop Dell Inspiron', subtitle: 'Product • 12 in stock', path: '/inventory/products/5' },
    
    // Invoices
    { id: 6, type: 'invoice', module: 'accounting', title: 'INV/2025/001', subtitle: 'Invoice • $5,000 • ABC Corporation', path: '/accounting/invoices/6' },
    { id: 7, type: 'invoice', module: 'accounting', title: 'INV/2025/002', subtitle: 'Invoice • $3,200 • XYZ Industries', path: '/accounting/invoices/7' },
    
    // Sales Orders
    { id: 8, type: 'order', module: 'sales', title: 'SO/2025/001', subtitle: 'Sales Order • $12,000 • Confirmed', path: '/sales/orders/8' },
    
    // Purchase Orders
    { id: 9, type: 'order', module: 'purchase', title: 'PO/2025/001', subtitle: 'Purchase Order • $8,500 • Pending', path: '/purchase/orders/9' },
    
    // Employees
    { id: 10, type: 'employee', module: 'hr', title: 'John Doe', subtitle: 'Employee • Software Developer', path: '/hr/employees/10' },
    { id: 11, type: 'employee', module: 'hr', title: 'Jane Smith', subtitle: 'Employee • Project Manager', path: '/hr/employees/11' },
    
    // Projects
    { id: 12, type: 'project', module: 'project', title: 'ERP Implementation', subtitle: 'Project • 75% complete', path: '/project/projects/12' },
    
    // Leads
    { id: 13, type: 'lead', module: 'crm', title: 'Tech Solutions Inc.', subtitle: 'Lead • New • High Priority', path: '/crm/leads/13' },
    
    // Expenses
    { id: 14, type: 'expense', module: 'expenses', title: 'Travel Expense - John Doe', subtitle: 'Expense • $450 • Pending Approval', path: '/expenses/14' }
  ];

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchValue.trim()) {
        performSearch(searchValue);
      } else {
        setSearchResults([]);
        setShowResults(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchValue]);

  // Handle click outside to close results
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const performSearch = async (query) => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const filteredResults = sampleData.filter(item => {
        // Check if user can access this module
        if (!canAccessModule(item.module)) return false;
        
        // Search in title and subtitle
        const searchText = `${item.title} ${item.subtitle}`.toLowerCase();
        return searchText.includes(query.toLowerCase());
      });

      // Group results by module
      const groupedResults = filteredResults.reduce((acc, item) => {
        if (!acc[item.module]) {
          acc[item.module] = [];
        }
        acc[item.module].push(item);
        return acc;
      }, {});

      setSearchResults(groupedResults);
      setShowResults(true);
      setLoading(false);
    }, 200);
  };

  const handleResultClick = (item) => {
    navigate(item.path);
    setShowResults(false);
    setSearchValue('');
  };

  const handleInputFocus = () => {
    if (searchValue.trim() && searchResults.length > 0) {
      setShowResults(true);
    }
  };

  const getResultIcon = (type, module) => {
    const iconStyle = { 
      color: moduleColors[module] || '#8c8c8c',
      fontSize: '16px'
    };

    switch (type) {
      case 'customer':
      case 'vendor':
        return <UserOutlined style={iconStyle} />;
      case 'product':
        return <InboxOutlined style={iconStyle} />;
      case 'invoice':
        return <CreditCardOutlined style={iconStyle} />;
      case 'order':
        return module === 'sales' ? <ShoppingCartOutlined style={iconStyle} /> : <ShopOutlined style={iconStyle} />;
      case 'employee':
        return <TeamOutlined style={iconStyle} />;
      case 'project':
        return <ProjectOutlined style={iconStyle} />;
      case 'lead':
        return <PhoneOutlined style={iconStyle} />;
      case 'expense':
        return <FileTextOutlined style={iconStyle} />;
      default:
        return moduleIcons[module] || <FileTextOutlined style={iconStyle} />;
    }
  };

  const getModuleDisplayName = (module) => {
    const names = {
      accounting: 'Accounting',
      sales: 'Sales',
      purchase: 'Purchase',
      inventory: 'Inventory',
      hr: 'Human Resources',
      crm: 'CRM',
      project: 'Project',
      invoicing: 'Invoicing',
      expenses: 'Expenses'
    };
    return names[module] || module;
  };

  const totalResults = Object.values(searchResults).reduce((sum, items) => sum + items.length, 0);

  return (
    <div ref={searchRef} className="global-search" style={{ position: 'relative', width: '100%' }}>
      <Input
        placeholder="Search customers, products, invoices..."
        prefix={<SearchOutlined />}
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        onFocus={handleInputFocus}
        style={{
          borderRadius: '20px',
          background: '#f5f5f5',
          border: 'none'
        }}
        size="large"
      />

      {showResults && (
        <div
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            background: '#fff',
            border: '1px solid #f0f0f0',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            zIndex: 1000,
            maxHeight: '400px',
            overflowY: 'auto',
            marginTop: '4px'
          }}
        >
          {loading ? (
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <Spin />
            </div>
          ) : totalResults === 0 ? (
            <Empty 
              description={`No results found for "${searchValue}"`}
              style={{ padding: '20px' }}
            />
          ) : (
            <div>
              {/* Results header */}
              <div style={{ 
                padding: '8px 16px', 
                background: '#fafafa',
                borderBottom: '1px solid #f0f0f0'
              }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {totalResults} result{totalResults !== 1 ? 's' : ''} found
                </Text>
              </div>

              {/* Results by module */}
              {Object.entries(searchResults).map(([module, items], moduleIndex) => (
                <div key={module}>
                  {moduleIndex > 0 && <Divider style={{ margin: 0 }} />}
                  
                  {/* Module header */}
                  <div style={{ 
                    padding: '8px 16px', 
                    background: '#f9f9f9',
                    borderBottom: '1px solid #f5f5f5'
                  }}>
                    <Space>
                      <Avatar 
                        icon={moduleIcons[module]} 
                        size="small"
                        style={{ backgroundColor: moduleColors[module] }}
                      />
                      <Text strong style={{ fontSize: '13px' }}>
                        {getModuleDisplayName(module)}
                      </Text>
                      <Tag size="small">{items.length}</Tag>
                    </Space>
                  </div>

                  {/* Module results */}
                  <List
                    dataSource={items}
                    renderItem={(item) => (
                      <List.Item
                        style={{
                          padding: '8px 16px',
                          cursor: 'pointer',
                          borderBottom: '1px solid #f5f5f5'
                        }}
                        onClick={() => handleResultClick(item)}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = '#f0f2f5';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = '#fff';
                        }}
                      >
                        <List.Item.Meta
                          avatar={getResultIcon(item.type, item.module)}
                          title={
                            <Text style={{ fontSize: '14px' }}>
                              {item.title}
                            </Text>
                          }
                          description={
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {item.subtitle}
                            </Text>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </div>
              ))}

              {/* Footer */}
              <div style={{ 
                padding: '8px 16px', 
                background: '#fafafa',
                borderTop: '1px solid #f0f0f0',
                textAlign: 'center'
              }}>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  Press Enter to search all modules
                </Text>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default GlobalSearch;
