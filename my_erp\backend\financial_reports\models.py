"""
Financial Reports Module Models - Advanced Financial Reporting
Based on OCA account-financial-reporting modules
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime, timedelta
import json

# Import models from other modules for integration
from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountTax
)
# Import setup models for localization
from setup.models import Currency


class DateRange(models.Model):
    """Date Range - For flexible date range selection in reports"""

    TYPE_CHOICES = [
        ('month', 'Month'),
        ('quarter', 'Quarter'),
        ('year', 'Year'),
        ('custom', 'Custom'),
    ]

    name = models.CharField(max_length=64)
    type_id = models.CharField(max_length=16, choices=TYPE_CHOICES, default='month')
    date_start = models.DateField(help_text="Start Date")
    date_end = models.DateField(help_text="End Date")
    active = models.BooleanField(default=True)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'date_range'

    def __str__(self):
        return self.name


class AccountAgeReportConfiguration(models.Model):
    """Age Report Configuration - For aged partner balance reports"""

    name = models.CharField(max_length=64, default="Default Age Configuration")
    active = models.BooleanField(default=True)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_age_report_configuration'

    def __str__(self):
        return self.name


class AccountAgeReportConfigurationLine(models.Model):
    """Age Report Configuration Line - Age intervals"""

    configuration_id = models.ForeignKey(AccountAgeReportConfiguration, on_delete=models.CASCADE, related_name='line_ids')
    name = models.CharField(max_length=64, help_text="Interval Name")
    days_from = models.IntegerField(help_text="Days From")
    days_to = models.IntegerField(help_text="Days To")
    sequence = models.IntegerField(default=1)

    class Meta:
        db_table = 'account_age_report_configuration_line'
        ordering = ['sequence']

    def __str__(self):
        return f"{self.configuration_id.name} - {self.name}"


class FinancialReportAbstract(models.Model):
    """Abstract model for financial reports"""

    name = models.CharField(max_length=128, help_text="Report Name")
    date_from = models.DateField(help_text="Date From")
    date_to = models.DateField(help_text="Date To")

    # Filters
    account_ids = models.ManyToManyField(AccountAccount, blank=True, help_text="Accounts")
    partner_ids = models.ManyToManyField(ResPartner, blank=True, help_text="Partners")
    journal_ids = models.ManyToManyField(AccountJournal, blank=True, help_text="Journals")

    # Options
    only_posted_moves = models.BooleanField(default=True, help_text="Only Posted Entries")
    show_move_line_details = models.BooleanField(default=False, help_text="Show Move Line Details")
    hide_account_at_0 = models.BooleanField(default=True, help_text="Hide Accounts at 0")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True


class TrialBalanceReport(FinancialReportAbstract):
    """Trial Balance Report"""

    # Additional options for trial balance
    show_partner_details = models.BooleanField(default=False, help_text="Show Partner Details")
    limit_hierarchy_level = models.BooleanField(default=False, help_text="Limit Hierarchy Level")
    hierarchy_level = models.IntegerField(default=1, help_text="Hierarchy Level")
    show_hierarchy_level = models.IntegerField(default=1, help_text="Show Hierarchy Level")

    class Meta:
        db_table = 'trial_balance_report'

    def __str__(self):
        return f"Trial Balance - {self.name}"

    def generate_report_data(self):
        """Generate trial balance report data"""
        # This would contain the logic to generate trial balance data
        # Similar to the OCA module logic
        return {
            'report_name': 'Trial Balance',
            'date_from': self.date_from,
            'date_to': self.date_to,
            'accounts': self._get_account_data(),
        }

    def _get_account_data(self):
        """Get account data for trial balance"""
        accounts = self.account_ids.all() if self.account_ids.exists() else AccountAccount.objects.filter(company_id=self.company_id)
        account_data = []

        for account in accounts:
            # Calculate balances for each account
            balance_data = self._calculate_account_balance(account)
            if not self.hide_account_at_0 or balance_data['balance'] != 0:
                account_data.append({
                    'account': account,
                    'code': account.code,
                    'name': account.name,
                    'initial_balance': balance_data['initial_balance'],
                    'debit': balance_data['debit'],
                    'credit': balance_data['credit'],
                    'balance': balance_data['balance'],
                    'ending_balance': balance_data['ending_balance'],
                })

        return account_data

    def _calculate_account_balance(self, account):
        """Calculate account balance for the period"""
        # This would contain the actual balance calculation logic
        return {
            'initial_balance': 0.0,
            'debit': 0.0,
            'credit': 0.0,
            'balance': 0.0,
            'ending_balance': 0.0,
        }


class GeneralLedgerReport(FinancialReportAbstract):
    """General Ledger Report"""

    # Additional options for general ledger
    centralize = models.BooleanField(default=True, help_text="Centralize")
    show_analytic_tags = models.BooleanField(default=False, help_text="Show Analytic Tags")

    class Meta:
        db_table = 'general_ledger_report'

    def __str__(self):
        return f"General Ledger - {self.name}"

    def generate_report_data(self):
        """Generate general ledger report data"""
        return {
            'report_name': 'General Ledger',
            'date_from': self.date_from,
            'date_to': self.date_to,
            'accounts': self._get_general_ledger_data(),
        }

    def _get_general_ledger_data(self):
        """Get general ledger data"""
        accounts = self.account_ids.all() if self.account_ids.exists() else AccountAccount.objects.filter(company_id=self.company_id)
        ledger_data = []

        for account in accounts:
            move_lines = self._get_account_move_lines(account)
            if move_lines or not self.hide_account_at_0:
                ledger_data.append({
                    'account': account,
                    'code': account.code,
                    'name': account.name,
                    'move_lines': move_lines,
                    'total_debit': sum(line['debit'] for line in move_lines),
                    'total_credit': sum(line['credit'] for line in move_lines),
                })

        return ledger_data

    def _get_account_move_lines(self, account):
        """Get move lines for account"""
        domain = [
            ('account_id', '=', account.id),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to),
        ]

        if self.only_posted_moves:
            domain.append(('move_id__state', '=', 'posted'))

        if self.partner_ids.exists():
            domain.append(('partner_id__in', self.partner_ids.values_list('id', flat=True)))

        if self.journal_ids.exists():
            domain.append(('journal_id__in', self.journal_ids.values_list('id', flat=True)))

        move_lines = AccountMoveLine.objects.filter(*[models.Q(condition) for condition in domain])

        return [{
            'date': line.date,
            'move_name': line.move_id.name,
            'partner': line.partner_id.name if line.partner_id else '',
            'label': line.name,
            'debit': float(line.debit),
            'credit': float(line.credit),
            'balance': float(line.debit - line.credit),
        } for line in move_lines]


class AgedPartnerBalanceReport(FinancialReportAbstract):
    """Aged Partner Balance Report"""

    ACCOUNT_TYPES = [
        ('receivable', 'Receivable'),
        ('payable', 'Payable'),
    ]

    account_type = models.CharField(max_length=16, choices=ACCOUNT_TYPES, default='receivable')
    show_move_line_details = models.BooleanField(default=True, help_text="Show Move Line Details")

    class Meta:
        db_table = 'aged_partner_balance_report'

    def __str__(self):
        return f"Aged Partner Balance - {self.name}"

    def generate_report_data(self):
        """Generate aged partner balance report data"""
        return {
            'report_name': 'Aged Partner Balance',
            'account_type': self.account_type,
            'date_to': self.date_to,
            'partners': self._get_aged_partner_data(),
        }

    def _get_aged_partner_data(self):
        """Get aged partner balance data"""
        partners = self.partner_ids.all() if self.partner_ids.exists() else ResPartner.objects.filter(
            models.Q(customer_rank__gt=0) | models.Q(supplier_rank__gt=0)
        )

        aged_data = []
        for partner in partners:
            partner_data = self._calculate_partner_aging(partner)
            if partner_data['total'] != 0 or not self.hide_account_at_0:
                aged_data.append(partner_data)

        return aged_data

    def _calculate_partner_aging(self, partner):
        """Calculate aging for a partner"""
        # Get open move lines for partner
        account_type = 'asset_receivable' if self.account_type == 'receivable' else 'liability_payable'

        move_lines = AccountMoveLine.objects.filter(
            partner_id=partner,
            account_id__account_type=account_type,
            reconciled=False,
            date__lte=self.date_to,
        )

        # Calculate aging buckets
        current = 0.0
        days_30 = 0.0
        days_60 = 0.0
        days_90 = 0.0
        days_120 = 0.0
        older = 0.0

        for line in move_lines:
            days_due = (self.date_to - line.date_maturity if line.date_maturity else line.date).days
            amount = float(line.amount_residual)

            if days_due <= 0:
                current += amount
            elif days_due <= 30:
                days_30 += amount
            elif days_due <= 60:
                days_60 += amount
            elif days_due <= 90:
                days_90 += amount
            elif days_due <= 120:
                days_120 += amount
            else:
                older += amount

        total = current + days_30 + days_60 + days_90 + days_120 + older

        return {
            'partner': partner,
            'current': current,
            '30_days': days_30,
            '60_days': days_60,
            '90_days': days_90,
            '120_days': days_120,
            'older': older,
            'total': total,
        }


class OpenItemsReport(FinancialReportAbstract):
    """Open Items Report"""

    ACCOUNT_TYPES = [
        ('receivable', 'Receivable'),
        ('payable', 'Payable'),
    ]

    account_type = models.CharField(max_length=16, choices=ACCOUNT_TYPES, default='receivable')
    target_move = models.CharField(max_length=16, choices=[('posted', 'Posted'), ('all', 'All')], default='posted')

    class Meta:
        db_table = 'open_items_report'

    def __str__(self):
        return f"Open Items - {self.name}"

    def generate_report_data(self):
        """Generate open items report data"""
        return {
            'report_name': 'Open Items',
            'account_type': self.account_type,
            'date_to': self.date_to,
            'open_items': self._get_open_items_data(),
        }

    def _get_open_items_data(self):
        """Get open items data"""
        account_type = 'asset_receivable' if self.account_type == 'receivable' else 'liability_payable'

        move_lines = AccountMoveLine.objects.filter(
            account_id__account_type=account_type,
            reconciled=False,
            date__lte=self.date_to,
            company_id=self.company_id,
        )

        if self.target_move == 'posted':
            move_lines = move_lines.filter(move_id__state='posted')

        if self.partner_ids.exists():
            move_lines = move_lines.filter(partner_id__in=self.partner_ids.all())

        open_items = []
        for line in move_lines:
            if line.amount_residual != 0:
                open_items.append({
                    'date': line.date,
                    'due_date': line.date_maturity or line.date,
                    'partner': line.partner_id.name if line.partner_id else '',
                    'account': line.account_id.code + ' ' + line.account_id.name,
                    'move': line.move_id.name,
                    'reference': line.ref or '',
                    'original_amount': float(line.debit or line.credit),
                    'residual_amount': float(line.amount_residual),
                    'currency': line.currency_id.name if line.currency_id else self.company_id.currency_id,
                })

        return open_items


class JournalLedgerReport(FinancialReportAbstract):
    """Journal Ledger Report"""

    SORT_OPTIONS = [
        ('move_name', 'Entry Number'),
        ('date', 'Date'),
    ]

    sort_option = models.CharField(max_length=16, choices=SORT_OPTIONS, default='move_name')
    group_option = models.CharField(max_length=16, choices=[('journal', 'Journal'), ('none', 'None')], default='journal')

    class Meta:
        db_table = 'journal_ledger_report'

    def __str__(self):
        return f"Journal Ledger - {self.name}"

    def generate_report_data(self):
        """Generate journal ledger report data"""
        return {
            'report_name': 'Journal Ledger',
            'date_from': self.date_from,
            'date_to': self.date_to,
            'journals': self._get_journal_ledger_data(),
        }

    def _get_journal_ledger_data(self):
        """Get journal ledger data"""
        journals = self.journal_ids.all() if self.journal_ids.exists() else AccountJournal.objects.filter(company_id=self.company_id)

        journal_data = []
        for journal in journals:
            moves = self._get_journal_moves(journal)
            if moves:
                journal_data.append({
                    'journal': journal,
                    'moves': moves,
                    'total_debit': sum(move['total_debit'] for move in moves),
                    'total_credit': sum(move['total_credit'] for move in moves),
                })

        return journal_data

    def _get_journal_moves(self, journal):
        """Get moves for journal"""
        moves = AccountMove.objects.filter(
            journal_id=journal,
            date__gte=self.date_from,
            date__lte=self.date_to,
        )

        if self.only_posted_moves:
            moves = moves.filter(state='posted')

        move_data = []
        for move in moves:
            lines = move.line_ids.all()
            if self.partner_ids.exists():
                lines = lines.filter(partner_id__in=self.partner_ids.all())

            if lines.exists():
                move_data.append({
                    'move': move,
                    'lines': [{
                        'account': line.account_id.code + ' ' + line.account_id.name,
                        'partner': line.partner_id.name if line.partner_id else '',
                        'label': line.name,
                        'debit': float(line.debit),
                        'credit': float(line.credit),
                    } for line in lines],
                    'total_debit': sum(float(line.debit) for line in lines),
                    'total_credit': sum(float(line.credit) for line in lines),
                })

        return move_data


class VatReport(FinancialReportAbstract):
    """VAT Report"""

    based_on = models.CharField(max_length=16, choices=[('taxtags', 'Tax Tags'), ('taxgroups', 'Tax Groups')], default='taxtags')
    tax_detail = models.BooleanField(default=True, help_text="Show Tax Detail")

    class Meta:
        db_table = 'vat_report'

    def __str__(self):
        return f"VAT Report - {self.name}"

    def generate_report_data(self):
        """Generate VAT report data"""
        return {
            'report_name': 'VAT Report',
            'date_from': self.date_from,
            'date_to': self.date_to,
            'taxes': self._get_vat_data(),
        }

    def _get_vat_data(self):
        """Get VAT data"""
        taxes = AccountTax.objects.filter(company_id=self.company_id, active=True)

        vat_data = []
        for tax in taxes:
            tax_lines = AccountMoveLine.objects.filter(
                tax_line_id=tax,
                date__gte=self.date_from,
                date__lte=self.date_to,
            )

            if self.only_posted_moves:
                tax_lines = tax_lines.filter(move_id__state='posted')

            base_lines = AccountMoveLine.objects.filter(
                tax_ids=tax,
                date__gte=self.date_from,
                date__lte=self.date_to,
            )

            if self.only_posted_moves:
                base_lines = base_lines.filter(move_id__state='posted')

            net = sum(float(line.balance) for line in base_lines)
            tax_amount = sum(float(line.balance) for line in tax_lines)

            if net != 0 or tax_amount != 0 or not self.hide_account_at_0:
                vat_data.append({
                    'tax': tax,
                    'net': net,
                    'tax': tax_amount,
                    'total': net + tax_amount,
                })

        return vat_data
