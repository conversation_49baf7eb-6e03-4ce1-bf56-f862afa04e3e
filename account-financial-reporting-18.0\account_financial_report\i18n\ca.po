# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_financial_report
#
# Translators:
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-03-16 08:14+0000\n"
"PO-Revision-Date: 2022-06-16 11:05+0000\n"
"Last-Translator: jabe<PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Catalan (https://www.transifex.com/oca/teams/23907/ca/)\n"
"Language: ca\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "&gt; 120 d."
msgstr "&gt; 120 d."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "1 - 30 d."
msgstr "1 - 30 d."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
msgid "10"
msgstr "10"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "31 - 60 d."
msgstr "31 - 60 d."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "61 - 90 d."
msgstr "61 - 90 d."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "91 - 120 d."
msgstr "91 - 120 d."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "<b>Taxes summary</b>"
msgstr "<B>Resum d'impostos</b>"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid ""
"<i class=\"fa fa-exclamation-triangle mr-3\"/>\n"
"                    Duplicate amounts may be shown because more than one "
"analytical account may be defined in the journal items."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Intervals configuration</span>"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid "<span class=\"oe_inline\">To</span>"
msgstr "<span class=\"oe_inline\">A</span>"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_abstract_report
msgid "Abstract Report"
msgstr "Informe abstracte"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_financial_report_abstract_wizard
msgid "Abstract Wizard"
msgstr "Assistent d'extracte"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_abstract_report_xlsx
msgid "Abstract XLSX Account Financial Report"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model,name:account_financial_report.model_account_account
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Account"
msgstr "Compte"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__account_age_report_config_id
msgid "Account Age Report Config"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_code_from
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_code_from
msgid "Account Code From"
msgstr "Des de codi de compte"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_code_to
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_code_to
msgid "Account Code To"
msgstr "Des de codi de compte"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_group
msgid "Account Group"
msgstr "Grup de comptes"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Account Name"
msgstr "Compte"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Account at 0 filter"
msgstr "Filtrar per compte a 0"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
msgid "Account balance at 0 filter"
msgstr "Balan?? de compte en filtre 0"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__account_ids
msgid "Accounts"
msgstr "Comptes"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__centralize
msgid "Activate centralization"
msgstr "Activar centralitzaci??"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Additional Filtering"
msgstr "Filtrat addicional"

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.action_aged_partner_report_configuration
msgid "Age Partner Report Configuration"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 120\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 120 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 30\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 30 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 60\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 60 d."
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Age ≤ 90\n"
"                            d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Age ≤ 90 d."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_aged_partner_balance_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_aged_partner_balance_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_aged_partner_balance_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_aged_partner_balance_wizard
msgid "Aged Partner Balance"
msgstr "Saldo ven??uts d'empresa"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_base
msgid "Aged Partner Balance -"
msgstr "Balan?? empresa ven??ut"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_aged_partner_balance
msgid "Aged Partner Balance Report"
msgstr "Saldos ven??ut d'empreses"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_aged_partner_balance_report_wizard
msgid "Aged Partner Balance Wizard"
msgstr "Assistent del balan?? de ven??uts dels clients"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_aged_partner_balance_xlsx
msgid "Aged Partner Balance XLSL Report"
msgstr "Saldos ven??uts d'empreses XLSX"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_aged_partner_balance_xlsx
msgid "Aged Partner Balance XLSX"
msgstr "Saldo ven??uts d'empresa XLSX"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "All"
msgstr "Tots"

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__aged_partner_balance_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__open_items_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__target_move__all
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__target_move__all
msgid "All Entries"
msgstr "Tots els assentaments"

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__aged_partner_balance_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__open_items_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__target_move__posted
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__target_move__posted
msgid "All Posted Entries"
msgstr "Tots els assentaments assentats"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "All entries"
msgstr "Tots els assentaments"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "All posted entries"
msgstr "Tots els assentaments assentats"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Amount Cur."
msgstr "Import inicial."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Amount Currency"
msgstr "Moneda de l'import"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Amount cur."
msgstr "Import actual"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_move_line__analytic_account_ids
#: model:ir.model.fields.selection,name:account_financial_report.selection__trial_balance_report_wizard__grouped_by__analytic_account
msgid "Analytic Account"
msgstr "Compte anal??tic"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Analytic Distribution"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Balance"
msgstr "Saldo"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Base Amount"
msgstr "Import base"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Balance"
msgstr "Saldo base"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Credit"
msgstr "Haver base"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Base Debit"
msgstr "Deure base"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__based_on
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Based On"
msgstr "Basat en"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Based on"
msgstr "Basat en"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Cancel"
msgstr "Cancel??lar "

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "Centralize filter"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_account__centralized
msgid "Centralized"
msgstr "Centralitzat"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__group_child_ids
msgid "Child Groups"
msgstr "Grups fill"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Code"
msgstr "Codi"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_account_financial_report_abstract_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__company_id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__company_id
msgid "Company"
msgstr "Empresa "

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__compute_account_ids
msgid "Compute accounts"
msgstr "Calcula comptes"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "Configurations"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__create_uid
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__create_uid
msgid "Created by"
msgstr "Creat per "

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__create_date
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__create_date
msgid "Created on"
msgstr "Creat a "

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Credit"
msgstr "Haver"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Cumul cur."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Cumul. Bal."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur. Original"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Cur. Residual"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Currency"
msgstr "Moneda"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid "Current"
msgstr "Actual"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Date"
msgstr "Data"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__date_at
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__date_at
msgid "Date At"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__date_from
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_from
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Date From"
msgstr "Data inicial"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_to
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_to
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_filters
msgid "Date To"
msgstr "Data final"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
msgid "Date at filter"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Date from"
msgstr "Data inicial"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__date_range_id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_range_id
msgid "Date range"
msgstr "Rang de dates"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Date range filter"
msgstr "Filtre del rang de dates"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
msgid "Date to"
msgstr "Data final"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Debit"
msgstr "Deure"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Description"
msgstr "Descripci??"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__tax_detail
msgid "Detail Taxes"
msgstr "Detall d'impostos"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__display_name
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__display_name
msgid "Display Name"
msgstr "Nom a mostrar "

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__foreign_currency
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__foreign_currency
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__foreign_currency
msgid ""
"Display foreign currency for move lines, unless account currency is not "
"setup through chart of accounts will display initial and final balance in "
"that currency."
msgstr ""
"Mostra la moneda estrangera per a les l??nies d'assentaments, llevat que la "
"moneda del compte no estigui configurada a trav??s de la llista de comptes "
"mostrar?? el saldo inicial i final en aquesta moneda."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__hide_parent_hierarchy_level
msgid "Do not display parent levels"
msgstr "No mostrar nivells pare"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid ""
"Due\n"
"                        date"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#, fuzzy
msgid ""
"Due\n"
"                    date"
msgstr ""
"Antiguitat ??? 30\n"
"                        d."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
msgid "Due date"
msgstr "Venciment"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_to
msgid "End Date"
msgstr "Data final"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_to
msgid "End date"
msgstr "Data final"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_ending_cumul
msgid ""
"Ending\n"
"                        balance"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_aged_partner_balance_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__account_code_to
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__account_code_to
msgid "Ending account in a range"
msgstr "Darrer compte d'un rang"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Ending balance"
msgstr "Saldo final"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
#, fuzzy
msgid ""
"Ending balance\n"
"                        cur."
msgstr ""
"Antiguitat ??? 30\n"
"                        d."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Entries sorted by"
msgstr "Apunts ordenats per"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Entry"
msgstr "Apunt"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Entry number"
msgstr "N??mero d'apunt"

#. module: account_financial_report
#. odoo-javascript
#: code:addons/account_financial_report/static/src/xml/report.xml:0
msgid "Export"
msgstr "Exportar"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Export PDF"
msgstr "Exporta PDF"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "Export XLSX"
msgstr "Exportar XLSX"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__account_ids
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__account_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Filter accounts"
msgstr "Filtra comptes"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Filter analytic accounts"
msgstr "Filtra comptes anal??tics"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__cost_center_ids
msgid "Filter cost centers"
msgstr "Filtra centres de cost"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__account_journal_ids
msgid "Filter journals"
msgstr "Filtra diaris"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__partner_ids
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__partner_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid "Filter partners"
msgstr "Filtra partners"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__foreign_currency
msgid "Foreign Currency"
msgstr "Divisa estrangera"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid "From Code"
msgstr "Des de codi"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "From:"
msgstr "Des de:"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "From: %(date_from)s To: %(date_to)s"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__complete_code
msgid "Full Code"
msgstr "Codi complet"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__complete_name
msgid "Full Name"
msgstr "Nom complet"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__fy_start_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__fy_start_date
msgid "Fy Start Date"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.act_action_general_ledger_wizard_partner_relation
#: model:ir.actions.act_window,name:account_financial_report.action_general_ledger_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_general_ledger_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_general_ledger_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_general_ledger_wizard
msgid "General Ledger"
msgstr "Llibre major"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_base
msgid "General Ledger -"
msgstr "Llibre major -"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_general_ledger
msgid "General Ledger Report"
msgstr "Informe de llibre major"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_general_ledger_report_wizard
msgid "General Ledger Report Wizard"
msgstr "Assistent d'informe de llibre major"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_general_ledger_xlsx
msgid "General Ledger XLSL Report"
msgstr "Informe XLSX del llibre major"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_general_ledger_xlsx
msgid "General Ledger XLSX"
msgstr "Llibre major XLSX"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
msgid ""
"General Ledger can be computed only if selected company have\n"
"                        only one unaffected earnings account."
msgstr ""
"El llibre major nom??s pot calcular-se si la empresa seleccionada te\n"
"                        nom??s un compte de guanys no afectat."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__group_option
msgid "Group entries by"
msgstr "Agrupar apunts per"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__grouped_by
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__grouped_by
msgid "Grouped By"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid ""
"Here you can set the intervals that will appear on the Aged Partner Balance."
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Hide"
msgstr "Amaga"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__hide_account_at_0
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__hide_account_at_0
msgid "Hide account ending balance at 0"
msgstr "Amaga saldos finals a 0"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__hide_account_at_0
msgid "Hide accounts at 0"
msgstr "Amaga comptes a 0"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_hierarchy_level
msgid "Hierarchy Levels to display"
msgstr "Nivells de jerarquia a mostrar"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__id
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__id
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__id
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_account_account__centralized
msgid ""
"If flagged, no details will be displayed in the General Ledger report (the "
"webkit one only), only centralized amounts per period."
msgstr ""
"Si es marca, no es mostraran els detalls a l'informe del llibre major (nom??"
"s el webkit), nom??s imports centralitzats per per??ode."

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__inferior_limit
msgid "Inferior Limit"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/models/account_age_report_configuration.py:0
msgid "Inferior Limit must be greather than zero"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
#, fuzzy
msgid ""
"Initial\n"
"                        balance cur."
msgstr ""
"Antiguitat ??? 30\n"
"                        d."

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid ""
"Initial\n"
"                    balance"
msgstr ""
"Saldo\n"
"                    inicial"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Initial balance"
msgstr "Saldo inicial"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__age_partner_config_id
#: model:ir.model.fields,field_description:account_financial_report.field_res_config_settings__age_partner_config_id
msgid "Intervals configuration"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__journal_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Journal"
msgstr "Diari"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_move_line
msgid "Journal Item"
msgstr "Apunt comptable"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__domain
msgid "Journal Items Domain"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_journal_ledger_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_journal_ledger_wizard_html
#: model:ir.ui.menu,name:account_financial_report.menu_journal_ledger_wizard
msgid "Journal Ledger"
msgstr "Llibre diari"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_base
msgid "Journal Ledger -"
msgstr "Llibre diari -"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_journal_ledger
msgid "Journal Ledger Report"
msgstr "Informe del llibre diari"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_journal_ledger_report_wizard
msgid "Journal Ledger Report Wizard"
msgstr "Assistent d'informe de llibre diari"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_journal_ledger_xlsx
msgid "Journal Ledger XLSX"
msgstr "Llibre diari XLSX"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_journal_ledger_xlsx
msgid "Journal Ledger XLSX Report"
msgstr "Informe llibre diari XLSX"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__journal_ids
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Journals"
msgstr "Diaris"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__write_uid
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__write_uid
msgid "Last Updated by"
msgstr "??ltima actualitzaci?? per "

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__write_date
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__write_date
msgid "Last Updated on"
msgstr "??ltima actualitzaci?? a "

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_group__level
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Level"
msgstr "Nivell"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "Level %s"
msgstr "Nivell %s"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__limit_hierarchy_level
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Limit hierarchy levels"
msgstr "Limita nivells de jerarquia"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__line_ids
msgid "Line"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger.py:0
#: code:addons/account_financial_report/report/open_items.py:0
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid "Missing Partner"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_age_report_configuration_line
msgid "Model to set interval lines for Age partner balance report"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_account_age_report_configuration
msgid "Model to set intervals for Age partner balance report"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__move_target
msgid "Move Target"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal
msgid "Moves"
msgstr "Assentaments"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/models/account_age_report_configuration.py:0
msgid "Must complete Configuration Lines"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration__name
#: model:ir.model.fields,field_description:account_financial_report.field_account_age_report_configuration_line__name
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Name"
msgstr "Nom"

#. module: account_financial_report
#: model:ir.model.constraint,message:account_financial_report.constraint_account_age_report_configuration_line_unique_name_config_combination
msgid "Name must be unique per report configuration"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Net"
msgstr "Net"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "No"
msgstr "No"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "No group"
msgstr "Sense grup"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "No limit"
msgstr "Sense l??mit"

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__
msgid "None"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Not Posted"
msgstr "No confirmat"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
msgid "Not due"
msgstr "No ven??ut"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.res_config_settings_view_form
msgid "OCA Aged Report Configuration"
msgstr ""

#. module: account_financial_report
#: model:ir.ui.menu,name:account_financial_report.menu_oca_reports
msgid "OCA accounting reports"
msgstr "Informes comptables OCA"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
msgid "Older"
msgstr "M??s antic"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__only_one_unaffected_earnings_account
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__only_one_unaffected_earnings_account
msgid "Only One Unaffected Earnings Account"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_open_items_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_open_items_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_open_items_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_open_items_wizard
msgid "Open Items"
msgstr "Partides obertes"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_base
msgid "Open Items -"
msgstr "Partides obertes -"

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.act_action_open_items_wizard_partner_relation
msgid "Open Items Partner"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_open_items
msgid "Open Items Report"
msgstr "Informe de partides obertes"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_open_items_report_wizard
msgid "Open Items Report Wizard"
msgstr "Assistent d'informe de partides obertes"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_open_items_xlsx
msgid "Open Items XLSX"
msgstr "Partides obertes XLSX"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_open_items_xlsx
msgid "Open Items XLSX Report"
msgstr "Informe XLSX de partides obertes"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Options"
msgstr "Opcions"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Original"
msgstr "Original"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Partner"
msgstr "Partner"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_partner_ending_cumul
msgid ""
"Partner\n"
"                    cumul aged balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
msgid "Partner Initial balance"
msgstr "Saldo inicial partner"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
msgid "Partner cumul aged balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_ending_cumul
msgid "Partner ending balance"
msgstr "Saldo final partner"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Partner initial balance"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__partners
msgid "Partners"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__payable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__payable_accounts_only
msgid "Payable Accounts Only"
msgstr "Nom??s comptes a pagar"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_account_ending_cumul
msgid "Percents"
msgstr "Percentatges"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_lines_header
msgid "Period balance"
msgstr "Saldo del per??ode"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
msgid "Periods"
msgstr "Per??odes"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/journal_ledger_wizard.py:0
msgid "Posted"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Rec."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__receivable_accounts_only
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__receivable_accounts_only
msgid "Receivable Accounts Only"
msgstr "Nom??s comptes a cobrar"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid ""
"Ref -\n"
"                        Label"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
#, fuzzy
msgid ""
"Ref -\n"
"                    Label"
msgstr ""
"Antiguitat ??? 30\n"
"                        d."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Ref - Label"
msgstr ""

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_ir_actions_report
msgid "Report Action"
msgstr "Acci?? d'informe"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_lines_header
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_move_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_lines_header
msgid "Residual"
msgstr "Residual"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Sequence"
msgstr "Seq????ncia"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Show"
msgstr "Mostrar"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__show_cost_center
msgid "Show Analytic Account"
msgstr "Mostrar compte anal??tic"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__with_auto_sequence
msgid "Show Auto Sequence"
msgstr "Mostrar seq????ncia autom??tica"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__show_move_line_details
msgid "Show Move Line Details"
msgstr "Mostrar detalls d'apunts"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__show_partner_details
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_partner_details
msgid "Show Partner Details"
msgstr "Mostrar detalls de partner"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__foreign_currency
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__foreign_currency
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__foreign_currency
msgid "Show foreign currency"
msgstr "Mostrar divisa estrangera"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__show_hierarchy
msgid "Show hierarchy"
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__sort_option
msgid "Sort entries by"
msgstr "Ordenar assentaments per"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__date_from
msgid "Start Date"
msgstr "Data inicial"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__date_from
msgid "Start date"
msgstr "Data inicial"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_aged_partner_balance_report_wizard__account_code_from
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__account_code_from
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__account_code_from
msgid "Starting account in a range"
msgstr "Compte inicial del rang"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
msgid "TOTAL"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Tags"
msgstr "Etiquetes"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_aged_partner_balance_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_open_items_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__target_move
#: model:ir.model.fields,field_description:account_financial_report.field_vat_report_wizard__target_move
msgid "Target Moves"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/aged_partner_balance_xlsx.py:0
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_open_items_filters
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "Target moves filter"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "Tax"
msgstr "Impost"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_all_taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_taxes
msgid "Tax Amount"
msgstr "Total impost"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Balance"
msgstr "Saldo d'impostos"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Credit"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
msgid "Tax Debit"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__based_on__taxgroups
msgid "Tax Groups"
msgstr "Grups d'impostos"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
msgid "Tax Initial balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__vat_report_wizard__based_on__taxtags
msgid "Tax Tags"
msgstr "Etiquetes d'impostos"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_ending_cumul
msgid "Tax ending balance"
msgstr ""

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
msgid "Tax initial balance"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/journal_ledger_xlsx.py:0
#: model:ir.model.fields.selection,name:account_financial_report.selection__general_ledger_report_wizard__grouped_by__taxes
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_lines
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "Taxes"
msgstr "Impostos"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/general_ledger_wizard.py:0
msgid ""
"The Company in the General Ledger Report Wizard and in Date Range must be "
"the same."
msgstr ""
"La empresa de l'informe de llibre major i al rang de dates ha de ser la "
"mateixa."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/trial_balance_wizard.py:0
msgid ""
"The Company in the Trial Balance Report Wizard and in Date Range must be the "
"same."
msgstr ""
"La empresa al balan?? de sumes i saldos i al rang de dates ha de ser la "
"mateixa."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/vat_report_wizard.py:0
msgid ""
"The Company in the Vat Report Wizard and in Date Range must be the same."
msgstr ""
"La empresa a l'assistent de l'informe d'IVA i al rang de dates ha de ser la "
"mateixa."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/wizard/trial_balance_wizard.py:0
msgid "The hierarchy level to filter on must be greater than 0."
msgstr "El nivell de jerarquia pel filtre ha de ser m??s gran que 0."

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid ""
"There is a problem in the structure of the account groups. You may need to "
"create some child group of %s."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__domain
msgid "This domain will be used to select specific domain for Journal Items"
msgstr ""
"Aquest domini s'utilitzar?? per a seleccionar un domini espec??fic pels "
"apunts comptables"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_filters
msgid "To"
msgstr "Fins"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "To:"
msgstr "Fins:"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_aged_partner_balance_account_ending_cumul
msgid "Total"
msgstr "Total"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model:ir.actions.act_window,name:account_financial_report.action_trial_balance_wizard
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_html
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_trial_balance_wizard
msgid "Trial Balance"
msgstr "Balan?? de sumes i saldos"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_trial_balance_base
#, fuzzy
msgid "Trial Balance -"
msgstr "Cancel??lar "

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_trial_balance
#, fuzzy
msgid "Trial Balance Report"
msgstr "Cancel??lar "

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_trial_balance_report_wizard
msgid "Trial Balance Report Wizard"
msgstr "Assistent de l'informe de balan?? de sumes i saldos"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_trial_balance_xlsx
msgid "Trial Balance XLSX"
msgstr "Balan?? de sumes i saldos XLSX"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_trial_balance_xlsx
#, fuzzy
msgid "Trial Balance XLSX Report"
msgstr "Cancel??lar "

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
msgid ""
"Trial Balance can be computed only if selected company have only\n"
"                        one unaffected earnings account."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_general_ledger_report_wizard__unaffected_earnings_account
#: model:ir.model.fields,field_description:account_financial_report.field_trial_balance_report_wizard__unaffected_earnings_account
msgid "Unaffected Earnings Account"
msgstr "Compte de guanys no afectat"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_general_ledger_report_wizard__hide_account_at_0
#: model:ir.model.fields,help:account_financial_report.field_open_items_report_wizard__hide_account_at_0
msgid ""
"Use this filter to hide an account or a partner with an ending balance at 0. "
"If partners are filtered, debits and credits totals will not match the trial "
"balance."
msgstr ""

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__show_hierarchy
msgid "Use when your account groups are hierarchical"
msgstr ""

#. module: account_financial_report
#: model:ir.actions.act_window,name:account_financial_report.action_vat_report_wizard
#: model:ir.actions.report,name:account_financial_report.action_print_report_vat_report_html
#: model:ir.actions.report,name:account_financial_report.action_print_report_vat_report_qweb
#: model:ir.ui.menu,name:account_financial_report.menu_vat_report_wizard
msgid "VAT Report"
msgstr "Informe d'IVA"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_vat_report_base
msgid "VAT Report -"
msgstr "Informe d'IVA -"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "VAT Report Options"
msgstr "Opcions d'informe d'IVA"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_vat_report_wizard
msgid "VAT Report Wizard"
msgstr "Assistent d'informe d'IVA"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_report_vat_report_xlsx
msgid "VAT Report XLSX"
msgstr "Informe d'IVA XLSX"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/vat_report_xlsx.py:0
#, fuzzy
msgid "Vat Report"
msgstr "Informe extracte"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_account_financial_report_vat_report
#, fuzzy
msgid "Vat Report Report"
msgstr "Informe extracte"

#. module: account_financial_report
#: model:ir.model,name:account_financial_report.model_report_a_f_r_report_vat_report_xlsx
msgid "Vat Report XLSX Report"
msgstr "Informe d'IVA XLSX"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.aged_partner_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "View"
msgstr "Vista"

#. module: account_financial_report
#: model:ir.model.fields,help:account_financial_report.field_trial_balance_report_wizard__hide_account_at_0
msgid ""
"When this option is enabled, the trial balance will not display accounts "
"that have initial balance = debit = credit = end balance = 0"
msgstr ""
"Quan s'habilita aquesta opci??, el balan?? de sumes i saldos no mostrar?? "
"comptes que tinguin saldo inicial = deure = haver = saldo final = 0"

#. module: account_financial_report
#: model:ir.model.fields,field_description:account_financial_report.field_journal_ledger_report_wizard__with_account_name
msgid "With Account Name"
msgstr "Amb el nom de compte"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/trial_balance.py:0
msgid "Without analytic account"
msgstr ""

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger_xlsx.py:0
#: code:addons/account_financial_report/report/open_items_xlsx.py:0
#: code:addons/account_financial_report/report/trial_balance_xlsx.py:0
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_general_ledger_filters
msgid "Yes"
msgstr "S??"

#. module: account_financial_report
#. odoo-python
#: code:addons/account_financial_report/report/general_ledger.py:0
msgid "future"
msgstr "futur"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.general_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.journal_ledger_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.open_items_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.trial_balance_wizard
#: model_terms:ir.ui.view,arch_db:account_financial_report.vat_report_wizard
msgid "or"
msgstr "o"

#. module: account_financial_report
#: model:ir.actions.report,name:account_financial_report.action_print_journal_ledger_wizard_qweb
msgid "ournal Ledger"
msgstr "Llibre diari"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal
msgid "to"
msgstr "a"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 16.21%;"
msgstr "ample: 16.21%;"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 23.24%;"
msgstr "ample: 23.24%;"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 23.78%;"
msgstr "ample: 23.78%;"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 31.35%;"
msgstr "ample: 31.35%;"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 38.92%;"
msgstr "ample: 38.92%;"

#. module: account_financial_report
#: model_terms:ir.ui.view,arch_db:account_financial_report.report_journal_ledger_journal_table_header
msgid "width: 8.11%;"
msgstr "ample: 8.11%;"

#~ msgid ""
#~ "Age ??? 120\n"
#~ "                        d."
#~ msgstr ""
#~ "Antiguitat ??? 120\n"
#~ "                        d."

#, python-format
#~ msgid "Age ??? 120 d."
#~ msgstr "Antiguitat ??? 120 d."

#~ msgid ""
#~ "Age ??? 30\n"
#~ "                        d."
#~ msgstr ""
#~ "Antiguitat ??? 30\n"
#~ "                        d."

#, python-format
#~ msgid "Age ??? 30 d."
#~ msgstr "Antiguitat ??? 30 d."

#~ msgid ""
#~ "Age ??? 60\n"
#~ "                        d."
#~ msgstr ""
#~ "Antiguitat ??? 60\n"
#~ "                        d."

#, python-format
#~ msgid "Age ??? 60 d."
#~ msgstr "Antiguitat ??? 60 d."

#~ msgid ""
#~ "Age ??? 90\n"
#~ "                        d."
#~ msgstr ""
#~ "Antiguitat ??? 90\n"
#~ "                        d."

#, python-format
#~ msgid "Age ??? 90 d."
#~ msgstr "Antiguitat ??? 90 d."

#~ msgid "Last Modified on"
#~ msgstr "??ltima modificaci?? a "

#~ msgid "Filter analytic tags"
#~ msgstr "Filtra etiquetes anal??tiques"

#, python-format
#~ msgid "Print"
#~ msgstr "Imprimir"

#, python-format
#~ msgid "Show analytic tags"
#~ msgstr "Mostrar etiquetes anal??tiques"

#~ msgid "Child Accounts"
#~ msgstr "Comptes fill"

#~ msgid "Computed Accounts"
#~ msgstr "Comptes calculats"

#~ msgid "Hierarchy On"
#~ msgstr "Jerarquia en"

#~ msgid "No hierarchy"
#~ msgstr "Sense jerarquia"

#~ msgid "From: %s To: %s"
#~ msgstr "Des de: %s A: %s"

#~ msgid "<span class=\"fa fa-download\"/> Export"
#~ msgstr "<span class=\"fa fa-download\"/>Exportar"

#~ msgid "<span class=\"fa fa-print\"/> Print"
#~ msgstr "<span class=\"fa fa-print\"/>Imprimir"

#~ msgid "Account ID"
#~ msgstr "N?? Compte"

#~ msgid "Account Type"
#~ msgstr "Tipus de compte"

#~ msgid "Age 120 Days"
#~ msgstr "120 dies"

#~ msgid "Age 30 Days"
#~ msgstr "30 dies"

#~ msgid "Age 60 Days"
#~ msgstr "60 dies"

#~ msgid "Age 90 Days"
#~ msgstr "90 dies"

#~ msgid "Amount Residual"
#~ msgstr "Import residual"

#, fuzzy
#~ msgid "Company Currency"
#~ msgstr "Empresa "

#, fuzzy
#~ msgid "Period Balance"
#~ msgstr "Cancel??lar "

#, fuzzy
#~ msgid "Tax Name"
#~ msgstr "Nom a mostrar "
