<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!-- TRIAL BALANCE -->
    <record id="trial_balance_wizard" model="ir.ui.view">
        <field name="name">Trial Balance</field>
        <field name="model">trial.balance.report.wizard</field>
        <field name="arch" type="xml">
            <form>
                <div
                    class="alert alert-warning"
                    role="alert"
                    invisible="grouped_by!='analytic_account'"
                >
                    <i class="fa fa-exclamation-triangle mr-3" />
                    Duplicate amounts may be shown because more than one analytical account may be defined in the journal items.
                </div>
                <group name="main_info">
                    <field
                        name="company_id"
                        options="{'no_create': True}"
                        groups="base.group_multi_company"
                    />
                </group>
                <div invisible="not only_one_unaffected_earnings_account">
                    <group name="filters">
                        <group name="date_range">
                            <field name="date_range_id" />
                            <field name="date_from" />
                            <field name="date_to" />
                            <field name="fy_start_date" invisible="1" />
                        </group>
                        <group name="other_filters">
                            <field name="target_move" widget="radio" />
                            <field name="grouped_by" invisible="1" />
                            <field
                                name="grouped_by"
                                groups="analytic.group_analytic_accounting"
                            />
                            <field name="hide_account_at_0" />
                            <field name="show_partner_details" invisible="grouped_by" />
                            <field
                                name="show_hierarchy"
                                invisible="show_partner_details == True or grouped_by"
                            />
                            <field
                                name="limit_hierarchy_level"
                                invisible="show_hierarchy == False or show_partner_details == True"
                            />
                            <field
                                name="show_hierarchy_level"
                                invisible="limit_hierarchy_level == False"
                            />
                            <field
                                name="hide_parent_hierarchy_level"
                                invisible="limit_hierarchy_level == False"
                            />
                            <field name="foreign_currency" />
                        </group>
                    </group>
                    <group
                        name="partner_filter"
                        invisible="show_partner_details == True"
                        col="1"
                    >
                        <label for="partner_ids" />
                        <field
                            name="partner_ids"
                            nolabel="1"
                            widget="many2many_tags"
                            options="{'no_create': True}"
                        />
                    </group>
                    <label for="journal_ids" />
                    <field
                        name="journal_ids"
                        widget="many2many_tags"
                        nolabel="1"
                        options="{'no_create': True}"
                    />
                    <group invisible="show_partner_details == True" />
                    <div />
                    <group name="account_filter" col="4">
                        <label for="account_ids" colspan="4" />
                        <field name="receivable_accounts_only" />
                        <field name="payable_accounts_only" />
                        <label for="account_code_from" string="From Code" />
                        <div>
                            <div class="o_row">
                                <field
                                    name="account_code_from"
                                    class="oe_inline"
                                    options="{'no_create': True}"
                                />
                                <span class="oe_inline">To</span>
                                <field
                                    name="account_code_to"
                                    class="oe_inline"
                                    options="{'no_create': True}"
                                />
                            </div>
                        </div>
                        <field
                            name="account_ids"
                            nolabel="1"
                            widget="many2many_tags"
                            options="{'no_create': True}"
                            colspan="4"
                        />
                    </group>
                </div>
                <div invisible="only_one_unaffected_earnings_account">
                    <field name="only_one_unaffected_earnings_account" invisible="1" />
                    <group />
                    <h4>
                        Trial Balance can be computed only if selected company have only
                        one unaffected earnings account.
                    </h4>
                    <group />
                </div>
                <footer>
                    <div invisible="not only_one_unaffected_earnings_account">
                        <button
                        name="button_export_html"
                        string="View"
                        type="object"
                        default_focus="1"
                        class="oe_highlight"
                    />
                        or
                        <button
                        name="button_export_pdf"
                        string="Export PDF"
                        type="object"
                    />
                        or
                        <button
                        name="button_export_xlsx"
                        string="Export XLSX"
                        type="object"
                    />
                        or
                        <button string="Cancel" class="oe_link" special="cancel" />
                    </div>
                    <div invisible="only_one_unaffected_earnings_account">
                        <button string="Cancel" class="oe_link" special="cancel" />
                    </div>
                </footer>
            </form>
        </field>
    </record>
    <record id="action_trial_balance_wizard" model="ir.actions.act_window">
        <field name="name">Trial Balance</field>
        <field name="res_model">trial.balance.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="trial_balance_wizard" />
        <field name="target">new</field>
    </record>
</odoo>
