/* Login Form Styles - Professional ERP Authentication */

.login-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 1;
}

.login-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  animation: backgroundAnimation 20s ease-in-out infinite;
}

@keyframes backgroundAnimation {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(1px);
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  border: none;
  overflow: hidden;
  animation: cardSlideIn 0.6s ease-out;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-card .ant-card-body {
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header .ant-avatar {
  box-shadow: 0 8px 16px rgba(24, 144, 255, 0.3);
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

.login-header h2 {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-top: 16px !important;
}

.login-form {
  margin-top: 24px;
}

.login-form .ant-form-item-label > label {
  font-weight: 600;
  color: #262626;
}

.login-form .ant-input-affix-wrapper,
.login-form .ant-input {
  border-radius: 8px;
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.login-form .ant-input-affix-wrapper:hover,
.login-form .ant-input:hover {
  border-color: #1890ff;
  background: rgba(255, 255, 255, 0.95);
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused,
.login-form .ant-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
  background: rgba(255, 255, 255, 1);
}

.login-form .ant-select .ant-select-selector {
  border-radius: 8px;
  border: 2px solid #f0f0f0;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.login-form .ant-select:hover .ant-select-selector {
  border-color: #1890ff;
  background: rgba(255, 255, 255, 0.95);
}

.login-form .ant-select-focused .ant-select-selector {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1) !important;
  background: rgba(255, 255, 255, 1);
}

.login-button {
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.login-button:hover {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

.demo-users {
  margin-top: 16px;
}

.demo-user-card {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.demo-user-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.demo-user-card .ant-card-body {
  padding: 12px 16px;
}

.demo-user-card .ant-badge {
  margin-right: 8px;
}

.demo-user-card .ant-badge-count {
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  min-width: 16px;
  padding: 0 4px;
  border-radius: 8px;
}

.login-footer {
  margin-top: 24px;
  text-align: center;
}

.login-footer .ant-btn-link {
  color: #8c8c8c;
  font-size: 12px;
}

.login-footer .ant-btn-link:hover {
  color: #1890ff;
}

/* Alert Styles */
.ant-alert {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-alert-error {
  background: rgba(255, 77, 79, 0.1);
  border-left: 4px solid #ff4d4f;
}

/* Checkbox Styles */
.ant-checkbox-wrapper {
  color: #595959;
  font-weight: 500;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #1890ff;
  border-color: #1890ff;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .login-card .ant-card-body {
    padding: 24px;
  }
  
  .login-header h2 {
    font-size: 24px;
  }
  
  .demo-user-card .ant-card-body {
    padding: 8px 12px;
  }
  
  .demo-users .ant-col {
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .login-content {
    padding: 16px;
  }
  
  .login-card .ant-card-body {
    padding: 20px;
  }
  
  .login-header .ant-avatar {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
  
  .login-header h2 {
    font-size: 20px;
  }
  
  .login-button {
    height: 44px;
    font-size: 14px;
  }
}

/* Loading States */
.ant-btn-loading {
  pointer-events: none;
}

.ant-btn-loading .ant-btn-loading-icon {
  margin-right: 8px;
}

/* Focus Styles for Accessibility */
.login-form .ant-input:focus,
.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-select-focused .ant-select-selector {
  outline: 2px solid rgba(24, 144, 255, 0.2);
  outline-offset: 2px;
}

/* Animation for form validation errors */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input-affix-wrapper,
.ant-form-item-has-error .ant-select .ant-select-selector {
  border-color: #ff4d4f;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Success states */
.ant-form-item-has-success .ant-input,
.ant-form-item-has-success .ant-input-affix-wrapper {
  border-color: #52c41a;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(20, 20, 20, 0.95);
    color: #fff;
  }
  
  .login-form .ant-input-affix-wrapper,
  .login-form .ant-input {
    background: rgba(255, 255, 255, 0.1);
    border-color: #434343;
    color: #fff;
  }
  
  .login-form .ant-input-affix-wrapper:hover,
  .login-form .ant-input:hover {
    background: rgba(255, 255, 255, 0.15);
  }
  
  .demo-user-card {
    background: rgba(255, 255, 255, 0.1);
    border-color: #434343;
  }
  
  .demo-user-card:hover {
    background: rgba(255, 255, 255, 0.15);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .login-card {
    border: 2px solid #000;
  }
  
  .login-form .ant-input,
  .login-form .ant-input-affix-wrapper {
    border: 2px solid #000;
  }
  
  .login-button {
    background: #000;
    border: 2px solid #000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .login-card,
  .login-header .ant-avatar,
  .demo-user-card,
  .login-button {
    animation: none;
    transition: none;
  }
  
  .login-background::before {
    animation: none;
  }
}
