# Generated by Django 4.2.21 on 2025-07-15 20:28

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0003_delete_ircron'),
        ('inventory', '0002_alter_stocklocation_unique_together'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PosConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('currency_id', models.CharField(default='PKR', help_text='Currency', max_length=3)),
                ('picking_type_id', models.CharField(choices=[('incoming', 'Receipt'), ('outgoing', 'Delivery'), ('internal', 'Internal Transfer')], default='outgoing', help_text='Operation Type', max_length=16)),
                ('iface_cashdrawer', models.BooleanField(default=False, help_text='Cashdrawer')),
                ('iface_electronic_scale', models.BooleanField(default=False, help_text='Electronic Scale')),
                ('iface_customer_facing_display', models.BooleanField(default=False, help_text='Customer Facing Display')),
                ('iface_print_via_proxy', models.BooleanField(default=False, help_text='Print via Proxy')),
                ('receipt_header', models.TextField(blank=True, help_text='Receipt Header')),
                ('receipt_footer', models.TextField(blank=True, help_text='Receipt Footer')),
                ('cash_control', models.BooleanField(default=False, help_text='Cash Control')),
                ('sequence_id', models.IntegerField(blank=True, help_text='Order IDs Sequence', null=True)),
                ('sequence_line_id', models.IntegerField(blank=True, help_text='Order Line IDs Sequence', null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('invoice_journal_id', models.ForeignKey(help_text='Invoice Journal', on_delete=django.db.models.deletion.CASCADE, related_name='pos_invoice_journal', to='accounting.accountjournal')),
                ('journal_id', models.ForeignKey(help_text='Sales Journal', on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
                ('stock_location_id', models.ForeignKey(help_text='Stock Location', on_delete=django.db.models.deletion.CASCADE, to='inventory.stocklocation')),
            ],
            options={
                'db_table': 'pos_config',
            },
        ),
        migrations.CreateModel(
            name='PosOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Order Ref', max_length=64)),
                ('date_order', models.DateTimeField(default=datetime.datetime.now, help_text='Order Date')),
                ('amount_total', models.DecimalField(decimal_places=2, default=0, help_text='Total', max_digits=16)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0, help_text='Taxes', max_digits=16)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=0, help_text='Paid', max_digits=16)),
                ('amount_return', models.DecimalField(decimal_places=2, default=0, help_text='Returned', max_digits=16)),
                ('state', models.CharField(choices=[('draft', 'New'), ('cancel', 'Cancelled'), ('paid', 'Paid'), ('done', 'Posted'), ('invoiced', 'Invoiced')], default='draft', max_length=16)),
                ('fiscal_position_id', models.IntegerField(blank=True, help_text='Fiscal Position', null=True)),
                ('sequence_number', models.IntegerField(help_text='Sequence Number')),
                ('pos_reference', models.CharField(help_text='Receipt Number', max_length=64)),
                ('note', models.TextField(blank=True, help_text='Internal Notes')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_move', models.ForeignKey(blank=True, help_text='Journal Entry', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('config_id', models.ForeignKey(help_text='Point of Sale', on_delete=django.db.models.deletion.CASCADE, to='pos.posconfig')),
                ('partner_id', models.ForeignKey(blank=True, help_text='Customer', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('picking_id', models.ForeignKey(blank=True, help_text='Picking', null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.stockpicking')),
            ],
            options={
                'db_table': 'pos_order',
            },
        ),
        migrations.CreateModel(
            name='PosSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Session ID', max_length=64)),
                ('state', models.CharField(choices=[('opening_control', 'Opening Control'), ('opened', 'In Progress'), ('closing_control', 'Closing Control'), ('closed', 'Closed & Posted')], default='opening_control', max_length=16)),
                ('start_at', models.DateTimeField(help_text='Opening Date')),
                ('stop_at', models.DateTimeField(blank=True, help_text='Closing Date', null=True)),
                ('cash_register_balance_start', models.DecimalField(decimal_places=2, default=0, help_text='Starting Balance', max_digits=16)),
                ('cash_register_balance_end_real', models.DecimalField(decimal_places=2, default=0, help_text='Ending Balance', max_digits=16)),
                ('cash_register_balance_end', models.DecimalField(decimal_places=2, default=0, help_text='Theoretical Closing Balance', max_digits=16)),
                ('cash_register_difference', models.DecimalField(decimal_places=2, default=0, help_text='Difference', max_digits=16)),
                ('sequence_number', models.IntegerField(default=1, help_text='Order Sequence Number')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('config_id', models.ForeignKey(help_text='Point of Sale', on_delete=django.db.models.deletion.CASCADE, to='pos.posconfig')),
                ('move_id', models.ForeignKey(blank=True, help_text='Journal Entry', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove')),
                ('user_id', models.ForeignKey(help_text='Responsible', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'pos_session',
            },
        ),
        migrations.CreateModel(
            name='PosPaymentMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('is_cash_count', models.BooleanField(default=False, help_text='Cash')),
                ('use_payment_terminal', models.CharField(blank=True, help_text='Use a Payment Terminal', max_length=32)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(help_text='Journal', on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
                ('outstanding_account_id', models.ForeignKey(help_text='Outstanding Account', on_delete=django.db.models.deletion.CASCADE, to='accounting.accountaccount')),
            ],
            options={
                'db_table': 'pos_payment_method',
            },
        ),
        migrations.CreateModel(
            name='PosPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, default=0, help_text='Amount', max_digits=16)),
                ('payment_date', models.DateTimeField(default=datetime.datetime.now, help_text='Payment Date')),
                ('card_type', models.CharField(blank=True, help_text='Card Type', max_length=32)),
                ('cardholder_name', models.CharField(blank=True, help_text='Cardholder Name', max_length=64)),
                ('transaction_id', models.CharField(blank=True, help_text='Transaction ID', max_length=64)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('payment_method_id', models.ForeignKey(help_text='Payment Method', on_delete=django.db.models.deletion.CASCADE, to='pos.pospaymentmethod')),
                ('pos_order_id', models.ForeignKey(help_text='Order', on_delete=django.db.models.deletion.CASCADE, related_name='payment_ids', to='pos.posorder')),
                ('session_id', models.ForeignKey(help_text='Session', on_delete=django.db.models.deletion.CASCADE, to='pos.possession')),
            ],
            options={
                'db_table': 'pos_payment',
            },
        ),
        migrations.CreateModel(
            name='PosOrderLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qty', models.DecimalField(decimal_places=4, default=1, help_text='Quantity', max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=2, default=0, help_text='Unit Price', max_digits=16)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0, help_text='Subtotal w/o Tax', max_digits=16)),
                ('price_subtotal_incl', models.DecimalField(decimal_places=2, default=0, help_text='Subtotal', max_digits=16)),
                ('discount', models.DecimalField(decimal_places=2, default=0, help_text='Discount (%)', max_digits=5)),
                ('full_product_name', models.CharField(help_text='Full Product Name', max_length=256)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('order_id', models.ForeignKey(help_text='Order Ref', on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='pos.posorder')),
                ('product_id', models.ForeignKey(help_text='Product', on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
                ('tax_ids', models.ManyToManyField(blank=True, help_text='Taxes', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'pos_order_line',
            },
        ),
        migrations.AddField(
            model_name='posorder',
            name='session_id',
            field=models.ForeignKey(help_text='Session', on_delete=django.db.models.deletion.CASCADE, related_name='order_ids', to='pos.possession'),
        ),
        migrations.AddField(
            model_name='posorder',
            name='user_id',
            field=models.ForeignKey(help_text='Salesperson', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='PosMakePayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, default=0, help_text='Payment Amount', max_digits=16)),
                ('payment_name', models.CharField(blank=True, help_text='Payment Reference', max_length=64)),
                ('payment_date', models.DateTimeField(default=datetime.datetime.now, help_text='Payment Date')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('payment_method_id', models.ForeignKey(help_text='Payment Method', on_delete=django.db.models.deletion.CASCADE, to='pos.pospaymentmethod')),
                ('session_id', models.ForeignKey(help_text='Session', on_delete=django.db.models.deletion.CASCADE, to='pos.possession')),
            ],
            options={
                'db_table': 'pos_make_payment',
            },
        ),
        migrations.CreateModel(
            name='PosDetails',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_start', models.DateField(help_text='Start Date')),
                ('date_stop', models.DateField(help_text='End Date')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('pos_config_ids', models.ManyToManyField(help_text='Point of Sale', to='pos.posconfig')),
            ],
            options={
                'db_table': 'pos_details',
            },
        ),
        migrations.CreateModel(
            name='PosCloseSessionWizard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cash_register_balance_end_real', models.DecimalField(decimal_places=2, default=0, help_text='Ending Balance', max_digits=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('session_id', models.ForeignKey(help_text='Session', on_delete=django.db.models.deletion.CASCADE, to='pos.possession')),
            ],
            options={
                'db_table': 'pos_close_session_wizard',
            },
        ),
        migrations.CreateModel(
            name='PosDailyReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=datetime.date.today, help_text='Date')),
                ('total_sales', models.DecimalField(decimal_places=2, default=0, help_text='Total Sales', max_digits=16)),
                ('total_tax', models.DecimalField(decimal_places=2, default=0, help_text='Total Tax', max_digits=16)),
                ('total_discount', models.DecimalField(decimal_places=2, default=0, help_text='Total Discount', max_digits=16)),
                ('total_orders', models.IntegerField(default=0, help_text='Total Orders')),
                ('cash_total', models.DecimalField(decimal_places=2, default=0, help_text='Cash Total', max_digits=16)),
                ('card_total', models.DecimalField(decimal_places=2, default=0, help_text='Card Total', max_digits=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('pos_config_id', models.ForeignKey(help_text='Point of Sale', on_delete=django.db.models.deletion.CASCADE, to='pos.posconfig')),
            ],
            options={
                'db_table': 'pos_daily_report',
                'unique_together': {('date', 'pos_config_id')},
            },
        ),
    ]
