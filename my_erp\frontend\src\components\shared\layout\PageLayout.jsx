/**
 * Page Layout Components - Consistent page structure across the app
 */
import React from 'react';
import { Card, Typography, Space, Button, Breadcrumb, Divider, Row, Col } from 'antd';
import { ArrowLeftOutlined, PlusOutlined, EditOutlined, DeleteOutlined, ExportOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

// Page Header Component
export const PageHeader = ({
  title,
  subtitle,
  breadcrumbs = [],
  actions = [],
  showBack = false,
  onBack,
  extra,
  className = '',
  style = {}
}) => {
  const navigate = useNavigate();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate(-1);
    }
  };

  return (
    <div className={`page-header ${className}`} style={{ marginBottom: '24px', ...style }}>
      {breadcrumbs.length > 0 && (
        <Breadcrumb style={{ marginBottom: '16px' }}>
          {breadcrumbs.map((crumb, index) => (
            <Breadcrumb.Item key={index} href={crumb.href}>
              {crumb.title}
            </Breadcrumb.Item>
          ))}
        </Breadcrumb>
      )}
      
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
            {showBack && (
              <Button
                type="text"
                icon={<ArrowLeftOutlined />}
                onClick={handleBack}
                style={{ marginRight: '12px' }}
              />
            )}
            <Title level={2} style={{ margin: 0 }}>
              {title}
            </Title>
          </div>
          
          {subtitle && (
            <Text type="secondary" style={{ fontSize: '16px' }}>
              {subtitle}
            </Text>
          )}
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {actions.map((action, index) => (
            <Button
              key={index}
              type={action.type || 'default'}
              icon={action.icon}
              onClick={action.onClick}
              loading={action.loading}
              disabled={action.disabled}
              danger={action.danger}
            >
              {action.label}
            </Button>
          ))}
          {extra}
        </div>
      </div>
    </div>
  );
};

// Page Content Container
export const PageContent = ({
  children,
  loading = false,
  className = '',
  style = {},
  noPadding = false
}) => {
  return (
    <div 
      className={`page-content ${className}`} 
      style={{ 
        padding: noPadding ? 0 : '24px',
        minHeight: 'calc(100vh - 200px)',
        ...style 
      }}
    >
      {children}
    </div>
  );
};

// Action Button Group
export const ActionButtons = ({
  buttons = [],
  align = 'right',
  size = 'default',
  className = '',
  style = {}
}) => {
  return (
    <div 
      className={`action-buttons ${className}`}
      style={{ 
        textAlign: align, 
        marginTop: '16px',
        ...style 
      }}
    >
      <Space size="middle">
        {buttons.map((button, index) => (
          <Button
            key={index}
            type={button.type || 'default'}
            size={size}
            icon={button.icon}
            onClick={button.onClick}
            loading={button.loading}
            disabled={button.disabled}
            danger={button.danger}
            ghost={button.ghost}
          >
            {button.label}
          </Button>
        ))}
      </Space>
    </div>
  );
};

// Stats Card Component
export const StatsCard = ({
  title,
  value,
  prefix,
  suffix,
  trend,
  trendType = 'up', // 'up', 'down', 'neutral'
  icon,
  color = '#1890ff',
  loading = false,
  className = '',
  style = {}
}) => {
  const getTrendColor = () => {
    switch (trendType) {
      case 'up': return '#52c41a';
      case 'down': return '#ff4d4f';
      default: return '#faad14';
    }
  };

  return (
    <Card 
      className={`stats-card ${className}`}
      style={{ 
        textAlign: 'center',
        ...style 
      }}
      loading={loading}
    >
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', marginBottom: '12px' }}>
        {icon && (
          <div style={{ 
            fontSize: '24px', 
            color: color, 
            marginRight: '12px' 
          }}>
            {icon}
          </div>
        )}
        <div>
          <div style={{ fontSize: '24px', fontWeight: 'bold', color: color }}>
            {prefix}{value}{suffix}
          </div>
          <div style={{ fontSize: '14px', color: '#666' }}>
            {title}
          </div>
        </div>
      </div>
      
      {trend && (
        <div style={{ 
          fontSize: '12px', 
          color: getTrendColor(),
          marginTop: '8px'
        }}>
          {trend}
        </div>
      )}
    </Card>
  );
};

// Section Card Component
export const SectionCard = ({
  title,
  subtitle,
  children,
  actions = [],
  collapsible = false,
  defaultCollapsed = false,
  className = '',
  style = {},
  bodyStyle = {},
  ...cardProps
}) => {
  return (
    <Card
      title={title}
      extra={
        actions.length > 0 && (
          <Space>
            {actions.map((action, index) => (
              <Button
                key={index}
                type={action.type || 'text'}
                size="small"
                icon={action.icon}
                onClick={action.onClick}
                loading={action.loading}
                disabled={action.disabled}
              >
                {action.label}
              </Button>
            ))}
          </Space>
        )
      }
      className={`section-card ${className}`}
      style={{
        marginBottom: '24px',
        ...style
      }}
      bodyStyle={bodyStyle}
      {...cardProps}
    >
      {subtitle && (
        <>
          <Text type="secondary">{subtitle}</Text>
          <Divider style={{ margin: '16px 0' }} />
        </>
      )}
      {children}
    </Card>
  );
};

// Quick Action Buttons
export const QuickActions = {
  Create: ({ onClick, loading, disabled, children = 'Create' }) => (
    <Button
      type="primary"
      icon={<PlusOutlined />}
      onClick={onClick}
      loading={loading}
      disabled={disabled}
    >
      {children}
    </Button>
  ),
  
  Edit: ({ onClick, loading, disabled, children = 'Edit' }) => (
    <Button
      icon={<EditOutlined />}
      onClick={onClick}
      loading={loading}
      disabled={disabled}
    >
      {children}
    </Button>
  ),
  
  Delete: ({ onClick, loading, disabled, children = 'Delete' }) => (
    <Button
      danger
      icon={<DeleteOutlined />}
      onClick={onClick}
      loading={loading}
      disabled={disabled}
    >
      {children}
    </Button>
  ),
  
  Export: ({ onClick, loading, disabled, children = 'Export' }) => (
    <Button
      icon={<ExportOutlined />}
      onClick={onClick}
      loading={loading}
      disabled={disabled}
    >
      {children}
    </Button>
  )
};

// Grid Layout Helper
export const GridLayout = ({
  children,
  cols = 2,
  gutter = [16, 16],
  className = '',
  style = {}
}) => {
  const span = 24 / cols;
  
  return (
    <Row gutter={gutter} className={className} style={style}>
      {React.Children.map(children, (child, index) => (
        <Col span={span} key={index}>
          {child}
        </Col>
      ))}
    </Row>
  );
};
