"""
Simple API Views for Accounting Module - Django Compatible
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta, date
from decimal import Decimal

from .models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal
)
from .serializers import (
    CompanySerializer, PartnerSerializer, AccountSerializer,
    JournalSerializer
)


class CompanyViewSet(viewsets.ModelViewSet):
    """Company management - Based on Odoo res.company"""
    queryset = ResCompany.objects.all()
    serializer_class = CompanySerializer
    filterset_fields = ['currency_id']
    search_fields = ['name', 'vat']


class PartnerViewSet(viewsets.ModelViewSet):
    """Partner (Customer/Vendor) management - Based on Odoo res.partner"""
    queryset = ResPartner.objects.all()
    serializer_class = PartnerSerializer
    filterset_fields = ['is_company', 'customer_rank', 'supplier_rank', 'active', 'company_id']
    search_fields = ['name', 'email', 'vat', 'ref']
    ordering_fields = ['name', 'customer_rank', 'supplier_rank', 'create_date']

    @action(detail=False)
    def customers(self, request):
        """Get customers only"""
        customers = self.queryset.filter(customer_rank__gt=0)
        serializer = self.get_serializer(customers, many=True)
        return Response(serializer.data)

    @action(detail=False)
    def vendors(self, request):
        """Get vendors only"""
        vendors = self.queryset.filter(supplier_rank__gt=0)
        serializer = self.get_serializer(vendors, many=True)
        return Response(serializer.data)


class AccountViewSet(viewsets.ModelViewSet):
    """Chart of Accounts - Based on Odoo account.account"""
    queryset = AccountAccount.objects.all()
    serializer_class = AccountSerializer
    filterset_fields = ['account_type', 'deprecated', 'reconcile', 'company_id']
    search_fields = ['code', 'name']
    ordering_fields = ['code', 'name']

    @action(detail=False)
    def receivable(self, request):
        """Get receivable accounts"""
        accounts = self.queryset.filter(account_type='asset_receivable')
        serializer = self.get_serializer(accounts, many=True)
        return Response(serializer.data)

    @action(detail=False)
    def payable(self, request):
        """Get payable accounts"""
        accounts = self.queryset.filter(account_type='liability_payable')
        serializer = self.get_serializer(accounts, many=True)
        return Response(serializer.data)


class JournalViewSet(viewsets.ModelViewSet):
    """Journal management - Based on Odoo account.journal"""
    queryset = AccountJournal.objects.all()
    serializer_class = JournalSerializer
    filterset_fields = ['type', 'active', 'company_id']
    search_fields = ['name', 'code']
    ordering_fields = ['sequence', 'type', 'code']
